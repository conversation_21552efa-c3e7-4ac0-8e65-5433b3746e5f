@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 现代化按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.btn-soft-primary:hover,
.btn-soft-primary:focus,
.btn-soft-primary:active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
    box-shadow: none;
}

.btn-soft-secondary {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.btn-soft-secondary:hover,
.btn-soft-secondary:focus,
.btn-soft-secondary:active {
    background-color: #6c757d;
    color: #fff;
    border-color: #6c757d;
    box-shadow: none;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #e3e6f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.input-group-text {
    border-radius: 8px;
    border: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* 用户信息卡片 */
.user-info-card {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.membership-badge {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* 页面标题 */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 容器和布局优化 */
.container {
    max-width: 1200px;
}

/* 防止内容溢出 */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col-xl-4, .col-lg-5, .col-md-6,
.col-xl-8, .col-lg-7 {
    padding-left: 15px;
    padding-right: 15px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .user-info-card {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-flex.justify-content-between .btn {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .user-info-card {
        padding: 1.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="container py-4 fade-in">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">个人资料</h1>
        <p class="page-subtitle">管理您的账户信息和会员状态</p>
    </div>

    <div class="row">
        <!-- 左侧用户信息卡片 -->
        <div class="col-lg-4 col-md-12 mb-4">
            <div class="user-info-card">
                <div class="user-avatar">
                    <i class="bi bi-person-fill"></i>
                </div>
                <h4 class="mb-2">{{ $user->username }}</h4>
                <p class="mb-3 opacity-75">{{ $user->email }}</p>
                <div class="membership-badge">
                    <i class="bi bi-clock me-1"></i>
                    剩余 {{ $user->formatted_remaining_time }}
                </div>
                <div class="mt-3 small opacity-75">
                    <i class="bi bi-calendar me-1"></i>
                    注册时间：{{ $user->created_at->format('Y年m月d日') }}
                </div>
            </div>
        </div>

        <!-- 右侧表单区域 -->
        <div class="col-lg-8 col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-gear me-2"></i>基本信息设置
                    </h5>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('renewal_success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>{{ session('renewal_success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('profile.update') }}">
                        @csrf
                        @method('PATCH')

                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control @error('username') is-invalid @enderror"
                                id="username" name="username" value="{{ old('username', $user->username) }}" required>
                            @error('username')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱</label>
                            <div class="input-group">
                                <input type="email" class="form-control bg-light" id="email"
                                    value="{{ $user->email }}" readonly>
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="current_password" class="form-label">当前密码</label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror"
                                id="current_password" name="current_password">
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">新密码</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror"
                                id="password" name="password">
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">确认新密码</label>
                            <input type="password" class="form-control"
                                id="password_confirmation" name="password_confirmation">
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i> 返回仪表盘
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card" id="renewal-section">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">会员续费</h5>
                    <span class="badge bg-danger">剩余{{ $user->formatted_remaining_time }}</span>
                </div>

                <div class="card-body">
                    @if (session('renewal_error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('renewal_error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('profile.renew') }}" class="mb-0">
                        @csrf
                        <div class="mb-3">
                            <label for="activation_code" class="form-label">激活码</label>
                            <input type="text" class="form-control @error('activation_code') is-invalid @enderror"
                                id="activation_code" name="activation_code" placeholder="请输入有效的激活码"
                                value="{{ old('activation_code') }}" required>
                            @error('activation_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-clock-history me-1"></i> 立即续费
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 联系客服卡片 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-headset me-2"></i>需要帮助？
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-3">遇到问题或需要技术支持？我们的客服团队随时为您服务</p>
                    <a href="https://t.me/Kax0rs" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-telegram me-2"></i>联系客服
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 如果需要滚动到续费区域
    @if(session('scroll_to_renewal') || $errors->has('activation_code'))
        const renewalSection = document.getElementById('renewal-section');
        if (renewalSection) {
            // 计算目标滚动位置
            const offset = 50; // 额外的偏移量，确保完全显示
            const targetPosition = renewalSection.getBoundingClientRect().top + window.pageYOffset - offset;
            
            // 平滑滚动到目标位置
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            // 延迟聚焦，等待滚动完成
            setTimeout(() => {
                const activationCodeInput = document.getElementById('activation_code');
                if (activationCodeInput) {
                    activationCodeInput.focus();
                    activationCodeInput.select(); // 选中已有的文本
                }
            }, 500);
        }
    @endif
});
</script>
@endpush

@endsection 