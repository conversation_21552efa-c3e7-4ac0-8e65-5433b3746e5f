<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 10;
        
        $users = User::withCount([
                'awsAccounts',
                'awsAccounts as normal_accounts_count' => function($query) {
                    $query->where('status', 1); // 正常状态
                },
                'awsAccounts as untested_accounts_count' => function($query) {
                    $query->where('status', 0); // 未测试状态
                },
                'awsAccounts as new_accounts_count' => function($query) {
                    $query->whereDate('created_at', today()); // 今天新增的账户数量
                }
            ])
            ->when($request->filled('search'), function($query) use ($request) {
                $query->where('username', 'like', '%'.$request->search.'%')
                    ->orWhere('email', 'like', '%'.$request->search.'%');
            })
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return view('admin.users.index', compact('users', 'perPage'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'expires_at' => 'required|date',
        ]);

        User::create([
            'username' => $request->username,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'expires_at' => $request->expires_at,
        ]);

        return redirect()
            ->route('admin.users.index')
            ->with('success', '会员创建成功');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'username' => 'required|string|max:255|unique:users,username,'.$user->id,
            'email' => 'required|string|email|max:255|unique:users,email,'.$user->id,
            'password' => 'nullable|string|min:6',
            'expires_at' => 'required|date',
        ]);

        $data = $request->only(['username', 'email', 'expires_at']);
        if ($request->filled('password')) {
            $data['password'] = bcrypt($request->password);
        }

        $user->update($data);

        return redirect()
            ->route('admin.users.index')
            ->with('success', '会员信息更新成功');
    }

    public function destroy(User $user)
    {
        $user->delete();

        return redirect()
            ->route('admin.users.index')
            ->with('success', '会员删除成功');
    }

    public function ban(User $user)
    {
        $user->is_banned = true;
        $user->save();
        return redirect()->route('admin.users.index')
            ->with('success', '用户已被禁用');
    }

    public function unban(User $user)
    {
        $user->is_banned = false;
        $user->save();
        return redirect()->route('admin.users.index')
            ->with('success', '用户已被启用');
    }
} 