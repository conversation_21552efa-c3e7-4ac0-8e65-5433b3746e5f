# 螺旋AWS管理系统搭建教程
php artisan serve
php artisan serve --host=0.0.0.0 --port=8080

## 环境要求

- PHP >= 7.4
- MySQL >= 5.7
- Nginx/Apache
- Composer

## 一、宝塔面板配置

### 1. PHP配置

1. 在宝塔面板中，打开 `软件商店` -> `PHP管理器` -> `PHP7.4`

2. 点击 `设置` -> `禁用函数`，删除以下函数的禁用：
```proc_open
proc_get_status
exec
shell_exec
putenv
```

3. 安装必要的PHP扩展：

在宝塔面板中，打开 `软件商店` -> `PHP管理器` -> `PHP7.4` -> `设置` -> `安装扩展`，安装以下扩展：

基础扩展（默认已安装）：
- fileinfo
- openssl
- PDO
- Mysqli
- Mbstring
- Curl
- Zip
- GD
- XML

额外需要安装的扩展：
- redis（用于缓存）
- swoole（用于队列处理）
- opcache（用于性能优化）

4. 修改PHP配置：

在PHP设置中，修改以下配置：
- 上传文件大小：建议设置为 50M
- 最大执行时间：建议设置为 300s
- 最大内存限制：建议设置为 256M

### 2. 创建网站

1. 在宝塔面板中，点击 `网站` -> `添加站点`
2. 填写域名（如：aws.yourdomain.com）
3. 选择PHP版本为7.4
4. 创建数据库（记住数据库名、用户名和密码）

## 二、部署程序

### 1. 上传程序

1. 解压下载的zip文件
2. 通过宝塔文件管理器，将解压后的文件上传到网站根目录

### 2. 创建必要目录并设置权限

1. 在网站根目录下创建以下目录（如果不存在）：
```bash
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/logs
mkdir -p bootstrap/cache
```

2. 设置目录权限：
```bash
cd /www/wwwroot/你的网站目录
chown -R www:www .
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod -R 777 storage
chmod -R 777 bootstrap/cache
```

### 3. 配置环境

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 修改.env文件：
```
APP_NAME="螺旋AWS管理系统"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://你的域名

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=你的数据库名
DB_USERNAME=你的数据库用户名
DB_PASSWORD=你的数据库密码
```

### 4. 安装依赖

1. 如果没有安装composer，先执行：
```bash
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer
```

2. 在网站根目录下执行：
```bash
composer install --no-dev
```

3. 生成应用密钥：
```bash
php artisan key:generate
```

### 5. 数据库配置

1. 导入基础数据库结构（如果是全新安装，跳过这步）：
```bash
mysql -u你的数据库用户名 -p你的数据库密码 你的数据库名 < panel_clean.sql
```

2. 如果是重新安装或更新，先清理所有表：
```bash
php artisan migrate:reset
# 或者手动删除所有表
```

3. 运行数据库迁移：
```bash
# 如果是全新安装
php artisan migrate --force

# 如果已有数据，使用fresh命令重新创建所有表
php artisan migrate:fresh --force
```

4. 创建会话表（如果需要）：
```bash
php artisan session:table
php artisan migrate
```

5. 创建缓存表（如果需要）：
```bash
php artisan cache:table
php artisan migrate
```

6. 重置数据库并填充基础数据：
```bash
# 如果使用了migrate:fresh，需要重新导入管理员账户等基础数据
mysql -u你的数据库用户名 -p你的数据库密码 你的数据库名 < panel_clean.sql

# 或者使用以下命令创建管理员账户
php artisan tinker
>>> DB::table('admins')->insert([
    'username' => 'admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('admin'),
    'created_at' => now(),
    'updated_at' => now()
]);
```

### 6. 缓存配置

1. 设置文件权限：
```bash
chmod -R 777 storage/framework/sessions
chmod -R 777 storage/framework/views
chmod -R 777 storage/framework/cache
```

2. 清理和重新生成缓存：
```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 7. 配置会话和缓存驱动

1. 修改 .env 文件中的会话和缓存配置：
```
SESSION_DRIVER=database
CACHE_DRIVER=database
```

2. 重启PHP进程：
在宝塔面板中，点击 `PHP管理器` -> `PHP7.4` -> `重启`

### 8. 设置文件上传限制

在宝塔面板中，修改PHP配置：
1. 打开 `PHP管理器` -> `PHP7.4` -> `配置文件`
2. 修改以下参数：
```ini
upload_max_filesize = 50M
post_max_size = 50M
memory_limit = 256M
max_execution_time = 300
```

3. 保存并重启PHP

### 9. 导入数据库

1. 在宝塔面板中，打开 `数据库` -> 选择你创建的数据库 -> `导入`
2. 选择 `panel_clean.sql` 文件导入

### 10. 设置定时任务

在宝塔面板中添加定时任务：

1. 进入宝塔面板 -> `计划任务`

2. 点击 `添加计划任务`

3. 填写以下信息：
   - 任务名称：`AWS面板定时任务`
   - 执行周期：`N分钟`，设置为 `1` （每分钟执行一次）
   - 脚本内容：
   ```bash
   cd /www/wwwroot/你的网站目录 && php artisan schedule:run >> /dev/null 2>&1
   ```
   
   注意：将 `/www/wwwroot/你的网站目录` 替换为你的实际网站目录路径

4. 定时任务说明：
   - 此任务用于执行系统的计划任务，包括：
     * AWS账户状态检测（每5分钟）
     * 系统日志清理（每天凌晨）
     * 临时文件清理（每天凌晨）
     * 会话数据清理（每天凌晨）
     * 过期账户检测（每小时）

5. 添加后测试：
   - 点击任务列表中的 `执行` 按钮测试任务是否正常
   - 查看网站目录下的 `storage/logs/laravel.log` 确认任务执行情况

6. 常见问题处理：
   - 如果任务无法执行，检查：
     * PHP命令路径是否正确（可以使用绝对路径：`/www/server/php/74/bin/php`）
     * 网站目录权限是否正确
     * storage目录是否有写入权限

7. 其他重要定时任务（按需添加）：
   ```bash
   # 每天凌晨1点清理过期的会话数据
   cd /www/wwwroot/你的网站目录 && php artisan session:gc

   # 每6小时清理一次缓存
   cd /www/wwwroot/你的网站目录 && php artisan cache:clear

   # 每天凌晨2点备份数据库
   cd /www/wwwroot/你的网站目录 && php artisan backup:run
   ```

8. 定时任务时间说明：
   ```
   * * * * * 命令
   | | | | |
   | | | | +----- 星期几 (0-7) (0和7都表示星期天)
   | | | +------- 月份 (1-12)
   | | +--------- 每月第几天 (1-31)
   | +----------- 小时 (0-23)
   +------------- 分钟 (0-59)
   ```

   常用示例：
   - `* * * * *` 每分钟执行
   - `*/5 * * * *` 每5分钟执行
   - `0 * * * *` 每小时执行
   - `0 0 * * *` 每天零点执行
   - `0 1 * * *` 每天凌晨1点执行

9. 监控任务执行：
   - 在 `.env` 文件中设置日志级别：
   ```
   LOG_LEVEL=debug
   ```
   - 查看任务日志：
   ```bash
   tail -f storage/logs/laravel.log
   ```

### 11. 配置站点

1. 在宝塔面板中，打开网站设置
2. 配置伪静态规则（选择Laravel）
3. 开启SSL（如果需要）

## 三、完成安装

1. 访问你的域名
2. 使用以下默认管理员账号登录：
   - 用户名：admin
   - 邮箱：<EMAIL>
   - 密码：admin

## 四、注意事项

1. 确保所有PHP扩展都已正确安装
2. 确保storage和bootstrap/cache目录有写入权限
3. 如果遇到500错误，检查storage/logs/laravel.log查看具体错误信息
4. 建议安装后立即修改管理员密码
5. 定期备份数据库和程序文件

## 五、常见问题

### 1. 500错误
- 检查storage目录权限
- 检查.env配置是否正确
- 查看error.log和laravel.log

### 2. 无法访问
- 检查nginx/apache配置
- 确认域名解析是否正确
- 检查防火墙设置

### 3. 数据库连接错误
- 确认数据库配置是否正确
- 检查数据库用户权限
- 确认MySQL服务是否运行

如遇到其他问题，请查看storage/logs/laravel.log日志文件，或联系技术支持。 