<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserDataTemplate extends Model
{
    protected $table = 'user_data_templates';

    protected $fillable = [
        'name',
        'description',
        'script_content',
        'os_type',
        'category',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取活跃的模板
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 根据操作系统类型获取模板
     */
    public function scopeForOs($query, $osType)
    {
        return $query->where(function($q) use ($osType) {
            $q->where('os_type', $osType)
              ->orWhere('os_type', 'all');
        });
    }

    /**
     * 根据分类获取模板
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }
}
