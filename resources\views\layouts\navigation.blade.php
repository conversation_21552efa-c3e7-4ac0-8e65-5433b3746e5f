<!-- 现代化导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="{{ url('/') }}">
            {{ \App\Models\Setting::get('dashboard_title', config('app.name')) }}
        </a>
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                @auth
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                            <i class="bi bi-speedometer2 me-1"></i>仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('aws-accounts.index') }}" class="nav-link {{ request()->routeIs('aws-accounts.*') ? 'active' : '' }}">
                            <i class="bi bi-cloud me-1"></i>AWS管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('aws-regions.index') }}" class="nav-link {{ request()->routeIs('aws-regions.*') ? 'active' : '' }}">
                            <i class="bi bi-globe me-1"></i>区域开通
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('aws-iam.*') ? 'active' : '' }}" href="{{ route('aws-iam.index') }}">
                            <i class="bi bi-person-plus me-1"></i>IAM创建
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('user/aws-mfa*') ? 'active' : '' }}" href="{{ url('user/aws-mfa') }}">
                            <i class="bi bi-shield-check me-1"></i>MFA登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('aws-ec2.*') ? 'active' : '' }}" href="{{ route('aws-ec2.index') }}">
                            <i class="bi bi-server me-1"></i>EC2实例
                        </a>
                    </li>
                @endauth
            </ul>
            <ul class="navbar-nav">
                @guest
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('renewal.index') }}">
                            <i class="bi bi-clock-history me-1"></i>会员续费
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>会员登录
                        </a>
                    </li>
                    @if(\App\Models\Setting::getBool('register_enabled', true))
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('register') }}">
                            <i class="bi bi-person-plus me-1"></i>会员注册
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" href="https://t.me/Kax0rs" target="_blank">
                            <i class="bi bi-headset me-1"></i>联系客服
                        </a>
                    </li>
                @else
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" aria-expanded="false">
                            <span>{{ Auth::user()->username }}</span>
                            <span class="badge bg-danger ms-2">剩余{{ Auth::user()->formatted_remaining_time }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="bi bi-person me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#proxySettingsModal"><i class="bi bi-globe me-2"></i>代理设置</a></li>
                            <li><a class="dropdown-item" href="https://t.me/Kax0rs" target="_blank"><i class="bi bi-headset me-2"></i>联系客服</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @endguest
            </ul>
        </div>
    </div>
</nav>

<style>
.navbar {
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    padding: 0.5rem 1rem !important;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-link:hover {
    color: rgba(255,255,255,0.95) !important;
}

.nav-link.active {
    color: #ffffff !important;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
}

.dropdown-menu {
    border-radius: 8px;
    margin-top: 10px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: rgba(0,0,0,0.05);
}

.dropdown-item.text-danger:hover {
    background: rgba(220,53,69,0.1);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

/* 移动端优化 */
@media (max-width: 991.98px) {
    .navbar-collapse {
        margin-top: 0.5rem;
    }

    .navbar-nav {
        width: 100%;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        width: 100%;
        text-align: left;
        padding: 0.75rem 1rem !important;
    }

    /* 移动端下拉菜单样式 */
    .dropdown-menu {
        position: static !important;
        float: none;
        width: 100%;
        margin-top: 0;
        background-color: rgba(255,255,255,0.95);
        border: none;
        border-radius: 0;
        box-shadow: none;
    }

    .dropdown-item {
        color: #333;
        padding: 0.75rem 1.5rem;
    }

    .dropdown-item:hover {
        background-color: rgba(0,0,0,0.1);
        color: #333;
    }

    .dropdown-item.text-danger {
        color: #dc3545 !important;
    }

    .dropdown-item.text-danger:hover {
        background-color: rgba(220,53,69,0.1);
        color: #dc3545 !important;
    }
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏折叠功能
    var navbarToggler = document.querySelector('.navbar-toggler');
    var navbarCollapse = document.querySelector('.navbar-collapse');
    var dropdownToggle = document.querySelector('#navbarDropdown');
    var dropdownMenu = document.querySelector('.dropdown-menu');

    // 处理导航栏折叠
    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function(e) {
            e.preventDefault();
            navbarCollapse.classList.toggle('show');
        });

        // 点击普通导航链接后自动收起菜单
        var navLinks = navbarCollapse.querySelectorAll('.nav-link:not(.dropdown-toggle)');
        navLinks.forEach(function(link) {
            link.addEventListener('click', function() {
                navbarCollapse.classList.remove('show');
            });
        });

        // 点击页面其他地方时收起菜单
        document.addEventListener('click', function(e) {
            if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                navbarCollapse.classList.remove('show');
                if (dropdownMenu) {
                    dropdownMenu.classList.remove('show');
                }
            }
        });
    }

    // 处理用户下拉菜单
    if (dropdownToggle && dropdownMenu) {
        dropdownToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 切换下拉菜单显示状态
            var isCurrentlyShown = dropdownMenu.classList.contains('show');

            if (isCurrentlyShown) {
                dropdownMenu.classList.remove('show');
                dropdownToggle.setAttribute('aria-expanded', 'false');
            } else {
                dropdownMenu.classList.add('show');
                dropdownToggle.setAttribute('aria-expanded', 'true');
            }
        });

        // 点击下拉菜单项后的处理
        var dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                // 如果是退出登录按钮，不阻止默认行为
                if (item.tagName.toLowerCase() === 'button') {
                    return;
                }

                // 对于链接，收起菜单
                if (navbarCollapse) {
                    navbarCollapse.classList.remove('show');
                }
                dropdownMenu.classList.remove('show');
                dropdownToggle.setAttribute('aria-expanded', 'false');
            });
        });

        // 点击外部区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
                dropdownToggle.setAttribute('aria-expanded', 'false');
            }
        });
    }
});
</script>
@endpush