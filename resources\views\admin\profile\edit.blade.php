@extends('admin.layouts.app')

@section('title', '个人资料管理')

@section('content')
<div class="container-fluid p-0">
    <!-- 页面标题区 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-dark fw-bold mb-0">个人资料管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">控制台</a></li>
                    <li class="breadcrumb-item active">个人资料</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- 左侧个人信息卡片 -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <h5 class="fw-bold mb-1">{{ auth()->guard('admin')->user()->username }}</h5>
                    <p class="text-muted mb-3">{{ auth()->guard('admin')->user()->role_name ?? '系统管理员' }}</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-primary-subtle text-primary">
                            <i class="bi bi-shield-check me-1"></i>已认证
                        </span>
                        <span class="badge bg-success-subtle text-success">
                            <i class="bi bi-clock-history me-1"></i>在线
                        </span>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="row text-center">
                        <div class="col">
                            <h6 class="mb-1">{{ auth()->guard('admin')->user()->created_at->format('Y-m-d') }}</h6>
                            <small class="text-muted">加入时间</small>
                        </div>
                        <div class="col">
                            <h6 class="mb-1">{{ auth()->guard('admin')->user()->last_login_at ?? '未登录' }}</h6>
                            <small class="text-muted">最后登录</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧表单区域 -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-person-gear me-2"></i>基本信息设置
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.profile.update') }}" method="POST" class="needs-validation" novalidate>
                        @csrf
                        @method('PATCH')

                        <div class="row g-4">
                            <!-- 管理员名称 -->
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" 
                                           class="form-control @error('username') is-invalid @enderror" 
                                           id="username" 
                                           name="username" 
                                           value="{{ old('username', auth()->guard('admin')->user()->username) }}" 
                                           placeholder="请输入管理员名称"
                                           required>
                                    <label for="username">管理员名称</label>
                                    @error('username')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- 邮箱地址 -->
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', auth()->guard('admin')->user()->email) }}" 
                                           placeholder="请输入邮箱地址"
                                           required>
                                    <label for="email">邮箱地址</label>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- 当前密码 -->
                            <div class="col-md-12">
                                <div class="form-floating">
                                    <input type="password" 
                                           class="form-control @error('current_password') is-invalid @enderror" 
                                           id="current_password" 
                                           name="current_password"
                                           placeholder="请输入当前密码">
                                    <label for="current_password">当前密码</label>
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- 新密码 -->
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="password" 
                                           class="form-control @error('password') is-invalid @enderror" 
                                           id="password" 
                                           name="password"
                                           placeholder="请输入新密码">
                                    <label for="password">新密码</label>
                                    <div class="form-text">如不修改密码请留空</div>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- 确认新密码 -->
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password_confirmation" 
                                           name="password_confirmation"
                                           placeholder="请确认新密码">
                                    <label for="password_confirmation">确认新密码</label>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="text-end mt-4">
                            <button type="reset" class="btn btn-light me-2">
                                <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 安全日志卡片 -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-lock me-2"></i>安全日志
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline-sm">
                        <div class="timeline-item">
                            <span class="timeline-date">{{ now()->format('Y-m-d H:i') }}</span>
                            <div class="timeline-content">
                                <h6 class="text-primary mb-0">登录系统</h6>
                                <p class="text-muted mb-0">从 {{ request()->ip() }} 登录系统</p>
                            </div>
                        </div>
                        <!-- 这里可以添加更多日志记录 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表单验证
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>
@endpush

@push('styles')
<style>
.timeline-sm {
    padding-left: 20px;
    border-left: 2px solid #e9ecef;
}
.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}
.timeline-item:last-child {
    padding-bottom: 0;
}
.timeline-item:before {
    content: '';
    position: absolute;
    left: -26px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #3b7ddd;
    background-color: #fff;
}
.timeline-date {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
@endpush 