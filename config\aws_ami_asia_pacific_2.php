<?php

/**
 * AWS AMI数据配置 - 亚太地区2
 * 包含南亚、大洋洲等地区
 */

return [
    'ap-south-1' => [
        'name' => '亚太地区（孟买）',
        'code' => 'ap-south-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a7cf821b91bcccbc', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0a7cf821b91bcccbc', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0b123456cdef78901', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0b123456cdef78901', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0b123456cdef78901', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0b123456cdef78901', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0b123456cdef78901', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'ap-south-2' => [
        'name' => '亚太地区（海得拉巴）',
        'code' => 'ap-south-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0c234567def890123', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'ap-southeast-4' => [
        'name' => '亚太地区（墨尔本）',
        'code' => 'ap-southeast-4',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0c6b5d3bb3f3b1f1f', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-southeast-5' => [
        'name' => '亚太地区（马来西亚）',
        'code' => 'ap-southeast-5',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0bcdef56789012345', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0bcdef56789012345', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0bcdef56789012345', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-southeast-7' => [
        'name' => '亚太地区（泰国）',
        'code' => 'ap-southeast-7',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0cdef567890123456', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0cdef567890123456', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0cdef567890123456', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-east-1' => [
        'name' => '亚太地区（香港）',
        'code' => 'ap-east-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0f314b0c2f4165d9c', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0c2ce3930bd9f1cd1', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0f314b0c2f4165d9c', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0f1234567890abcde', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-06789012abcdef345', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0a0987654321fedcb', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0f1234567890abcde', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-east-2' => [
        'name' => '亚太地区（台北）',
        'code' => 'ap-east-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ]
];
