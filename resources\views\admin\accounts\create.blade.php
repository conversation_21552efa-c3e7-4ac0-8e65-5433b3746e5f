@extends('admin.layouts.app')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0 text-dark">添加AWS账户</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-secondary">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.accounts.index') }}" class="text-secondary">AWS账户管理</a></li>
                <li class="breadcrumb-item active" aria-current="page">添加账户</li>
            </ol>
        </nav>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.accounts.store') }}" method="POST">
            @csrf
            
            <div class="row g-3">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="user_id" class="form-label">所属会员 <span class="text-danger">*</span></label>
                <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror" required>
                            <option value="">选择会员</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                            {{ $user->username }}
                        </option>
                    @endforeach
                </select>
                @error('user_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            </div>

                <div class="col-12">
                    <div class="form-group">
                        <label for="accounts_data" class="form-label">账户信息 <span class="text-danger">*</span></label>
                        <div class="mb-2">
                            <small class="text-muted">请按照以下格式输入账户信息，每行一个账户：</small>
                            <pre class="bg-light p-2 rounded"><code>①微软账号：账号@outlook.com ②微软密码：密码 ③AWS密码：密码 ④访问密钥：密钥 ⑤秘密访问密钥：密钥</code></pre>
            </div>
                        <textarea class="form-control @error('accounts_data') is-invalid @enderror" 
                                id="accounts_data" name="accounts_data" rows="10" 
                                placeholder="请输入账户信息，每行一个账户..." required>{{ old('accounts_data') }}</textarea>
                        @error('accounts_data')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">添加账户</button>
                <a href="{{ route('admin.accounts.index') }}" class="btn btn-light ms-2">返回列表</a>
            </div>
        </form>
    </div>
</div>

<style>
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

pre {
    margin-bottom: 0.5rem;
}

pre code {
    color: #666;
}
</style>
@endsection 