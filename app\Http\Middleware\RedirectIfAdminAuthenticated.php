<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfAdminAuthenticated
{
    public function handle(Request $request, Closure $next)
    {
        // 获取动态后台路径
        $adminUrl = trim(config('admin.url'), '/');
        $currentPath = trim($request->path(), '/');
        
        // 检查是否在后台路径下
        $isAdminPath = $currentPath === $adminUrl || str_starts_with($currentPath, $adminUrl.'/');
        
        if ($isAdminPath) {
            if (Auth::guard('admin')->check()) {
                // 已登录且访问登录页面，重定向到仪表盘
                if ($request->routeIs('admin.login')) {
                    return redirect()->route('admin.dashboard');
                }
            } else {
                // 未登录且不是访问登录页面或注册页面，重定向到登录页
                if (!$request->routeIs(['admin.login', 'admin.register'])) {
                    return redirect()->route('admin.login');
                }
            }
        }

        return $next($request);
    }
} 