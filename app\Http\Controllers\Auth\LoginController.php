<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    use AuthenticatesUsers;

    /**
     * 登录成功后重定向到会员仪表盘
     *
     * @var string
     */
    protected $redirectTo = '/user/dashboard';

    /**
     * 退出登录后重定向到首页
     *
     * @return string
     */
    protected function logoutRedirectTo()
    {
        return '/';
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if (Auth::attempt($credentials, $request->filled('remember'))) {
            $user = Auth::user();

            // 检查用户是否过期
            if ($user->isExpired()) {
                Auth::logout();
                return back()->withErrors([
                    'expired' => '您的账户已过期，请联系管理员续期'
                ])->withInput($request->only('username', 'remember'));
            }

            // 更新最后登录时间
            $user->update(['last_login_at' => now()]);

            $request->session()->regenerate();

            // 只有当用户勾选了"记住我"时，才保存用户名到 session
            if ($request->filled('remember')) {
                session(['remembered_username' => $request->username]);
            } else {
                session()->forget('remembered_username');
            }

            return redirect()->intended('/user/dashboard');
        }

        return back()->withErrors([
            'username' => '用户名或密码错误',
        ])->withInput($request->only('username', 'remember'));
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/');
    }
} 