<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\AwsAccount;
use App\Models\ActivityLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        $stats = [
            'total_users' => User::count(),
            'today_users' => User::whereDate('created_at', $today)->count(),
            'total_aws_accounts' => AwsAccount::count(),
            'today_aws_accounts' => AwsAccount::whereDate('created_at', $today)->count(),
            'yesterday_aws_accounts' => AwsAccount::whereDate('created_at', $yesterday)->count(),
            'today_checks' => ActivityLog::whereDate('created_at', $today)
                ->where('action_type', 'check_account')
                ->count(),
            'yesterday_checks' => ActivityLog::whereDate('created_at', $yesterday)
                ->where('action_type', 'check_account')
                ->count(),
            'today_quotas' => ActivityLog::whereDate('created_at', $today)
                ->where('action_type', 'check_quota')
                ->count(),
            'yesterday_quotas' => ActivityLog::whereDate('created_at', $yesterday)
                ->where('action_type', 'check_quota')
                ->count(),
            // 添加图表数据
            'user_growth' => $this->getUserGrowthData(),
            'account_status' => $this->getAccountStatusData(),
            'activity_data' => $this->getActivityData(),
        ];

        return view('admin.dashboard', compact('stats'));
    }

    private function getUserGrowthData()
    {
        // 获取过去30天的数据
        $startDate = Carbon::now()->subDays(29);
        $endDate = Carbon::now();

        $userData = User::select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        $data = [];
        $dates = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $currentDate = $current->format('Y-m-d');
            $dates[] = $current->format('m-d');
            $data[] = $userData->get($currentDate)?->count ?? 0;
            $current->addDay();
        }

        return [
            'dates' => $dates,
            'data' => $data
        ];
    }

    private function getAccountStatusData()
    {
        try {
            $statusData = AwsAccount::select('status', DB::raw('COUNT(*) as count'))
                ->groupBy('status')
                ->get();

            $labels = [];
            $data = [];
            $colors = [];
            $colorMap = [
                0 => '#f7b84b', // 未测试 - 黄色
                1 => '#0ab39c', // 正常 - 绿色
                2 => '#f06548'  // 封禁 - 红色
            ];

            // 如果没有数据，返回默认的空状态
            if ($statusData->isEmpty()) {
                return [
                    'labels' => ['暂无数据'],
                    'data' => [1],
                    'colors' => ['#e9ecef']
                ];
            }

            foreach ($statusData as $status) {
                $statusText = match ($status->status) {
                    0 => '未测试',
                    1 => '正常',
                    2 => '封禁',
                    default => '未知'
                };
                $labels[] = $statusText;
                $data[] = (int)$status->count;
                $colors[] = $colorMap[$status->status] ?? '#6c757d';
            }

            return [
                'labels' => $labels,
                'data' => $data,
                'colors' => $colors
            ];
        } catch (\Exception $e) {
            // 如果出错，返回默认数据
            return [
                'labels' => ['暂无数据'],
                'data' => [1],
                'colors' => ['#e9ecef']
            ];
        }
    }

    private function getActivityData()
    {
        try {
            $startDate = Carbon::now()->startOfDay();
            $endDate = Carbon::now();

            $activityData = ActivityLog::select(
                    DB::raw('HOUR(created_at) as hour'),
                    DB::raw('COUNT(*) as count')
                )
                ->where('created_at', '>=', $startDate)
                ->groupBy('hour')
                ->orderBy('hour')
                ->get()
                ->keyBy('hour');

            $data = [];
            $hours = [];

            for ($i = 0; $i < 24; $i++) {
                $hours[] = sprintf('%02d:00', $i);
                $count = $activityData->get($i)?->count ?? 0;
                // 确保数据是有效的数字
                $data[] = is_numeric($count) ? (int)$count : 0;
            }

            return [
                'hours' => $hours,
                'data' => $data
            ];
        } catch (\Exception $e) {
            // 如果出错，返回默认的空数据
            $data = [];
            $hours = [];

            for ($i = 0; $i < 24; $i++) {
                $hours[] = sprintf('%02d:00', $i);
                $data[] = 0;
            }

            return [
                'hours' => $hours,
                'data' => $data
            ];
        }
    }
} 