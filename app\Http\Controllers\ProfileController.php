<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use App\Models\ActivationCode;
use Carbon\Carbon;

class ProfileController extends Controller
{
    public function edit()
    {
        return view('profile.edit', [
            'user' => auth()->user()
        ]);
    }

    public function update(Request $request)
    {
        $user = auth()->user();

        $rules = [
            'username' => ['required', 'string', 'max:255', 'unique:users,username,' . $user->id],
        ];

        // 只有当用户尝试修改密码时才验证密码相关字段
        if ($request->filled('password')) {
            $rules['current_password'] = ['required'];
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        }

        $request->validate($rules);

        // 如果用户尝试修改密码，验证当前密码是否正确
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => '当前密码不正确']);
            }
        }

        $user->username = $request->username;

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        $user->save();

        return back()->with('success', '个人资料已更新');
    }

    public function renew(Request $request)
    {
        $request->validate([
            'activation_code' => ['required', 'string', 'max:255'],
        ]);

        $user = auth()->user();
        $code = ActivationCode::where('code', $request->activation_code)
            ->where('is_used', false)
            ->first();

        if (!$code) {
            return back()
                ->withInput()
                ->withErrors(['activation_code' => '激活码无效或已使用'])
                ->with('scroll_to_renewal', true);
        }

        // 未使用的激活码永不过期，只检查是否已使用

        // 更新用户的到期时间
        $currentExpiry = $user->expires_at ? Carbon::parse($user->expires_at) : now();
        $newExpiry = $currentExpiry->gt(now()) ? $currentExpiry : now();
        $user->expires_at = $newExpiry->addDays($code->valid_days);
        $user->save();

        // 标记激活码为已使用
        $code->is_used = true;
        $code->user_id = $user->id;
        $code->save();

        return back()->with('renewal_success', '续费成功，会员有效期已延长' . $code->valid_days . '天');
    }
} 