<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * 定义应用的命令调度
     */
    protected function schedule(Schedule $schedule): void
    {
        // AWS账户状态检测（每5分钟执行一次）
        /*$schedule->command('aws:check-status')
                ->everyFiveMinutes()
                ->withoutOverlapping()
                ->appendOutputTo(storage_path('logs/aws-check.log'));*/

        // 清理过期的会话（每天凌晨1点）
        $schedule->command('session:gc')
                ->dailyAt('01:00')
                ->withoutOverlapping();

        // 清理系统日志（每天凌晨2点）
        $schedule->command('log:clear')
                ->dailyAt('02:00')
                ->withoutOverlapping();

        // 清理临时文件（每天凌晨3点）
        $schedule->command('temp:clear')
                ->dailyAt('03:00')
                ->withoutOverlapping();

        // 检查用户账户过期（每小时执行）
        $schedule->command('user:check-expiration')
                ->hourly()
                ->withoutOverlapping();

        // 数据库备份（每天凌晨4点）
        $schedule->command('backup:run')
                ->dailyAt('04:00')
                ->withoutOverlapping();

        // 清理缓存（每6小时执行一次）
        $schedule->command('cache:clear')
                ->everySixHours()
                ->withoutOverlapping();


    }

    /**
     * 注册命令
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 