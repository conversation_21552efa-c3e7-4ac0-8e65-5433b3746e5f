<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ \App\Models\Setting::get('site_name', config('app.name')) }} - 会员控制中心</title>

    <!-- jQuery -->
    <script src="{{ asset('assets/js/jquery-3.6.0.min.js') }}"></script>

    <!-- Bootstrap CSS -->
    <link href="{{ asset('assets/css/bootstrap-5.1.3.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/bootstrap-icons-1.8.1.css') }}" rel="stylesheet">

    <!-- Bootstrap JS -->
    <script src="{{ asset('assets/js/popper-2.9.2.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap-5.1.3.bundle.min.js') }}"></script>

    <!-- 免费代理监控器 -->
    <script src="{{ asset('js/free-proxy-monitor.js') }}"></script>

    <!-- Styles -->
    <link href="{{ asset('assets/css/app.min.css') }}" rel="stylesheet">

    <style>
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 9999;
        }
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        .btn-hover-shadow:hover {
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
            transform: translateY(-2px);
            transition: all .3s ease;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all .3s ease;
        }
        .card:hover {
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        /* 移动端导航栏样式 */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                top: 100%;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
                padding: 1rem;
                border-radius: 0 0 8px 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            .navbar-collapse.show {
                display: block !important;
            }
            
            .navbar-nav {
                margin: 0 !important;
            }
            
            .nav-item {
                margin: 0.5rem 0;
            }
            
            .nav-link {
                color: white !important;
                padding: 0.5rem 1rem !important;
                border-radius: 4px;
            }
            
            .nav-link:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }
    </style>
    @stack('styles')
</head>
<body>
    <div id="app">
        @include('layouts.navigation')

        <main class="py-4">
            @if(session('success'))
                <div class="container">
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="container">
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                </div>
            @endif

            @yield('content')
        </main>


    </div>

    <!-- 代理设置模态框 -->
    @auth
        @include('components.proxy-settings-modal')
    @endauth

    @stack('scripts')

    <!-- 免费代理状态监控初始化 -->
    @auth
    <script>
    window.initFreeProxyMonitor = function() {
        const currentProxyMode = '{{ Auth::user()->proxy_mode ?? "local" }}';
        @php
            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');
            $freeProxyErrorMessage = \App\Models\Setting::get('free_proxy_error_message', '');
        @endphp
        const freeProxyStatus = '{{ $freeProxyStatus }}';
        const freeProxyErrorMessage = '{{ $freeProxyErrorMessage }}';

        // 初始化免费代理监控器
        if (window.FreeProxyMonitor) {
            window.FreeProxyMonitor.init(currentProxyMode, freeProxyStatus, freeProxyErrorMessage);
        }
    };
    </script>
    @endauth
</body>
</html>