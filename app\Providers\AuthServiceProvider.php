<?php

namespace App\Providers;

use App\Models\AwsAccount;
use App\Policies\AwsAccountPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        AwsAccount::class => AwsAccountPolicy::class,
    ];

    public function boot(): void
    {
        $this->registerPolicies();
    }
} 