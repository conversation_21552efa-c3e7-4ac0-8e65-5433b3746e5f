<!-- 代理设置模态框 -->
<div class="modal fade" id="proxySettingsModal" tabindex="-1" aria-labelledby="proxySettingsModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="proxySettingsModalLabel">
                    <i class="bi bi-globe me-2"></i>代理设置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!--<div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>代理模式说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>本地模式：</strong>直接从服务器请求AWS API</li>
                        <li><strong>代理模式：</strong>通过您的代理IP请求AWS API，避免服务器IP被封禁</li>
                    </ul>
                </div>-->

                <form id="proxySettingsForm">
                    @csrf
                    
                    <!-- 模式选择 -->
                    <div class="mb-4">
                        <label class="form-label fw-bold mb-3">请求模式</label>
                        <div class="mode-selector">
                            <div class="mode-option">
                                <input type="radio" name="proxy_mode" id="modal_mode_local" value="local"
                                       {{ (Auth::user()->proxy_mode ?? 'local') === 'local' ? 'checked' : '' }}>
                                <label for="modal_mode_local" class="mode-label">
                                    <div class="mode-content">
                                        <div class="mode-icon-wrapper">
                                            <i class="bi bi-server mode-icon"></i>
                                        </div>
                                        <div class="mode-text">
                                            <h6 class="mode-title">本地模式</h6>
                                            <p class="mode-desc">服务器直连AWS API</p>
                                        </div>
                                    </div>
                                    <div class="mode-check">
                                        <i class="bi bi-check-circle-fill"></i>
                                    </div>
                                </label>
                            </div>

                            @if(\App\Models\Setting::getBool('free_proxy_enabled', false))
                            <div class="mode-option">
                                <input type="radio" name="proxy_mode" id="modal_mode_free_proxy" value="free_proxy"
                                       {{ (Auth::user()->proxy_mode ?? 'local') === 'free_proxy' ? 'checked' : '' }}>
                                <label for="modal_mode_free_proxy" class="mode-label">
                                    <div class="mode-content">
                                        <div class="mode-icon-wrapper">
                                            <i class="bi bi-gift mode-icon"></i>
                                        </div>
                                        <div class="mode-text">
                                            <h6 class="mode-title">免费代理</h6>
                                            <p class="mode-desc">使用系统提供的免费代理</p>
                                        </div>
                                    </div>
                                    <div class="mode-check">
                                        <i class="bi bi-check-circle-fill"></i>
                                    </div>
                                </label>
                            </div>
                            @endif

                            <div class="mode-option">
                                <input type="radio" name="proxy_mode" id="modal_mode_proxy" value="proxy"
                                       {{ (Auth::user()->proxy_mode ?? 'local') === 'proxy' ? 'checked' : '' }}>
                                <label for="modal_mode_proxy" class="mode-label">
                                    <div class="mode-content">
                                        <div class="mode-icon-wrapper">
                                            <i class="bi bi-shield-check mode-icon"></i>
                                        </div>
                                        <div class="mode-text">
                                            <h6 class="mode-title">代理模式</h6>
                                            <p class="mode-desc">通过代理IP请求AWS API</p>
                                        </div>
                                    </div>
                                    <div class="mode-check">
                                        <i class="bi bi-check-circle-fill"></i>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 代理配置区域 -->
                    <div id="modalProxyConfig" style="{{ (Auth::user()->proxy_mode ?? 'local') === 'local' ? 'display:none' : '' }}">
                        <hr class="my-4">
                        
                        <!-- 代理类型 -->
                        <div class="mb-3">
                            <label class="form-label">代理类型</label>
                            <select class="form-select" name="proxy_type">
                                <option value="http" {{ (Auth::user()->proxy_type ?? 'http') === 'http' ? 'selected' : '' }}>
                                    HTTP代理 (推荐)
                                </option>
                                <option value="socks5" {{ (Auth::user()->proxy_type ?? 'http') === 'socks5' ? 'selected' : '' }}>
                                    SOCKS5代理
                                </option>
                            </select>
                            <div class="form-text">HTTP代理兼容性更好，SOCKS5代理速度更快</div>
                        </div>
                        
                        <!-- 代理地址 -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">代理主机</label>
                                    <input type="text" class="form-control" name="proxy_host" 
                                           value="{{ Auth::user()->proxy_host ?? '' }}" placeholder="例如: *************">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">端口</label>
                                    <input type="number" class="form-control" name="proxy_port" 
                                           value="{{ Auth::user()->proxy_port ?? '' }}" placeholder="8080" min="1" max="65535">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 代理认证 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户名 <span class="text-muted">(可选)</span></label>
                                    <input type="text" class="form-control" name="proxy_username" 
                                           value="{{ Auth::user()->proxy_username ?? '' }}" placeholder="代理用户名">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">密码 <span class="text-muted">(可选)</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="proxy_password" name="proxy_password"
                                               value="{{ Auth::user()->proxy_password ?? '' }}" placeholder="代理密码">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye" id="passwordIcon"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 代理状态 -->
                        @if((Auth::user()->proxy_mode ?? 'local') === 'proxy')
                        <div class="mb-3">
                            <label class="form-label">代理状态</label>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ (Auth::user()->proxy_status ?? 'inactive') === 'active' ? 'success' : 'danger' }} me-2">
                                    <i class="bi bi-{{ (Auth::user()->proxy_status ?? 'inactive') === 'active' ? 'check-circle' : 'x-circle' }} me-1"></i>
                                    {{ (Auth::user()->proxy_status ?? 'inactive') === 'active' ? '正常' : '异常' }}
                                </span>
                                @if(Auth::user()->proxy_last_test ?? false)
                                    <small class="text-muted">
                                        最后测试: {{ \Carbon\Carbon::parse(Auth::user()->proxy_last_test)->format('m-d H:i') }}
                                    </small>
                                @endif
                            </div>
                            @if(Auth::user()->proxy_error_message ?? false)
                                <div class="text-danger mt-1 small">{{ Auth::user()->proxy_error_message }}</div>
                            @endif
                        </div>
                        @endif
                    </div>
                </form>
                </form>

                <!-- 联系客服提示 -->
                <div class="mt-4 p-3 bg-light rounded">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                代理配置遇到问题？我们的技术支持团队随时为您服务
                            </small>
                        </div>
                        <a href="https://t.me/Kax0rs" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-headset me-1"></i>联系客服
                        </a>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-outline-primary" id="modalTestProxy" style="display:none;">
                    <i class="bi bi-wifi me-1"></i>测试连接
                </button>
                <button type="button" class="btn btn-primary" id="modalSaveProxy">
                    <i class="bi bi-save me-1"></i>保存设置
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* 现代化模式选择器 */
.mode-selector {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mode-option {
    position: relative;
}

.mode-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.mode-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.mode-label:hover {
    border-color: #4361ee;
    background: rgba(67, 97, 238, 0.02);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.mode-option input[type="radio"]:checked + .mode-label {
    border-color: #4361ee;
    background: rgba(67, 97, 238, 0.05);
    box-shadow: 0 4px 16px rgba(67, 97, 238, 0.15);
}

.mode-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mode-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mode-icon {
    font-size: 1.5rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.mode-option input[type="radio"]:checked + .mode-label .mode-icon-wrapper {
    background: linear-gradient(135deg, #4361ee 0%, #3b82f6 100%);
}

.mode-option input[type="radio"]:checked + .mode-label .mode-icon {
    color: #fff;
}

.mode-text {
    flex: 1;
}

.mode-title {
    font-size: 1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.25rem;
}

.mode-desc {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

.mode-check {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mode-check i {
    font-size: 1.25rem;
    color: #e9ecef;
    transition: all 0.3s ease;
}

.mode-option input[type="radio"]:checked + .mode-label .mode-check i {
    color: #4361ee;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mode-label {
        padding: 1rem;
    }

    .mode-content {
        gap: 0.75rem;
    }

    .mode-icon-wrapper {
        width: 40px;
        height: 40px;
    }

    .mode-icon {
        font-size: 1.25rem;
    }

    .mode-title {
        font-size: 0.9rem;
    }

    .mode-desc {
        font-size: 0.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('proxy_password');
    const passwordIcon = document.getElementById('passwordIcon');

    if (togglePassword && passwordInput && passwordIcon) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // 切换图标
            if (type === 'text') {
                passwordIcon.classList.remove('bi-eye');
                passwordIcon.classList.add('bi-eye-slash');
            } else {
                passwordIcon.classList.remove('bi-eye-slash');
                passwordIcon.classList.add('bi-eye');
            }
        });
    }

    // 模式切换
    document.querySelectorAll('input[name="proxy_mode"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            const proxyConfig = document.getElementById('modalProxyConfig');
            const testBtn = document.getElementById('modalTestProxy');

            if (this.value === 'proxy') {
                proxyConfig.style.display = 'block';
                testBtn.style.display = 'inline-block';
            } else {
                proxyConfig.style.display = 'none';
                testBtn.style.display = 'none';
            }
        });
    });

    // 测试代理连接
    document.getElementById('modalTestProxy').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;

        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>测试中...';

        const formData = new FormData();
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        formData.append('proxy_type', document.querySelector('select[name="proxy_type"]').value);
        formData.append('proxy_host', document.querySelector('input[name="proxy_host"]').value);
        formData.append('proxy_port', document.querySelector('input[name="proxy_port"]').value);
        formData.append('proxy_username', document.querySelector('input[name="proxy_username"]').value);
        formData.append('proxy_password', document.querySelector('input[name="proxy_password"]').value);

        fetch('/user/proxy/test', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // 先检查响应状态
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || '请求失败');
                }).catch(() => {
                    throw new Error('服务器响应错误 (状态码: ' + response.status + ')');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('success', '代理连接成功！出口IP: ' + data.ip);
                } else {
                    alert('代理连接成功！\n出口IP: ' + data.ip);
                }
            } else {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', '代理连接失败：' + data.message);
                } else {
                    alert('代理连接失败：' + data.message);
                }
            }
        })
        .catch(error => {
            console.error('代理测试错误:', error);
            const errorMessage = error.message || '网络错误';
            if (typeof showProxyToast === 'function') {
                showProxyToast('error', '测试失败：' + errorMessage);
            } else {
                alert('测试失败：' + errorMessage);
            }
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    });

    // 保存代理设置
    document.getElementById('modalSaveProxy').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;

        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>保存中...';

        // 获取表单元素
        const form = document.getElementById('proxySettingsForm');
        const formData = new FormData(form);

        // 获取选择的代理模式
        const selectedMode = formData.get('proxy_mode');

        // 如果选择的是本地模式或免费代理模式，移除代理配置字段
        if (selectedMode === 'local' || selectedMode === 'free_proxy') {
            // 创建一个新的FormData对象，只包含必要的字段
            const cleanFormData = new FormData();
            cleanFormData.append('_token', formData.get('_token'));
            cleanFormData.append('proxy_mode', selectedMode);

            // 使用清理后的表单数据
            return submitProxySettings(cleanFormData, btn, originalText);
        }

        // 代理模式，使用完整表单数据
        submitProxySettings(formData, btn, originalText);
    });

    // 提交代理设置
    function submitProxySettings(formData, btn, originalText) {

        return fetch('/user/proxy/update', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // 先检查响应状态
            if (!response.ok) {
                // 如果状态码不是2xx，尝试解析错误信息
                return response.text().then(errorText => {
                    console.error('服务器响应错误:', errorText);
                    try {
                        const errorData = JSON.parse(errorText);
                        throw new Error(errorData.message || '请求失败');
                    } catch (e) {
                        throw new Error('服务器响应错误 (状态码: ' + response.status + ')');
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 获取新的代理模式
                const newMode = formData.get('proxy_mode');

                // 显示成功提示
                if (typeof showProxyToast === 'function') {
                    showProxyToast('success', '代理设置保存成功！');
                } else {
                    alert('设置保存成功！');
                }

                // 广播模式变更到其他页面
                if (typeof broadcastProxyModeChange === 'function') {
                    broadcastProxyModeChange(newMode);
                }

                // 更新当前页面显示
                if (typeof updateProxyModeDisplay === 'function') {
                    updateProxyModeDisplay(newMode);
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('proxySettingsModal'));
                modal.hide();

                // 延迟刷新页面以更新状态栏
                setTimeout(() => location.reload(), 1000);
            } else {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', '保存失败：' + data.message);
                } else {
                    alert('保存失败：' + data.message);
                }
            }
        })
        .catch(error => {
            console.error('代理设置保存错误:', error);
            console.error('错误详情:', error.stack);
            const errorMessage = error.message || '网络错误';
            if (typeof showProxyToast === 'function') {
                showProxyToast('error', '保存失败：' + errorMessage);
            } else {
                alert('保存失败：' + errorMessage);
            }
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    }

    // 模态框打开时重新初始化
    const modal = document.getElementById('proxySettingsModal');
    modal.addEventListener('show.bs.modal', function() {
        // 重新获取当前用户的代理模式
        const currentMode = '{{ Auth::user()->proxy_mode ?? "local" }}';

        // 设置正确的选中状态
        document.getElementById('modal_mode_local').checked = (currentMode === 'local');
        @if(\App\Models\Setting::getBool('free_proxy_enabled', false))
        const freeProxyRadio = document.getElementById('modal_mode_free_proxy');
        if (freeProxyRadio) {
            freeProxyRadio.checked = (currentMode === 'free_proxy');
        }
        @endif
        document.getElementById('modal_mode_proxy').checked = (currentMode === 'proxy');

        // 更新显示状态
        const proxyConfig = document.getElementById('modalProxyConfig');
        const testBtn = document.getElementById('modalTestProxy');

        if (currentMode === 'proxy') {
            proxyConfig.style.display = 'block';
            testBtn.style.display = 'inline-block';
        } else {
            proxyConfig.style.display = 'none';
            testBtn.style.display = 'none';
        }
    });

    // 初始化显示状态
    const currentMode = document.querySelector('input[name="proxy_mode"]:checked').value;
    const proxyConfig = document.getElementById('modalProxyConfig');
    const testBtn = document.getElementById('modalTestProxy');

    if (currentMode === 'proxy') {
        proxyConfig.style.display = 'block';
        testBtn.style.display = 'inline-block';
    } else {
        proxyConfig.style.display = 'none';
        testBtn.style.display = 'none';
    }
});
</script>
