@extends('layouts.app')

@section('title', 'EC2实例 - 批量创建实例')

@push('scripts')
<!-- AWS统一数据管理器 -->
<script src="{{ asset('js/aws-unified-data-manager.js') }}"></script>
@endpush

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 0; /* 全屏模式去掉圆角 */
    box-shadow: none; /* 全屏模式去掉阴影 */
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 0; /* 全屏模式去掉底部间距 */
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: #f8f9fa; /* 全屏模式添加背景色 */
    border-bottom: 1px solid rgba(0,0,0,.1);
    padding: 1.5rem; /* 统一内边距 */
}

.card-body {
    padding: 2rem; /* 增加内边距 */
    background: #fff;
}

.steps-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.1);
}

.step-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-label {
    color: #667eea;
    font-weight: 600;
}

.step-connector {
    width: 60px;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    margin-top: -20px;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.section-title {
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.section-icon {
    color: #667eea;
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.os-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 1.5rem;
}

.os-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.8rem 0.4rem 0.6rem 0.4rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    aspect-ratio: 1.4/1; /* 进一步减少高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.os-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.os-card:hover::before {
    left: 100%;
}

.os-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.os-card.selected {
    border-color: #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4) !important;
    border-width: 3px !important;
}

.os-card.selected::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 15px;
    background: #667eea;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.os-icon {
    width: 36px;
    height: 36px;
    margin: 0 auto 0.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.os-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* SUSE图标备用样式 */
.os-card[data-os="SUSE Linux"] .os-icon .suse-fallback {
    font-size: 1.5rem;
    color: #0c322c;
    display: none;
    text-align: center;
    line-height: 36px;
}

.os-card[data-os="SUSE Linux"] .os-icon img.error {
    display: none;
}

.os-card[data-os="SUSE Linux"] .os-icon img.error + .suse-fallback {
    display: block;
}

.os-name {
    font-weight: 600;
    color: #333;
    font-size: 0.8rem;
    text-align: center;
    line-height: 1.1;
}

.ami-selection {
    display: none;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.ami-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.8rem;
}

.ami-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.5rem; /* 减少内边距 */
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    align-items: flex-start; /* 水平左对齐 */
    text-align: left; /* 文字左对齐 */
}

.ami-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.15);
}

.ami-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.ami-card.selected::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 15px;
    background: #667eea;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

.ami-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ami-description {
    display: none; /* 隐藏AMI描述 */
}

.ami-id {
    display: none; /* 隐藏AMI ID */
}

/* AWS账户选择器样式 */
.account-select-container {
    position: relative;
}

.account-search {
    border-radius: 6px;
    border: 1px solid #ced4da;
    cursor: pointer;
    background-color: white; /* 确保背景是白色 */
}

.account-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 120px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.account-option:hover {
    background-color: #f8f9fa;
}

.account-option:last-child {
    border-bottom: none;
}

.account-option.highlighted {
    background-color: #e3f2fd;
}

/* 统一下拉框样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: white;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: 0;
}

.form-control:hover {
    border-color: #adb5bd;
}

select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}



/* OS卡片禁用状态 */
.os-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.os-card.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 密码输入框样式 */
.password-input-wrapper {
    position: relative;
}

.password-input-wrapper input {
    padding-right: 45px;
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    font-size: 16px;
    transition: color 0.3s ease;
    z-index: 10;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle-btn:hover {
    color: #495057;
}

.password-toggle-btn:focus {
    outline: none;
}

.instance-type-search {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.instance-type-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.instance-type-dropdown.show {
    display: block;
}

.instance-type-option {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.instance-type-option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.instance-type-option:last-child {
    border-bottom: none;
}

.instance-type-name {
    font-weight: 600;
    color: #2c3e50;
}

.instance-type-specs {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 选中的实例类型高亮 */
.instance-type-option.selected {
    background: rgba(102, 126, 234, 0.15);
    border-left: 4px solid #667eea;
}

.instance-type-option.selected .instance-type-name {
    color: #667eea;
    font-weight: 700;
}

/* 搜索匹配项高亮 
.instance-type-option.match {
    background: rgba(255, 193, 7, 0.1);
}*/

/* 搜索时隐藏非匹配项 */
.instance-type-dropdown.searching .instance-type-option:not(.match) {
    display: none;
}

/* 当没有搜索时，显示所有项 */
.instance-type-dropdown:not(.searching) .instance-type-option {
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
    background: linear-gradient(135deg, #a0a0a0, #888888);
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
}

.btn-loading .spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

.btn-secondary {
    background: #6c757d;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-label {
    color: #2c3e50;
    font-weight: 500;
}

@media (max-width: 768px) {
    .os-selection-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
    }

    .steps-indicator {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .step-connector {
        display: none;
    }

    .main-card {
        margin: 0 1rem;
        padding: 1rem;
    }

    .header-title {
        font-size: 2rem;
    }
}

/* 批量模式样式 */
.batch-mode-display {
    margin-bottom: 1rem;
}

.bg-light-info {
    background-color: #e3f2fd !important;
}

.batch-accounts-details .card {
    transition: all 0.2s ease;
}

.batch-accounts-details .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}

/* 实例类型搜索样式 */
.instance-type-search {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.instance-type-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.instance-type-dropdown.show {
    display: block;
}

.instance-type-option {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.instance-type-option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.instance-type-option:last-child {
    border-bottom: none;
}

.instance-type-name {
    font-weight: 600;
    color: #2c3e50;
}

.instance-type-specs {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 选中的实例类型高亮 */
.instance-type-option.selected {
    background: rgba(102, 126, 234, 0.15);
    border-left: 4px solid #667eea;
}

.instance-type-option.selected .instance-type-name {
    color: #667eea;
    font-weight: 700;
}

/* 搜索时隐藏非匹配项 */
.instance-type-dropdown.searching .instance-type-option:not(.match) {
    display: none;
}

/* 当没有搜索时，显示所有项 */
.instance-type-dropdown:not(.searching) .instance-type-option {
    display: block;
}

/* 卷类型相关样式 */
.volume-type-info {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.volume-type-info h6 {
    color: #667eea;
    margin-bottom: 0.5rem;
}

.volume-type-info p {
    margin-bottom: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* OS卡片禁用状态 */
.os-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.os-card.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 密码输入框样式 */
.password-input-group {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    z-index: 10;
}

.password-toggle-btn:hover {
    color: #667eea;
}

.password-toggle-btn:focus {
    outline: none;
}
</style>
@endpush

@section('content')
<div class="container-fluid px-0">
    <div class="row g-0">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="bi bi-servers me-2"></i>
                                批量创建EC2实例
                            </h4>
                            <p class="text-muted mb-0 mt-1">为多个账户批量配置云服务器实例</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <a href="{{ route('aws-ec2.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <div class="card-body">

            <!-- 步骤指示器 -->
            <div class="steps-indicator">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-label">选择系统</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-label">配置实例</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">存储配置</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">网络安全</div>
                </div>
            </div>

            <!-- 表单内容 -->
            <div class="content-section">
                <form id="createInstanceForm" action="{{ route('aws-ec2.batch-store') }}" method="POST">
                    @csrf

                    <!-- 基础配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cog section-icon"></i>
                            基础配置
                        </h3>

                        <!-- 批量账户显示 -->
                        <div class="batch-accounts-container mb-4" id="batchAccountsContainer">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-people-fill me-2"></i>
                                        已选择 <span id="selectedAccountCount">0</span> 个账户进行批量创建
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="selected-accounts-list" id="selectedAccountsList">
                                        <!-- 账户列表将通过JavaScript动态加载 -->
                                    </div>
                                    <div class="mt-3 text-end">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.location.href='{{ route('aws-ec2.index') }}'">
                                            <i class="bi bi-arrow-left me-1"></i>重新选择账户
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 隐藏的账户ID列表 -->
                        <input type="hidden" id="batch_account_ids" name="batch_account_ids">

                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <label for="region" class="form-label">地区 *</label>
                                <select class="form-control" id="region" name="region" required>
                                    <option value="">请选择地区</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 操作系统选择 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-desktop section-icon"></i>
                            选择操作系统
                        </h3>
                        <div class="os-selection-grid" id="batchOsSelectionGrid">
                            <!-- 操作系统选项将通过JavaScript从后端动态加载 -->
                            <div class="loading-placeholder" id="batchOsLoadingPlaceholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                正在加载操作系统...
                            </div>
                        </div>

                        <!-- AMI版本选择 -->
                        <div class="ami-selection" id="amiSelection">
                            <h4 class="mb-3">选择系统版本</h4>
                            <div class="ami-grid" id="amiGrid">
                                <!-- AMI选项将通过JavaScript动态加载 -->
                            </div>
                        </div>

                        <input type="hidden" id="selected_os" name="selected_os">
                        <input type="hidden" id="ami_id" name="ami_id">
                    </div>

                    <!-- 实例配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs section-icon"></i>
                            实例配置
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="instance_name" class="form-label">实例名称 *</label>
                                <input type="text" class="form-control" id="instance_name" name="instance_name" required placeholder="输入实例名称">
                            </div>
                            <div class="col-md-6">
                                <label for="instance_type" class="form-label">实例类型 *</label>
                                <div class="instance-type-search">
                                    <input type="text" class="search-input" id="instance_type" name="instance_type" placeholder="搜索实例类型，如：t3.micro" required>
                                    <div class="instance-type-dropdown" id="instanceTypeDropdown">
                                        <!-- 实例类型选项将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="instance_count" class="form-label">实例数量 *</label>
                                <input type="number" class="form-control" id="instance_count" name="instance_count" required min="1" max="20" value="1" placeholder="输入实例数量">
                                <small class="text-muted">可创建1-20个实例</small>
                            </div>
                        </div>
                    </div>

                    <!-- 存储配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-hdd section-icon"></i>
                            存储配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 存储配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>IOPS</strong>：每秒输入/输出操作数，决定磁盘的读写性能。数值越高，磁盘响应越快</li>
                                <li><strong>吞吐量 (MiB/s)</strong>：每秒传输的数据量，影响大文件传输速度。适用于需要高带宽的应用</li>
                                <li><strong>gp3</strong>：最新通用SSD，性价比最高，可独立配置IOPS和吞吐量</li>
                                <li><strong>io1/io2</strong>：高性能SSD，适用于数据库等对IOPS要求极高的应用</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="volume_size" class="form-label">根卷大小 (GB) *</label>
                                <input type="number" class="form-control" id="volume_size" name="volume_size" value="30" min="8" max="16384" required>
                            </div>
                            <div class="col-md-3">
                                <label for="volume_type" class="form-label">卷类型 *</label>
                                <select class="form-control" id="volume_type" name="volume_type" required>
                                    <option value="gp3">gp3 (通用SSD)</option>
                                    <option value="gp2">gp2 (通用SSD)</option>
                                    <option value="io1">io1 (预配置IOPS SSD)</option>
                                    <option value="io2">io2 (预配置IOPS SSD)</option>
                                    <option value="st1">st1 (吞吐量优化HDD)</option>
                                    <option value="sc1">sc1 (冷HDD)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="iops" class="form-label">IOPS</label>
                                <input type="number" class="form-control" id="iops" name="iops" placeholder="自动" min="100" max="64000">
                            </div>
                            <div class="col-md-3">
                                <label for="throughput" class="form-label">吞吐量 (MiB/s)</label>
                                <input type="number" class="form-control" id="throughput" name="throughput" placeholder="自动" min="125" max="1000">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="hidden" name="delete_on_termination" value="0">
                                    <input class="form-check-input" type="checkbox" id="delete_on_termination" name="delete_on_termination" value="1" checked>
                                    <label class="form-check-label" for="delete_on_termination">
                                        终止时删除卷
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="encrypted" name="encrypted">
                                    <label class="form-check-label" for="encrypted">
                                        加密卷
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 网络配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-network-wired section-icon"></i>
                            网络配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 网络配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>子网 (Subnet)</strong>：VPC内的网络分段，决定实例的网络位置和可用区。公有子网可访问互联网，私有子网仅内网访问</li>
                                <li><strong>安全组</strong>：虚拟防火墙，控制实例的入站和出站流量规则</li>
                                <li><strong>公网IP</strong>：是否为实例分配可从互联网访问的IP地址</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="subnet_id" class="form-label">子网</label>
                                <select class="form-control" id="subnet_id" name="subnet_id">
                                    <option value="">请先选择账户和地区</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="security_group_ids" class="form-label">安全组 *</label>
                                <select class="form-control" id="security_group_ids" name="security_group_ids[]" multiple required>
                                    <option value="">请先选择账户和地区</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="hidden" name="associate_public_ip" value="0">
                                    <input class="form-check-input" type="checkbox" id="associate_public_ip" name="associate_public_ip" value="1" checked>
                                    <label class="form-check-label" for="associate_public_ip">
                                        分配公网IP
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 密钥对和密码配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-key section-icon"></i>
                            访问配置
                        </h3>
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle"></i> <strong>访问配置要求：</strong>密钥对和登录密码至少需要配置一个，用于实例登录访问
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="key_name" class="form-label">密钥对 <span class="text-muted">(二选一)</span></label>
                                <select class="form-control" id="key_name" name="key_name">
                                    <option value="">请先选择账户和地区</option>
                                </select>
                                <small class="form-text text-muted">推荐使用密钥对，更安全</small>
                            </div>
                            <div class="col-md-6">
                                <label for="password" class="form-label">登录密码 <span class="text-muted">(二选一)</span></label>
                                <div class="password-input-wrapper">
                                    <input type="password" class="form-control" id="password" name="password" placeholder="设置登录密码（至少8位）" minlength="8">
                                    <button type="button" class="password-toggle-btn" id="passwordToggle">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">Windows系统为Administrator密码，Linux系统为root密码</small>
                            </div>
                        </div>
                    </div>

                    <!-- 高级配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-sliders-h section-icon"></i>
                            高级配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 高级配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>启用详细监控</strong>：提供更详细的CloudWatch监控指标，1分钟间隔（默认5分钟），需额外费用</li>
                                <li><strong>EBS优化</strong>：为实例提供专用的EBS带宽，提高存储性能，适用于I/O密集型应用</li>
                                <li><strong>终止保护</strong>：防止意外删除实例，启用后需要先禁用保护才能终止实例</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="monitoring" name="monitoring">
                                    <label class="form-check-label" for="monitoring">
                                        启用详细监控
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ebs_optimized" name="ebs_optimized">
                                    <label class="form-check-label" for="ebs_optimized">
                                        EBS优化
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="termination_protection" name="termination_protection">
                                    <label class="form-check-label" for="termination_protection">
                                        终止保护
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 开机脚本 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs section-icon"></i>
                            开机脚本
                        </h3>

                        <!-- 脚本配置功能卡片 - 显眼位置 -->
                        <div class="card border-primary mb-4" style="border-width: 2px;" id="batchUserDataCard">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    脚本配置
                                    <span class="badge bg-light text-primary ms-2">批量功能</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle"></i> 批量说明：</h6>
                                            <ul class="mb-0 small">
                                                <li><strong>统一脚本</strong>：所有账户的实例使用相同脚本</li>
                                                <li><strong>自动执行</strong>：每个实例首次启动时执行</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb"></i> 适用场景：</h6>
                                            <ul class="mb-0 small">
                                                <li>批量部署相同环境和软件</li>
                                                <li>统一安全配置和系统设置</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="batch_enable_user_data" name="enable_user_data" style="transform: scale(1.2);">
                                            <label class="form-check-label fw-bold" for="batch_enable_user_data" style="margin-left: 10px;">
                                                启用脚本配置功能
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div id="batchUserDataSection" style="display: none;">
                                    <hr class="my-3">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="batch_user_data_template" class="form-label fw-bold">选择预设模板</label>
                                            <select class="form-control" id="batch_user_data_template" name="user_data_template">
                                                <option value="">请选择模板...</option>
                                            </select>
                                            <small class="text-muted">选择常用的预设脚本模板</small>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">脚本大小监控</label>
                                            <div class="form-control-plaintext">
                                                <span id="batch_script_size" class="fw-bold">0</span> / 16,384 字符
                                                <div class="progress mt-1" style="height: 6px;">
                                                    <div class="progress-bar" id="batch_script_progress" role="progressbar" style="width: 0%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <label for="batch_user_data" class="form-label fw-bold">脚本内容</label>
                                            <textarea class="form-control" id="batch_user_data" name="user_data" rows="10"
                                                      placeholder="请输入脚本内容，例如：&#10;#!/bin/bash&#10;yum update -y&#10;yum install -y docker&#10;systemctl start docker"
                                                      style="font-family: 'Courier New', monospace; font-size: 13px;"></textarea>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-lightbulb text-primary"></i>
                                                    <strong>提示：</strong>Linux脚本以 <code>#!/bin/bash</code> 开头，Windows脚本使用 <code>&lt;powershell&gt;</code> 标签
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-secondary me-3" onclick="history.back()">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </button>
                        <button type="submit" class="btn btn-primary" id="createInstanceBtn">
                            <span class="btn-text">
                                <i class="fas fa-rocket me-2"></i>创建实例
                            </span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                创建中...
                            </span>
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩层 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 class="mb-3">正在批量创建EC2实例</h5>
            <p class="text-muted mb-3" id="progressText">正在处理请求...</p>
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
// 优化错误消息处理（全局函数）
function getOptimizedErrorMessage(message) {
    if (!message) return '操作失败，请检查网络连接或稍后重试';

    // 检查是否包含验证中的消息
    if (message.includes('Your request for accessing resources in this region is being validated')) {
        return '请求正在验证中';
    }

    // 检查是否包含vCPU限制的消息
    if (message.includes('You have requested more vCPU capacity than') || message.includes('VcpuLimitExceeded')) {
        return '开通的vCPU超过限制';
    }

    // 检查子网相关错误
    if (message.includes('InvalidSubnetID.NotFound') || message.includes('does not exist')) {
        return '子网配置错误';
    }

    // 检查安全组相关错误
    if (message.includes('InvalidGroup.NotFound') || (message.includes('security group') && message.includes('does not exist'))) {
        return '安全组配置错误';
    }

    // 检查其他常见AWS错误
    if (message.includes('InvalidAMIID.NotFound')) {
        return 'AMI镜像不存在';
    }

    if (message.includes('UnauthorizedOperation')) {
        return '权限不足';
    }

    if (message.includes('InvalidParameterValue')) {
        return '参数配置错误';
    }

    // 如果错误信息太长，截取前30个字符
    if (message.length > 30) {
        return message.substring(0, 30) + '...';
    }

    // 返回原始消息
    return message;
}

$(document).ready(function() {
    let instanceTypes = [];
    let currentAMIs = [];
    let preloadedAMIs = {}; // 预加载的AMI缓存
    let backendAmiData = null; // 后端AMI数据

    // 使用统一数据管理器加载AMI数据
    awsDataManager.getAmiData(function(amiData) {
        if (amiData) {
            window.backendAmiData = amiData; // 确保全局可访问
            console.log('✅ 批量创建页面：统一数据管理器AMI数据加载成功');
            initializeBatchPageWithBackendData();
        } else {
            console.error('❌ 批量创建页面：统一数据管理器AMI数据加载失败');
            showBatchErrorMessage('数据加载失败，请刷新页面重试');
        }
    });

    // 设置CSRF token
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // 初始化批量账户
    initializeBatchAccounts();

    // 初始化AMI数据管理器
    initializeAMIDataManager();

    // 页面加载时就加载实例类型（不依赖地区选择）
    loadInstanceTypes();
    // 初始化批量模式功能
    initBatchMode();
    // 初始化OS卡片状态检查
    checkOSCardStatus();

    // 批量账户处理
    function initializeBatchAccounts() {
        console.log('🔄 开始初始化批量账户...');

        // 从URL参数获取账户ID
        const urlParams = new URLSearchParams(window.location.search);
        const accountsParam = urlParams.get('accounts');

        console.log('📋 URL参数 accounts:', accountsParam);

        if (accountsParam) {
            const accountIds = accountsParam.split(',');
            $('#batch_account_ids').val(accountIds.join(','));

            // 获取账户信息并显示
            loadBatchAccountsInfo(accountIds);
        } else {
            // 如果没有账户参数，重定向到主页
            window.location.href = '{{ route("aws-ec2.index") }}';
        }
    }

    // 加载批量账户信息
    function loadBatchAccountsInfo(accountIds) {
        $.ajax({
            url: '{{ route("aws-accounts.get-by-ids") }}',
            method: 'POST',
            data: {
                account_ids: accountIds,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success && response.accounts) {
                    displayBatchAccounts(response.accounts);

                    // 预加载所有账户的相关数据（子网、安全组、密钥对）
                    preloadBatchAccountsData(response.accounts);
                } else {
                    console.error('❌ 响应格式错误:', response);
                    alert('账户信息格式错误');
                }
            },
            error: function(xhr, status, error) {
                console.error('加载账户信息失败:', error);
                alert('加载账户信息失败，请重新选择');
                window.location.href = '{{ route("aws-ec2.index") }}';
            }
        });
    }

    // 显示批量账户
    function displayBatchAccounts(accounts) {
        $('#selectedAccountCount').text(accounts.length);

        const maxDisplayCount = 6; // 最多显示6个账户
        const displayAccounts = accounts.slice(0, maxDisplayCount);
        const remainingCount = accounts.length - maxDisplayCount;

        let accountsHtml = '<div class="row g-2">';

        // 显示前6个账户
        displayAccounts.forEach(function(account, index) {
            const displayName = account.account_name.includes('@') ?
                account.account_name.split('@')[0] : account.account_name;

            accountsHtml += `
                <div class="col-md-4 col-sm-6">
                    <div class="card border-success h-100">
                        <div class="card-body p-2 text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-person-circle text-success me-2"></i>
                                <small class="fw-bold text-truncate" title="${displayName}">${displayName}</small>
                            </div>
                            <small class="text-muted">账户 ${index + 1}</small>
                        </div>
                    </div>
                </div>
            `;
        });

        // 如果有更多账户，显示"查看更多"按钮
        if (remainingCount > 0) {
            accountsHtml += `
                <div class="col-md-4 col-sm-6">
                    <div class="card border-info h-100">
                        <div class="card-body p-2 text-center d-flex align-items-center justify-content-center">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="showAllAccounts()">
                                <i class="bi bi-three-dots me-1"></i>
                                还有 ${remainingCount} 个账户
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        accountsHtml += '</div>';
        $('#selectedAccountsList').html(accountsHtml);

        // 存储所有账户数据供详细查看使用
        window.allBatchAccounts = accounts;
    }







    // 获取当前表单数据
    function getCurrentFormData() {
        const formData = new FormData($('#createInstanceForm')[0]);
        return formData;
    }



    // 全局变量
    let amiDataReady = false;

    // 初始化AMI数据管理器
    function initializeAMIDataManager() {
        console.log('🔄 开始初始化AMI数据管理器...');

        // 检查AMI数据管理器是否已加载
        function checkAmiDataManager() {
            if (window.awsAmiDataManager) {
                console.log('✅ AMI数据管理器已找到');

                // 监听数据加载完成
                window.awsAmiDataManager.onDataReady(function(data) {
                    amiDataReady = true;
                    console.log('✅ AMI数据已就绪，开始初始化页面');
                    initializePageWithAmiData();
                });

                // 检查数据是否已经加载完成
                try {
                    const status = window.awsAmiDataManager.getStatus();
                    if (status && status.loaded) {
                        amiDataReady = true;
                        console.log('✅ AMI数据已加载，直接初始化页面');
                        initializePageWithAmiData();
                    } else {
                        console.log('⏳ AMI数据尚未加载完成，等待数据就绪...');
                    }
                } catch (error) {
                    console.log('⏳ AMI数据管理器方法调用失败，等待数据就绪...', error);
                }
            } else {
                console.log('⏳ 等待AMI数据管理器加载...');
                // 延迟100ms后重试
                setTimeout(checkAmiDataManager, 100);
            }
        }

        checkAmiDataManager();
    }

    // 使用AMI数据初始化页面
    function initializePageWithAmiData() {
        try {
            // 加载地区列表
            loadRegionsFromAmiData();
        } catch (error) {
            console.error('❌ 页面初始化失败:', error);
        }
    }

    // 从AMI数据加载地区列表（与create页面保持一致）
    function loadRegionsFromAmiData() {
        if (!window.awsAmiDataManager) {
            console.log('⏳ AMI数据管理器未就绪，跳过地区加载');
            return;
        }

        try {
            const regions = window.awsAmiDataManager.getRegions();
            const regionSelect = $('#region');

            if (regionSelect.length && regions) {
                regionSelect.empty().append('<option value="">请选择地区</option>');

                // 使用与create页面完全相同的地区加载逻辑
                Object.values(regions).forEach(region => {
                    regionSelect.append(`<option value="${region.code}">${region.name}</option>`);
                });

                console.log(`✅ 已加载 ${Object.keys(regions).length} 个地区`);
            } else {
                console.log('⚠️ 地区数据为空或地区选择框未找到');
            }
        } catch (error) {
            console.error('❌ 加载地区列表失败:', error);
        }
    }




    // 地区选择变化时处理（优化：减少API请求，优先使用静态数据）
    $('#region').change(function() {
        const region = $(this).val();
        const batchAccountIds = $('#batch_account_ids').val();

        console.log('🔍 地区选择变化事件触发:', {region, batchAccountIds});

        // 使用后端AMI数据更新系统显示（与create页面保持一致）
        if (window.backendAmiData) {
            updateSystemsForRegion(region);
            console.log('✅ 地区选择变化，已更新系统显示:', region);
        } else {
            console.log('⚠️ 后端AMI数据尚未加载，跳过系统筛选');
        }
        if (region && batchAccountIds) {
            const accountIds = batchAccountIds.split(',');
            console.log('✅ 条件满足，开始使用统一数据管理器加载数据:', {accountIds: accountIds, region});

            // 对于批量创建，由于不同账户有不同的VPC/子网配置，使用默认子网
            $('#subnet_id').html(`
                <option value="">请选择子网</option>
                <option value="default" selected>使用默认子网（推荐）</option>
                <option value="" disabled>--- 批量创建建议使用默认子网 ---</option>
            `);
            // 批量创建建议使用默认安全组，不加载特定账户的安全组
            $('#security_group_ids').html(`
                <option value="">请选择安全组</option>
                <option value="default" selected>使用默认安全组（推荐）</option>
                <option value="" disabled>--- 批量创建建议使用默认安全组 ---</option>
            `);

            // 使用统一数据管理器加载第一个账户的密钥对
            awsDataManager.getKeyPairs(accountIds[0], region, function(keyPairs) {
                console.log('🔍 batch-create页面密钥对回调被调用:', keyPairs);
                updateBatchKeyPairOptions(keyPairs);
            });
        } else {
            console.log('⚠️ 条件不满足，清除依赖字段:', {region, batchAccountIds});
            clearDependentFields();
        }
    });
    // 已删除loadKeyPairsOnDemand函数，现在使用统一数据管理器预加载

    // 已删除点击加载密钥对的代码，现在在账户预加载时自动加载
    // 操作系统选择（使用事件委托支持动态加载的元素）
    $('#batchOsSelectionGrid').on('click', '.os-card', function() {
        // 检查卡片是否被禁用或隐藏
        if ($(this).hasClass('disabled') || $(this).is(':hidden')) {
            return false;
        }

        // 检查是否已选择地区
        const region = $('#region').val();
        const batchAccountIds = $('#batch_account_ids').val();

        if (!region) {
            alert('请先选择地区');
            return false;
        }

        if (!batchAccountIds) {
            alert('请先选择账户');
            return false;
        }

        // 移除其他卡片的选中状态
        $('.os-card').removeClass('selected');
        // 添加选中状态
        $(this).addClass('selected');

        // 获取选中的操作系统
        const selectedOS = $(this).data('os');
        console.log('✅ 已选择操作系统:', selectedOS);

        // 更新隐藏字段
        $('#selected_os').val(selectedOS);

        // 触发系统版本更新（使用后端数据）
        loadBatchSystemVersions(region, selectedOS);
    });

    // AMI版本选择事件处理（与create页面保持一致）
    $(document).on('click', '.ami-card', function() {
        // 移除其他卡片的选中状态
        $('.ami-card').removeClass('selected');
        // 添加选中状态
        $(this).addClass('selected');

        // 获取选中的AMI ID
        const selectedAmiId = $(this).data('ami-id');
        $('#ami_id').val(selectedAmiId);

        console.log('✅ 已选择AMI:', selectedAmiId);
    });

    // 根据系统更新版本列表（与create页面保持一致）
    function updateVersionsForSystem(regionCode, systemName) {
        if (!window.backendAmiData || !regionCode || !systemName) {
            $('#amiSelection').hide();
            return;
        }

        const regionData = window.backendAmiData.regions[regionCode];
        if (!regionData || !regionData.systems || !regionData.systems[systemName]) {
            $('#amiSelection').hide();
            return;
        }

        const versions = regionData.systems[systemName].versions;

        if (versions && versions.length > 0) {
            // 使用卡片网格显示版本（与create页面保持一致）
            let html = '';
            versions.forEach(version => {
                html += `
                    <div class="ami-card" data-ami-id="${version.ami_id}">
                        <div class="ami-name">${version.display_name}</div>
                        <div class="ami-description">${version.name}</div>
                        <div class="ami-id">${version.ami_id}</div>
                    </div>
                `;
            });
            $('#amiGrid').html(html);

            // 显示版本选择区域
            $('#amiSelection').show();

            // 默认选择第一个版本
            if (versions.length > 0) {
                $('#ami_id').val(versions[0].ami_id);
                $('#selected_os').val(systemName);
                // 默认选中第一个卡片
                $('#amiGrid .ami-card').first().addClass('selected');
            }

            console.log(`✅ 已加载 ${versions.length} 个 ${systemName} 版本`);
        } else {
            $('#amiSelection').hide();
            console.log(`⚠️ 地区 ${regionCode} 不支持系统 ${systemName}`);
        }
    }

    // 兼容旧的函数名
    function updateSystemVersions(osType, region) {
        updateVersionsForSystem(region, osType);
    }

    // 根据地区筛选操作系统（与create页面保持一致）
    function updateSystemsForRegion(regionCode) {
        if (!window.backendAmiData || !regionCode) {
            // 隐藏所有系统
            $('.os-card').hide();
            return;
        }

        const regionData = window.backendAmiData.regions[regionCode];
        if (!regionData || !regionData.systems) {
            $('.os-card').hide();
            return;
        }

        const supportedSystems = Object.keys(regionData.systems);
        const currentSelectedOS = $('#selected_os').val();

        $('.os-card').each(function() {
            const systemName = $(this).data('os');
            const isSupported = supportedSystems.includes(systemName);

            if (isSupported) {
                $(this).show().removeClass('disabled');
            } else {
                $(this).hide().addClass('disabled');
                // 如果当前选择的操作系统在新地区不支持，清空选择
                if (systemName === currentSelectedOS) {
                    $('.os-card').removeClass('selected');
                    $('#selected_os').val('');
                    $('#ami_id').val('');
                    $('#amiSelection').hide();
                    console.log(`⚠️ 当前选择的操作系统 ${systemName} 在地区 ${regionCode} 不支持，已清空选择`);
                }
            }
        });

        console.log(`✅ 地区 ${regionCode} 支持 ${supportedSystems.length} 个系统:`, supportedSystems);
    }

    // 兼容旧的函数名
    function filterOSByRegion(region) {
        updateSystemsForRegion(region);
    }

    // 检查OS卡片状态
    function checkOSCardStatus() {
        const region = $('#region').val();
        const batchAccountIds = $('#batch_account_ids').val();

        if (!region || !batchAccountIds) {
            $('.os-card').hide(); // 隐藏所有系统
            console.log('⏳ 地区或账户未选择，隐藏所有操作系统卡片');
        } else {
            // 如果地区和账户都已选择，重新执行地区筛选
            updateSystemsForRegion(region);
        }
    }

    // 初始化批量模式功能
    function initBatchMode() {
        // 批量模式的初始化逻辑
        console.log('✅ 批量模式初始化完成');

        // 可以在这里添加批量模式特有的功能
        // 例如：批量操作按钮、批量状态显示等
    }

    // 加载实例类型列表
    function loadInstanceTypes() {
        $.ajax({
            url: '{{ route("aws-ec2.instance-types") }}',
            method: 'GET',
            success: function(response) {
                instanceTypes = response;
                renderInstanceTypes(response);
                console.log('✅ 实例类型加载完成');
            },
            error: function(xhr, status, error) {
                console.error('❌ 加载实例类型失败:', xhr.responseText);
            }
        });
    }

    // 渲染实例类型列表
    function renderInstanceTypes(types) {
        const currentValue = $('#instance_type').val();
        let html = '';

        types.forEach(function(type) {
            const isSelected = type.name === currentValue;
            const selectedClass = isSelected ? 'selected' : '';

            html += `
                <div class="instance-type-option ${selectedClass}" data-type="${type.name}">
                    <div class="instance-type-name">${type.name}</div>
                    <div class="instance-type-specs">${type.specs}</div>
                </div>
            `;
        });
        $('#instanceTypeDropdown').html(html);
    }

    // 过滤实例类型（搜索时显示匹配结果，无搜索时显示完整列表）
    function filterInstanceTypes(searchTerm) {
        const currentValue = $('#instance_type').val();
        const isSearching = searchTerm.trim() !== '';
        let html = '';

        // 添加或移除搜索状态类
        if (isSearching) {
            $('#instanceTypeDropdown').addClass('searching');
        } else {
            $('#instanceTypeDropdown').removeClass('searching');
        }

        instanceTypes.forEach(function(type) {
            const isMatch = !isSearching ||
                           type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           type.specs.toLowerCase().includes(searchTerm.toLowerCase());
            const isSelected = type.name === currentValue;

            const matchClass = isMatch ? 'match' : '';
            const selectedClass = isSelected ? 'selected' : '';

            html += `
                <div class="instance-type-option ${matchClass} ${selectedClass}" data-type="${type.name}">
                    <div class="instance-type-name">${type.name}</div>
                    <div class="instance-type-specs">${type.specs}</div>
                </div>
            `;
        });

        $('#instanceTypeDropdown').html(html);
        $('#instanceTypeDropdown').addClass('show');
    }

    // 实例类型搜索事件
    $('#instance_type').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterInstanceTypes(searchTerm);
    });

    // 实例类型选择事件
    $(document).on('click', '.instance-type-option', function() {
        const instanceType = $(this).data('type');
        $('#instance_type').val(instanceType);

        // 更新选中状态
        $('.instance-type-option').removeClass('selected');
        $(this).addClass('selected');

        $('#instanceTypeDropdown').removeClass('show');
        console.log('✅ 已选择实例类型:', instanceType);
    });

    // 实例类型输入框焦点事件
    $('#instance_type').on('focus', function() {
        $('#instanceTypeDropdown').addClass('show');
        if (instanceTypes.length > 0) {
            filterInstanceTypes($(this).val());
        }
    });

    // 点击其他地方隐藏下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.instance-type-search').length) {
            $('#instanceTypeDropdown').removeClass('show');
        }
    });

    // 其他必要的函数定义...

    // 重新选择账户按钮点击事件
    $('#reselect-accounts').click(function() {
        if (confirm('确定要重新选择账户吗？当前的选择将被清除。')) {
            // 清除Session中的批量选择
            fetch('{{ route("aws-ec2.clear-batch-selection") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到账户选择页面
                    window.location.href = '{{ route("aws-ec2.index") }}';
                } else {
                    alert('清除选择失败，请重试');
                }
            })
            .catch(error => {
                console.error('清除选择失败:', error);
                alert('清除选择失败，请重试');
            });
        }
    });

    // 表单提交处理
    $('#createInstanceForm').on('submit', function(e) {
        e.preventDefault(); // 阻止默认表单提交

        const submitBtn = $('#createInstanceBtn');
        const originalText = submitBtn.find('.btn-text').html();

        // 禁用按钮并显示加载状态
        submitBtn.prop('disabled', true);
        submitBtn.find('.btn-text').addClass('d-none');
        submitBtn.find('.btn-loading').removeClass('d-none');

        // 显示进度条
        const progressInterval = showBatchLoadingProgress();

        // 收集表单数据
        const formData = new FormData(this);

        // 调试：检查关键字段的值
        console.log('表单提交数据检查:');
        console.log('selected_os:', $('#selected_os').val());
        console.log('ami_id:', $('#ami_id').val());
        console.log('region:', $('#region').val());
        console.log('batch_account_ids:', $('#batch_account_ids').val());
        console.log('instance_name:', $('#instance_name').val());
        console.log('instance_type:', $('#instance_type').val());

        // 调试：检查操作系统选择状态
        const selectedOSCard = $('.os-card.selected');
        console.log('选中的操作系统卡片数量:', selectedOSCard.length);
        if (selectedOSCard.length > 0) {
            console.log('选中的操作系统:', selectedOSCard.data('os'));
        }

        // 调试：打印FormData内容
        console.log('FormData内容:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }

        // 确保关键字段有值
        if (!$('#selected_os').val()) {
            alert('请选择操作系统');
            // 隐藏进度条
            hideBatchLoadingProgress(progressInterval);
            // 恢复按钮状态
            submitBtn.prop('disabled', false);
            submitBtn.find('.btn-text').removeClass('d-none').html(originalText);
            submitBtn.find('.btn-loading').addClass('d-none');
            return;
        }

        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // 隐藏进度条
                hideBatchLoadingProgress(progressInterval);

                // 恢复按钮状态
                submitBtn.prop('disabled', false);
                submitBtn.find('.btn-text').removeClass('d-none').html(originalText);
                submitBtn.find('.btn-loading').addClass('d-none');

                if (response.success) {
                    // 显示批量创建结果
                    showBatchCreateResults(response);
                } else {
                    alert('批量创建失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr) {
                // 隐藏进度条
                hideBatchLoadingProgress(progressInterval);

                // 恢复按钮状态
                submitBtn.prop('disabled', false);
                submitBtn.find('.btn-text').removeClass('d-none').html(originalText);
                submitBtn.find('.btn-loading').addClass('d-none');

                const response = xhr.responseJSON;
                let errorMessage = '批量创建失败，请检查网络连接或稍后重试';

                if (response && response.message) {
                    // 检查是否是代理异常错误
                    if (response.message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
                        response.message.includes('当前代理模式IP异常，请切换其他模式进行操作')) {
                        // 显示代理异常提示
                        if (typeof showProxyToast === 'function') {
                            showProxyToast('error', response.message);
                        } else {
                            alert(response.message);
                        }
                        return; // 不刷新页面，让用户切换代理模式
                    }

                    errorMessage = '批量创建失败：' + response.message;
                } else if (xhr.status === 422 && response && response.errors) {
                    // 验证错误
                    const errors = Object.values(response.errors).flat();
                    errorMessage = '表单验证失败：' + errors.join(', ');
                }

                alert(errorMessage);
                console.error('批量创建错误:', xhr.responseText);
            }
        });
    });

    // 高级选项 - 开机脚本功能
    initBatchUserDataFeature();

    console.log('✅ 页面初始化完成');

    // 初始化批量创建开机脚本功能
    function initBatchUserDataFeature() {
        // 启用/禁用开机脚本
        $('#batch_enable_user_data').change(function() {
            if ($(this).is(':checked')) {
                $('#batchUserDataSection').slideDown();
                loadBatchUserDataTemplates();
            } else {
                $('#batchUserDataSection').slideUp();
                $('#batch_user_data').val('');
                updateBatchScriptSize();
            }
        });

        // 模板选择
        $('#batch_user_data_template').change(function() {
            const templateId = $(this).val();
            if (templateId) {
                const selectedOption = $(this).find('option:selected');
                const scriptContent = selectedOption.data('script');
                if (scriptContent) {
                    $('#batch_user_data').val(scriptContent);
                    updateBatchScriptSize();
                }
            }
        });

        // 点击下拉框时触发模板加载
        $('#batch_user_data_template').on('focus click', function() {
            if (!batchTemplatesLoaded && !batchTemplatesLoading) {
                loadBatchUserDataTemplates();
            }
        });

        // 脚本内容变化时更新大小显示
        $('#batch_user_data').on('input', function() {
            updateBatchScriptSize();
        });

        // 更新脚本大小显示
        function updateBatchScriptSize() {
            const content = $('#batch_user_data').val();
            const size = new Blob([content]).size;
            const maxSize = 16384; // 16KB
            const percentage = (size / maxSize) * 100;

            $('#batch_script_size').text(size);
            $('#batch_script_progress').css('width', Math.min(percentage, 100) + '%');

            // 根据大小改变进度条颜色
            const progressBar = $('#batch_script_progress');
            progressBar.removeClass('bg-success bg-warning bg-danger');
            if (percentage < 70) {
                progressBar.addClass('bg-success');
            } else if (percentage < 90) {
                progressBar.addClass('bg-warning');
            } else {
                progressBar.addClass('bg-danger');
            }

            // 如果超过限制，显示警告
            if (size > maxSize) {
                if (!$('#batch_size_warning').length) {
                    $('#batch_user_data').after('<div id="batch_size_warning" class="text-danger mt-1"><small>脚本大小超过16KB限制，请减少内容</small></div>');
                }
            } else {
                $('#batch_size_warning').remove();
            }
        }

        // 模板加载状态管理
        let batchTemplatesLoaded = false;
        let batchTemplatesLoading = false;

        // 加载开机脚本模板
        function loadBatchUserDataTemplates() {
            // 如果已经加载过或正在加载，直接返回
            if (batchTemplatesLoaded || batchTemplatesLoading) {
                return;
            }

            batchTemplatesLoading = true;
            const templateSelect = $('#batch_user_data_template');

            // 设置加载状态
            templateSelect.empty().append('<option value="">请选择模板...</option>');
            templateSelect.append('<option disabled>模板加载中......</option>');

            // 获取当前选择的操作系统类型
            const selectedOs = $('#selected_os').val();
            let osType = 'all';

            if (selectedOs) {
                if (selectedOs.toLowerCase().includes('windows')) {
                    osType = 'windows';
                } else {
                    osType = 'linux';
                }
            }

            $.get('{{ route("aws-ec2.user-data-templates") }}', { os_type: osType })
                .done(function(response) {
                    if (response.success && response.data) {
                        // 清空并重新填充
                        templateSelect.empty().append('<option value="">请选择模板...</option>');

                        // 按分类分组
                        const categories = {};
                        response.data.forEach(template => {
                            if (!categories[template.category]) {
                                categories[template.category] = [];
                            }
                            categories[template.category].push(template);
                        });

                        // 添加分组选项
                        Object.keys(categories).forEach(category => {
                            const optgroup = $('<optgroup></optgroup>');
                            optgroup.attr('label', getBatchCategoryName(category));

                            categories[category].forEach(template => {
                                const option = $('<option></option>');
                                option.attr('value', template.id);
                                option.attr('data-script', template.script_content);
                                option.attr('title', template.description);
                                option.text(template.name);
                                optgroup.append(option);
                            });
                            templateSelect.append(optgroup);
                        });

                        batchTemplatesLoaded = true;
                    }
                })
                .fail(function() {
                    console.error('加载开机脚本模板失败');
                    // 失败时显示错误信息
                    templateSelect.empty().append('<option value="">请选择模板...</option>');
                    templateSelect.append('<option disabled>模板加载失败，请重试</option>');
                })
                .always(function() {
                    batchTemplatesLoading = false;
                });
        }

        // 获取分类中文名称
        function getBatchCategoryName(category) {
            const categoryNames = {
                'development': '开发环境',
                'web': 'Web服务',
                'security': '安全配置',
                'basic': '基础配置',
                'custom': '自定义'
            };
            return categoryNames[category] || category;
        }
    }

    // 显示批量创建加载进度条
    function showBatchLoadingProgress() {
        document.getElementById('loadingOverlay').style.display = 'flex';
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        let progress = 1;
        let lastUpdate = Date.now();

        // 模拟进度
        const progressInterval = setInterval(() => {
            const now = Date.now();
            if (progress < 90 && (now - lastUpdate) >= 1000) {
                progress += Math.floor(Math.random() * 15) + 5;
                if (progress > 89) progress = 89;
                progressBar.style.width = progress + '%';

                // 更新进度文本
                if (progress < 30) {
                    progressText.textContent = '正在验证参数...';
                } else if (progress < 60) {
                    progressText.textContent = '正在批量创建实例...';
                } else {
                    progressText.textContent = '正在配置网络和安全组...';
                }
                lastUpdate = now;
            }
        }, 500);

        return progressInterval;
    }

    // 隐藏批量创建加载进度条
    function hideBatchLoadingProgress(progressInterval) {
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // 设置进度为100%
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        progressBar.style.width = '100%';
        progressText.textContent = '完成！';

        // 延迟隐藏
        setTimeout(() => {
            document.getElementById('loadingOverlay').style.display = 'none';
            progressBar.style.width = '0%';
            progressText.textContent = '正在处理请求...';
        }, 1000);
    }
});

// 全局函数定义（在jQuery ready外部，确保可以被onclick调用）

// 显示批量创建结果
function showBatchCreateResults(response) {
    const { results, summary } = response;

    // 创建结果模态框HTML
    const modalHtml = `
        <div class="modal fade" id="batchResultModal" tabindex="-1" aria-labelledby="batchResultModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="batchResultModalLabel">
                            <i class="fas fa-chart-bar me-2"></i>批量创建结果
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 汇总信息 -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h5 class="card-title text-info">总计</h5>
                                        <h3 class="text-primary">${summary.total}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h5 class="card-title text-success">成功</h5>
                                        <h3 class="text-success">${summary.success}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <h5 class="card-title text-danger">失败</h5>
                                        <h3 class="text-danger">${summary.failure}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细结果列表 -->
                        <div class="results-list">
                            ${results.map((result, index) => `
                                <div class="card mb-3 ${result.success ? 'border-success' : 'border-danger'}">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-1">账户${index + 1}: ${result.account_name}</h6>
                                                <small class="text-muted">ID: ${result.account_id}</small>
                                            </div>
                                            <div class="col-md-3">
                                                <span class="badge ${result.success ? 'bg-success' : 'bg-danger'} fs-6">
                                                    <i class="fas ${result.success ? 'fa-check' : 'fa-times'} me-1"></i>
                                                    ${result.success ? '成功' : '失败'}
                                                </span>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="showResultDetail(${index})">
                                                    <i class="fas fa-info-circle me-1"></i>详情
                                                </button>
                                            </div>
                                        </div>
                                        ${result.success && result.instance_ids ? `
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="fas fa-server me-1"></i>
                                                    实例ID: ${result.instance_ids.join(', ')}
                                                </small>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>关闭结果窗口
                        </button>
                        ${summary.failure > 0 ? `
                            <button type="button" class="btn btn-warning" onclick="retryFailedAccounts()">
                                <i class="fas fa-redo me-1"></i>重新提交失败的账户
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#batchResultModal').remove();

    // 添加新的模态框到页面
    $('body').append(modalHtml);

    // 存储结果数据供其他函数使用
    window.batchCreateResults = response;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchResultModal'));
    modal.show();

    // 更新账户状态
    updateAccountStatuses(results);
}

// 显示结果详情
function showResultDetail(index) {
    const result = window.batchCreateResults.results[index];

    let detailContent = '';
    if (result.success) {
        detailContent = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle me-2"></i>创建成功</h6>
                <p><strong>消息:</strong> ${result.message}</p>
                ${result.instance_ids ? `<p><strong>实例ID:</strong> ${result.instance_ids.join(', ')}</p>` : ''}
            </div>
        `;
    } else {
        detailContent = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>创建失败</h6>
                <p><strong>错误信息:</strong></p>
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0" style="white-space: pre-wrap; word-wrap: break-word;">${result.message}</pre>
                </div>
            </div>
        `;
    }

    const detailModalHtml = `
        <div class="modal fade" id="resultDetailModal" tabindex="-1" aria-labelledby="resultDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="resultDetailModalLabel">
                            <i class="fas fa-info-circle me-2"></i>账户${index + 1}详情 - ${result.account_name}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        ${detailContent}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>关闭
                        </button>
                        ${!result.success ? `
                            <button type="button" class="btn btn-primary" onclick="copyErrorMessage(${index})">
                                <i class="fas fa-copy me-1"></i>复制
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的详情模态框
    $('#resultDetailModal').remove();

    // 添加新的详情模态框
    $('body').append(detailModalHtml);

    // 显示详情模态框
    const detailModal = new bootstrap.Modal(document.getElementById('resultDetailModal'));
    detailModal.show();
}

// 复制错误信息
function copyErrorMessage(index) {
    const result = window.batchCreateResults.results[index];
    const message = result.message;
    const copyBtn = event.target.closest('button');

    // 使用现代的Clipboard API或者fallback到传统方法
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代Clipboard API
        navigator.clipboard.writeText(message).then(() => {
            showCopySuccess(copyBtn);
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(message, copyBtn);
        });
    } else {
        // 使用传统方法
        fallbackCopyTextToClipboard(message, copyBtn);
    }
}

// 传统复制方法
function fallbackCopyTextToClipboard(text, copyBtn) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(copyBtn);
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
    }

    document.body.removeChild(textArea);
}

// 显示复制成功提示
function showCopySuccess(copyBtn) {
    if (!copyBtn) return;

    const originalText = copyBtn.innerHTML;
    copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
    copyBtn.classList.remove('btn-primary');
    copyBtn.classList.add('btn-success');
    copyBtn.disabled = true;

    setTimeout(() => {
        copyBtn.innerHTML = originalText;
        copyBtn.classList.remove('btn-success');
        copyBtn.classList.add('btn-primary');
        copyBtn.disabled = false;
    }, 2000);
}

// 重新提交失败的账户
function retryFailedAccounts() {
    if (!window.batchCreateResults) {
        alert('没有找到批量创建结果数据');
        return;
    }

    // 获取失败的账户ID
    const failedAccountIds = window.batchCreateResults.results
        .filter(result => !result.success)
        .map(result => result.account_id);

    if (failedAccountIds.length === 0) {
        alert('没有失败的账户需要重新提交');
        return;
    }

    if (!confirm(`确定要重新提交 ${failedAccountIds.length} 个失败的账户吗？`)) {
        return;
    }

    // 关闭当前结果模态框
    bootstrap.Modal.getInstance(document.getElementById('batchResultModal')).hide();

    // 更新表单中的账户ID为失败的账户
    $('#batch_account_ids').val(failedAccountIds.join(','));

    // 显示重新提交的加载状态
    const retryBtn = event.target;
    const originalText = retryBtn.innerHTML;
    retryBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>重新提交中...';
    retryBtn.disabled = true;

    // 显示进度条
    const progressInterval = showBatchLoadingProgress();

    // 重新提交表单
    const formData = new FormData(document.getElementById('createInstanceForm'));

    $.ajax({
        url: $('#createInstanceForm').attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            // 隐藏进度条
            hideBatchLoadingProgress(progressInterval);

            if (response.success) {
                // 显示重新提交的结果
                showBatchCreateResults(response);
            } else {
                alert('重新提交失败：' + (response.message || '未知错误'));
            }
        },
        error: function(xhr) {
            // 隐藏进度条
            hideBatchLoadingProgress(progressInterval);

            const response = xhr.responseJSON;
            let errorMessage = '重新提交失败，请检查网络连接或稍后重试';

            if (response && response.message) {
                // 检查是否是代理异常错误
                if (response.message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
                    response.message.includes('当前代理模式IP异常，请切换其他模式进行操作')) {
                    // 显示代理异常提示
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', response.message);
                    } else {
                        alert(response.message);
                    }
                    return; // 不刷新页面，让用户切换代理模式
                }

                errorMessage = '重新提交失败：' + response.message;
            }

            alert(errorMessage);
            console.error('重新提交错误:', xhr.responseText);
        },
        complete: function() {
            // 恢复按钮状态
            retryBtn.innerHTML = originalText;
            retryBtn.disabled = false;
        }
    });
}

// 更新账户状态
function updateAccountStatuses(results) {
    results.forEach(result => {
        // 这里可以添加更新账户EC2开通状态的逻辑
        // 例如：调用API更新数据库中的账户状态
        if (result.success) {
            console.log(`✅ 账户 ${result.account_id} EC2开通成功`);
            // 可以在这里调用API更新账户的ec2_status为已开通
        } else {
            console.log(`❌ 账户 ${result.account_id} EC2开通失败`);
        }
    });
}

// 清除依赖字段
function clearDependentFields() {
    // 清除子网选择
    $('#subnet_id').html('<option value="">请先选择账户和地区</option>');

    // 清除安全组选择
    $('#security_group_ids').html('<option value="">请先选择账户和地区</option>');

    // 清除密钥对选择
    $('#key_name').html('<option value="">请先选择账户和地区</option>');

    console.log('✅ 已清除依赖字段');
}



// 显示所有账户的模态框（全局函数，供onclick调用）
function showAllAccounts() {
    if (!window.allBatchAccounts) {
        alert('账户数据未加载');
        return;
    }

    let modalHtml = `
        <div class="modal fade" id="allAccountsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-people-fill me-2"></i>
                            所有选中的账户 (${window.allBatchAccounts.length} 个)
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-2">
    `;

    window.allBatchAccounts.forEach(function(account, index) {
        const displayName = account.account_name.includes('@') ?
            account.account_name.split('@')[0] : account.account_name;

        modalHtml += `
            <div class="col-md-4 col-sm-6">
                <div class="card border-success h-100">
                    <div class="card-body p-2 text-center">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-circle text-success me-2"></i>
                            <small class="fw-bold text-truncate" title="${displayName}">${displayName}</small>
                        </div>
                        <small class="text-muted">账户 ${index + 1}</small>
                    </div>
                </div>
            </div>
        `;
    });

    modalHtml += `
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#allAccountsModal').remove();

    // 添加新的模态框
    $('body').append(modalHtml);

    // 显示模态框
    $('#allAccountsModal').modal('show');
}

// 显示所有账户信息的模态框
function showAllAccountsModal(accounts) {
    const modalHtml = `
        <div class="modal fade" id="allAccountsModal" tabindex="-1" aria-labelledby="allAccountsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="allAccountsModalLabel">所有选中的账户</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            ${accounts.map(account => `
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">${account.name}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">ID: ${account.id}</small><br>
                                                <small class="text-muted">邮箱: ${account.email}</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#allAccountsModal').remove();

    // 添加新的模态框
    $('body').append(modalHtml);

    // 显示模态框
    $('#allAccountsModal').modal('show');
}

// 已使用统一数据管理器替代，此函数已删除

// 使用后端数据初始化批量创建页面
function initializeBatchPageWithBackendData() {
    try {
        // 加载地区列表
        loadBatchRegionsFromBackend();

        // 加载操作系统列表
        loadBatchOperatingSystemsFromBackend();

        // 如果已经选择了地区，立即应用筛选
        const selectedRegion = $('#region').val();
        if (selectedRegion) {
            updateSystemsForRegion(selectedRegion);
            console.log('✅ 已应用地区筛选:', selectedRegion);
        }

        console.log('✅ 批量创建页面初始化完成');
    } catch (error) {
        console.error('❌ 批量创建页面初始化失败:', error);
        showBatchErrorMessage('页面初始化失败，请刷新页面重试');
    }
}

// 从后端数据加载地区列表（批量创建）
function loadBatchRegionsFromBackend() {
    if (!backendAmiData || !backendAmiData.regions) {
        console.error('❌ 批量创建：后端地区数据不可用');
        return;
    }

    const regionSelect = $('#region');
    regionSelect.empty().append('<option value="">请选择地区</option>');

    Object.keys(backendAmiData.regions).forEach(regionCode => {
        const region = backendAmiData.regions[regionCode];
        regionSelect.append(`<option value="${regionCode}">${region.name}</option>`);
    });

    console.log(`✅ 批量创建：已加载 ${Object.keys(backendAmiData.regions).length} 个地区`);
}

// 从后端数据加载操作系统列表（批量创建）
function loadBatchOperatingSystemsFromBackend() {
    if (!backendAmiData || !backendAmiData.system_icons) {
        console.error('❌ 批量创建：后端操作系统数据不可用');
        return;
    }

    const osGrid = $('#batchOsSelectionGrid');
    osGrid.empty();

    Object.keys(backendAmiData.system_icons).forEach(osName => {
        const iconConfig = backendAmiData.system_icons[osName];
        const osCard = createBatchOSCard(osName, iconConfig);
        osGrid.append(osCard);
    });

    console.log(`✅ 批量创建：已加载 ${Object.keys(backendAmiData.system_icons).length} 个操作系统`);
}

// 创建操作系统卡片（批量创建）
function createBatchOSCard(osName, iconConfig) {
    const iconHtml = iconConfig.type === 'local'
        ? `<img src="${iconConfig.path}" alt="${osName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
           <i class="${iconConfig.fallback}" style="display:none; font-size: 2rem;"></i>`
        : `<i class="${iconConfig.fallback}" style="font-size: 2rem;"></i>`;

    return `
        <div class="os-card" data-os="${osName}">
            <div class="os-icon">
                ${iconHtml}
            </div>
            <div class="os-name">${osName}</div>
        </div>
    `;
}

// 加载系统版本（批量创建）
function loadBatchSystemVersions(regionCode, systemName) {
    if (!backendAmiData || !regionCode || !systemName) {
        console.error('❌ 批量创建：缺少必要参数');
        return;
    }

    const regionData = backendAmiData.regions[regionCode];
    if (!regionData || !regionData.systems || !regionData.systems[systemName]) {
        console.error(`❌ 批量创建：地区 ${regionCode} 不支持系统 ${systemName}`);
        return;
    }

    const systemData = regionData.systems[systemName];
    const versions = systemData.versions || [];

    if (versions.length === 0) {
        console.warn(`⚠️ 批量创建：系统 ${systemName} 在地区 ${regionCode} 没有可用版本`);
        $('#amiSelection').hide();
        return;
    }

    // 显示版本选择区域
    displayBatchSystemVersions(versions, systemName);

    console.log(`✅ 批量创建：已加载系统 ${systemName} 的 ${versions.length} 个版本`);
}

// 显示系统版本选择（批量创建）
function displayBatchSystemVersions(versions, systemName) {
    const amiGrid = $('#amiGrid');
    const amiSelection = $('#amiSelection');

    amiGrid.empty();

    versions.forEach(version => {
        const versionCard = `
            <div class="ami-card" data-ami-id="${version.ami_id}">
                <div class="ami-name">${version.display_name}</div>
                <div class="ami-description">${version.name}</div>
                <div class="ami-id">${version.ami_id}</div>
            </div>
        `;
        amiGrid.append(versionCard);
    });

    // 显示版本选择区域
    amiSelection.show();

    // 默认选择第一个版本
    if (versions.length > 0) {
        $('#ami_id').val(versions[0].ami_id);
        $('#selected_os').val(systemName);
        amiGrid.find('.ami-card').first().addClass('selected');
    }
}

// 显示错误消息（批量创建）
function showBatchErrorMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        alert('错误: ' + message);
    }
}

// 已删除重复的函数

// 预加载批量账户的相关数据
function preloadBatchAccountsData(accounts) {
    console.log('🔄 批量创建页面：开始预加载第一个账户的数据...');

    // 保存账户信息
    window.batchAccounts = accounts;

    if (accounts && accounts.length > 0) {
        const firstAccountId = accounts[0].id;
        const defaultRegion = 'us-east-1'; // 使用默认地区

        console.log(`🔄 预加载第一个账户 ${firstAccountId} 在默认地区 ${defaultRegion} 的数据...`);

        // 使用统一数据管理器预加载第一个账户的相关数据
        // 分别调用各个方法，避免回调函数作用域问题
        awsDataManager.getSubnets(firstAccountId, defaultRegion, function(subnets) {
            if (subnets !== null) {
                console.log(`✅ 批量创建：第一个账户子网数据预加载完成: ${Object.keys(subnets).length} 个`);
                updateBatchSubnetOptions(subnets);
            }
        });

        awsDataManager.getSecurityGroups(firstAccountId, defaultRegion, function(securityGroups) {
            if (securityGroups !== null) {
                console.log(`✅ 批量创建：第一个账户安全组数据预加载完成: ${securityGroups.length} 个`);
                updateBatchSecurityGroupOptions(securityGroups);
            }
        });

        awsDataManager.getKeyPairs(firstAccountId, defaultRegion, function(keyPairs) {
            if (keyPairs !== null) {
                console.log(`✅ 批量创建：第一个账户密钥对数据预加载完成: ${Object.keys(keyPairs).length} 个`);
                updateBatchKeyPairOptions(keyPairs);
            }
        });
    }

    console.log('✅ 批量账户数据预加载启动完成');
}

// 批量创建页面的数据更新函数
function updateBatchSubnetOptions(subnets) {
    const $subnetSelect = $('#subnet_id');
    $subnetSelect.empty().append('<option value="">请选择子网</option>');

    if (subnets && typeof subnets === 'object' && Object.keys(subnets).length > 0) {
        const subnetKeys = Object.keys(subnets);
        subnetKeys.forEach(function(subnetId) {
            const displayName = subnets[subnetId];
            $subnetSelect.append(`<option value="${subnetId}">${displayName}</option>`);
        });

        // 默认选择第一个子网
        if (subnetKeys.length > 0) {
            $subnetSelect.val(subnetKeys[0]);
            console.log(`✅ 批量创建：已更新 ${subnetKeys.length} 个子网选项，默认选择: ${subnetKeys[0]}`);
        }
    }
}

function updateBatchSecurityGroupOptions(securityGroups) {
    const $securityGroupSelect = $('#security_group_ids');
    $securityGroupSelect.empty().append('<option value="">请选择安全组</option>');

    if (securityGroups && Array.isArray(securityGroups) && securityGroups.length > 0) {
        securityGroups.forEach(function(sg) {
            const displayName = `${sg.GroupName} (${sg.Description || sg.GroupId})`;
            $securityGroupSelect.append(`<option value="${sg.GroupId}">${displayName}</option>`);
        });

        // 默认选择第一个安全组
        if (securityGroups.length > 0) {
            $securityGroupSelect.val(securityGroups[0].GroupId);
            console.log(`✅ 批量创建：已更新 ${securityGroups.length} 个安全组选项，默认选择: ${securityGroups[0].GroupId}`);
        }
    }
}

function updateBatchKeyPairOptions(keyPairs) {
    const $keyPairSelect = $('#key_name');
    $keyPairSelect.empty();

    console.log('🔍 batch-create页面updateBatchKeyPairOptions被调用:', keyPairs);

    // 如果是null或undefined，说明请求失败或账户验证失败
    if (keyPairs === null || keyPairs === undefined) {
        $keyPairSelect.append('<option value="">该账户暂无可用密钥对</option>');
        console.log('⚠️ 密钥对数据为空，可能是账户验证失败或接口获取失败');
        return;
    }

    if (keyPairs && typeof keyPairs === 'object' && Object.keys(keyPairs).length > 0) {
        // 处理对象格式 {keyName: keyName}
        $keyPairSelect.append('<option value="">请选择密钥对</option>');
        Object.keys(keyPairs).forEach(function(keyName) {
            $keyPairSelect.append(`<option value="${keyName}">${keyName}</option>`);
        });
        console.log(`✅ batch-create页面已更新 ${Object.keys(keyPairs).length} 个密钥对选项`);
    } else if (keyPairs && Array.isArray(keyPairs) && keyPairs.length > 0) {
        // 处理数组格式（备用）
        $keyPairSelect.append('<option value="">请选择密钥对</option>');
        keyPairs.forEach(function(kp) {
            $keyPairSelect.append(`<option value="${kp.name}">${kp.name}</option>`);
        });
        console.log(`✅ batch-create页面已更新 ${keyPairs.length} 个密钥对选项`);
    } else {
        // 当keyPairs为空对象或空数组时，显示该账户暂无密钥对
        $keyPairSelect.append('<option value="">该账户暂无可用密钥对</option>');
        console.log('⚠️ batch-create页面该账户暂无密钥对');
    }
}

</script>
@endpush