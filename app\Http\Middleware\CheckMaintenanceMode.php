<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Auth;

class CheckMaintenanceMode
{
    /**
     * 允许访问的路由前缀
     */
    protected $allowedPrefixes = [
        'keyadmin',  // 管理后台路径
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查维护模式状态
        if (Setting::getBool('maintenance_mode', false)) {
            $currentPath = $request->path();

            // 如果是已登录的管理员，允许访问任何页面
            if (Auth::guard('admin')->check()) {
                return $next($request);
            }

            // 检查是否是管理后台路径（允许访问整个管理后台）
            foreach ($this->allowedPrefixes as $prefix) {
                if (str_starts_with($currentPath, $prefix)) {
                    return $next($request);
                }
            }

            // 如果是API请求，返回JSON响应
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => '系统维护中，请稍后再试'
                ], 503);
            }

            // 返回维护页面
            return response()->view('errors.maintenance', [], 503);
        }

        return $next($request);
    }
} 