-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-06-02 16:56:40
-- 服务器版本： 5.7.44
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `panel`
--

-- --------------------------------------------------------

--
-- 表的结构 `activation_codes`
--

CREATE TABLE `activation_codes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL,
  `valid_days` int(11) NOT NULL,
  `is_used` tinyint(1) NOT NULL DEFAULT '0',
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_by_admin_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `action_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `action_detail` text COLLATE utf8mb4_unicode_ci COMMENT '操作详情',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `ip_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `admins`
--

INSERT INTO `admins` (`id`, `username`, `email`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(2, 'admin', '<EMAIL>', '$2y$12$uXkQxSw0NWSm4phtUJuSJOq6RWphkWFh3dNhVjEv0z3shq3p2n.QC', NULL, '2025-05-31 20:53:55', '2025-05-31 20:53:55');

-- --------------------------------------------------------

--
-- 表的结构 `aws_accounts`
--

CREATE TABLE `aws_accounts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `account_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账户邮箱',
  `email_password` mediumtext COLLATE utf8mb4_unicode_ci,
  `aws_password` mediumtext COLLATE utf8mb4_unicode_ci,
  `access_key` mediumtext COLLATE utf8mb4_unicode_ci,
  `secret_key` mediumtext COLLATE utf8mb4_unicode_ci,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `ec2_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'EC2实例开通状态：0=未开通，1=已开通',
  `quota` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_check_at` timestamp NULL DEFAULT NULL,
  `remarks` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `enable_result` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开通结果',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `aws_mfa_key` varchar(255) COLLATE utf8mb4_unic ode_ci DEFAULT NULL COMMENT 'AWS MFA密钥',
  `iam_username` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IAM用户名',
  `iam_password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IAM密码',
  `iam_access_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IAM访问密钥',
  `iam_secret_key` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IAM秘密密钥',
  `iam_status` enum('success','failed') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IAM创建状态',
  `iam_created_at` timestamp NULL DEFAULT NULL COMMENT 'IAM创建时间',
  `aws_account_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'AWS账户ID',
  `account_id` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '账户ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', '螺旋AWS管理系统', '网站名称', '2025-05-31 20:41:14', '2025-06-01 00:37:03'),
(2, 'site_description', '螺旋AWS管理系统_API控制面板_AWS开机面板', '网站描述', '2025-06-01 00:28:32', '2025-06-01 00:37:03'),
(3, 'login_title', '螺旋AWS云面板', '登录页面标题', '2025-06-01 00:28:32', '2025-06-01 00:29:25'),
(4, 'dashboard_title', '螺旋AWS云面板', '仪表盘标题', '2025-06-01 00:28:32', '2025-06-01 00:37:03'),
(5, 'register_enabled', '1', '注册功能\n', '2025-06-01 00:28:32', '2025-06-01 00:28:32'),
(6, 'maintenance_mode', '1', '维护模式\n', '2025-06-01 00:28:32', '2025-06-01 20:37:25'),
(7, 'free_proxy_enabled', '0', '是否启用免费代理功能', NOW(), NOW()),
(8, 'free_proxy_type', 'http', '免费代理协议类型', NOW(), NOW()),
(9, 'free_proxy_host', '', '免费代理服务器地址', NOW(), NOW()),
(10, 'free_proxy_port', '', '免费代理服务器端口', NOW(), NOW()),
(11, 'free_proxy_username', '', '免费代理用户名', NOW(), NOW()),
(12, 'free_proxy_password', '', '免费代理密码', NOW(), NOW()),
(13, 'free_proxy_status', 'inactive', '免费代理状态', NOW(), NOW()),
(14, 'free_proxy_last_test', '', '免费代理最后测试时间', NOW(), NOW()),
(15, 'free_proxy_error_message', '', '免费代理错误信息', NOW(), NOW());

-- --------------------------------------------------------

--
-- 表的结构 `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后一次登录时间',
  `proxy_mode` enum('local','free_proxy','proxy') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'local' COMMENT '代理模式',
  `proxy_type` enum('http','socks5') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理类型',
  `proxy_host` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理主机',
  `proxy_port` int(11) DEFAULT NULL COMMENT '代理端口',
  `proxy_username` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理用户名',
  `proxy_password` text COLLATE utf8mb4_unicode_ci COMMENT '代理密码(加密)',
  `proxy_status` enum('active','inactive','testing','error') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inactive' COMMENT '代理状态',
  `proxy_last_test` timestamp NULL DEFAULT NULL COMMENT '最后测试时间',
  `proxy_error_message` text COLLATE utf8mb4_unicode_ci COMMENT '代理错误信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `activation_codes_code_unique` (`code`),
  ADD KEY `activation_codes_user_id_foreign` (`user_id`),
  ADD KEY `activation_codes_created_by_admin_id_foreign` (`created_by_admin_id`);

--
-- 表的索引 `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_logs_user_id_foreign` (`user_id`);

--
-- 表的索引 `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_username_unique` (`username`),
  ADD UNIQUE KEY `admins_email_unique` (`email`);

--
-- 表的索引 `aws_accounts`
--
ALTER TABLE `aws_accounts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aws_accounts_user_id_foreign` (`user_id`);

--
-- 表的索引 `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- 表的索引 `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- 表的索引 `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- 表的索引 `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`);

--
-- 表的索引 `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `activation_codes`
--
ALTER TABLE `activation_codes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- 使用表AUTO_INCREMENT `aws_accounts`
--
ALTER TABLE `aws_accounts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- 使用表AUTO_INCREMENT `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 限制导出的表
--

--
-- 限制表 `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD CONSTRAINT `activation_codes_created_by_admin_id_foreign` FOREIGN KEY (`created_by_admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `activation_codes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `aws_accounts`
--
ALTER TABLE `aws_accounts`
  ADD CONSTRAINT `aws_accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

-- --------------------------------------------------------

--
-- 表的结构 `user_data_templates`
--

CREATE TABLE IF NOT EXISTS `user_data_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `script_content` text NOT NULL COMMENT '脚本内容',
  `os_type` enum('linux','windows','all') DEFAULT 'all' COMMENT '适用操作系统',
  `category` varchar(50) DEFAULT 'custom' COMMENT '模板分类',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_os_type` (`os_type`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开机脚本模板表';

--
-- 转存表中的数据 `user_data_templates`
--

INSERT INTO `user_data_templates` (`name`, `description`, `script_content`, `os_type`, `category`) VALUES
('安装Docker', '自动安装Docker和Docker Compose', '#!/bin/bash\nyum update -y\nyum install -y docker\nsystemctl start docker\nsystemctl enable docker\nusermod -a -G docker ec2-user\ncurl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose\nchmod +x /usr/local/bin/docker-compose', 'linux', 'development'),
('配置Nginx', '安装并启动Nginx Web服务器', '#!/bin/bash\nyum update -y\nyum install -y nginx\nsystemctl start nginx\nsystemctl enable nginx\necho "<h1>Welcome to Nginx on EC2</h1>" > /var/www/html/index.html', 'linux', 'web'),
('安装Node.js', '安装Node.js和npm环境', '#!/bin/bash\nyum update -y\ncurl -sL https://rpm.nodesource.com/setup_16.x | bash -\nyum install -y nodejs\nnpm install -g pm2', 'linux', 'development'),
('基础安全加固', '基础的安全配置和防火墙设置', '#!/bin/bash\nyum update -y\nyum install -y fail2ban\nsystemctl start fail2ban\nsystemctl enable fail2ban\n# 配置SSH安全\nsed -i "s/#PermitRootLogin yes/PermitRootLogin no/" /etc/ssh/sshd_config\nsystemctl restart sshd', 'linux', 'security'),
('Windows基础配置', 'Windows实例基础配置', '<powershell>\n# 启用Windows功能\nEnable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole\n# 安装Chocolatey\nSet-ExecutionPolicy Bypass -Scope Process -Force\niex ((New-Object System.Net.WebClient).DownloadString("https://chocolatey.org/install.ps1"))\n</powershell>', 'windows', 'basic');

--
-- 使用表AUTO_INCREMENT `user_data_templates`
--
ALTER TABLE `user_data_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;