@extends('admin.layouts.app')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.css" rel="stylesheet">
<style>
.stat-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    line-height: 1.2;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    font-size: 0.8rem;
    margin-top: 0.75rem;
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
}

.trend-up {
    color: white;
    background: var(--gradient-success);
}

.trend-down {
    color: white;
    background: var(--gradient-danger);
}

.trend-icon {
    font-size: 0.875rem;
    margin-right: 0.25rem;
}
.chart-card {
    margin-bottom: 1.5rem;
}
.chart-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.chart-card .card-header .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
.bg-soft-primary {
    background: var(--gradient-primary) !important;
    color: white !important;
}
.bg-soft-success {
    background: var(--gradient-success) !important;
    color: white !important;
}
.bg-soft-info {
    background: var(--gradient-info) !important;
    color: white !important;
}
.bg-soft-warning {
    background: var(--gradient-warning) !important;
    color: white !important;
}

/* 图表容器美化 */
.chart-container {
    background: rgba(255,255,255,0.9);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin: -0.5rem;
}

/* 页面标题美化 */
.page-header-modern {
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(226,232,240,0.5);
}

.page-title-modern {
    font-size: 2rem;
    font-weight: 800;
    color: #1a202c;
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
</style>
@endpush

@section('content')
<div class="page-header-modern">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title-modern mb-2">{{ \App\Models\Setting::get('dashboard_title', '仪表盘') }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="bi bi-house-door me-1"></i>首页
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <button type="button" class="btn btn-soft-primary" id="refreshStats">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="stat-number">{{ $stats['total_users'] }}</p>
                        <p class="stat-label">总用户数</p>
                        <p class="stat-trend trend-up">
                            <i class="bi bi-arrow-up-right trend-icon"></i>
                            较昨日 +{{ $stats['today_users'] }}
                        </p>
                    </div>
                    <div class="stat-icon bg-soft-primary text-primary">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="stat-number">{{ $stats['total_aws_accounts'] }}</p>
                        <p class="stat-label">AWS账户总数</p>
                        <p class="stat-trend {{ $stats['today_aws_accounts'] >= $stats['yesterday_aws_accounts'] ? 'trend-up' : 'trend-down' }}">
                            <i class="bi bi-{{ $stats['today_aws_accounts'] >= $stats['yesterday_aws_accounts'] ? 'arrow-up-right' : 'arrow-down-right' }} trend-icon"></i>
                            今日新增: {{ $stats['today_aws_accounts'] }}
                        </p>
                    </div>
                    <div class="stat-icon bg-soft-success text-success">
                        <i class="bi bi-cloud"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="stat-number">{{ $stats['today_checks'] }}</p>
                        <p class="stat-label">今日测号数</p>
                        <p class="stat-trend trend-up">
                            <i class="bi bi-arrow-up-right trend-icon"></i>
                            较昨日 +{{ $stats['today_checks'] - $stats['yesterday_checks'] }}
                        </p>
                    </div>
                    <div class="stat-icon bg-soft-info text-info">
                        <i class="bi bi-check2-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="stat-number">{{ $stats['today_quotas'] }}</p>
                        <p class="stat-label">今日配额检测</p>
                        <p class="stat-trend trend-up">
                            <i class="bi bi-arrow-up-right trend-icon"></i>
                            较昨日 +{{ $stats['today_quotas'] - $stats['yesterday_quotas'] }}
                        </p>
                    </div>
                    <div class="stat-icon bg-soft-warning text-warning">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表 -->
<div class="row">
    <!-- 用户增长趋势 -->
    <div class="col-xl-8">
        <div class="card chart-card">
            <div class="card-header">
                <h5 class="card-title">用户增长趋势</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-soft-primary active" data-period="week">周</button>
                    <button type="button" class="btn btn-soft-primary" data-period="month">月</button>
                    <button type="button" class="btn btn-soft-primary" data-period="year">年</button>
                </div>
            </div>
            <div class="card-body">
                <div id="userGrowthChart"></div>
            </div>
        </div>
    </div>

    <!-- AWS账户状态分布 -->
    <div class="col-xl-4">
        <div class="card chart-card">
            <div class="card-header">
                <h5 class="card-title">AWS账户状态分布</h5>
                <!--<button type="button" class="btn btn-soft-primary" data-bs-toggle="tooltip" title="刷新数据">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>-->
            </div>
            <div class="card-body">
                <div id="accountStatusChart"></div>
            </div>
        </div>
    </div>

    <!-- 操作活跃度 -->
    <div class="col-xl-12">
        <div class="card chart-card">
            <div class="card-header">
                <h5 class="card-title">操作活跃度</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-soft-primary active" data-period="day">日</button>
                    <button type="button" class="btn btn-soft-primary" data-period="week">周</button>
                    <button type="button" class="btn btn-soft-primary" data-period="month">月</button>
                </div>
            </div>
            <div class="card-body">
                <div id="activityChart"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>
<script>
$(document).ready(function() {
    // 用户增长趋势图表
    const userGrowthData = @json($stats['user_growth']);
    const userGrowthChart = new ApexCharts(document.querySelector("#userGrowthChart"), {
        series: [{
            name: '新增用户',
            data: userGrowthData.data
        }],
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2,
            colors: ['#5156be']
        },
        xaxis: {
            categories: userGrowthData.dates,
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            labels: {
                formatter: function(val) {
                    return Math.round(val);
                }
            }
        },
        tooltip: {
            theme: 'dark',
            x: {
                format: 'dd/MM/yy'
            }
        },
        grid: {
            borderColor: '#f1f1f1',
            padding: {
                left: 0,
                right: 0
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.2,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: '#5156be',
                        opacity: 0.7
                    },
                    {
                        offset: 100,
                        color: '#5156be',
                        opacity: 0.2
                    }
                ]
            }
        }
    });
    userGrowthChart.render();

    // AWS账户状态分布图表
    const accountStatusData = @json($stats['account_status']);
    const accountStatusChart = new ApexCharts(document.querySelector("#accountStatusChart"), {
        series: accountStatusData.data,
        chart: {
            type: 'donut',
            height: 350
        },
        labels: accountStatusData.labels,
        colors: accountStatusData.colors,
        legend: {
            position: 'bottom'
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%'
                }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function(val, opts) {
                return opts.w.config.series[opts.seriesIndex];
            }
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + ' 个账户';
                }
            }
        }
    });
    accountStatusChart.render();

    // 操作活跃度图表
    const activityData = @json($stats['activity_data']);

    const activityChart = new ApexCharts(document.querySelector("#activityChart"), {
        series: [{
            name: '操作次数',
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: false,
                columnWidth: '40%'
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            min: 0,
            max: 10,
            tickAmount: 5,
            labels: {
                formatter: function(val) {
                    return Math.round(val);
                }
            }
        },
        fill: {
            opacity: 1,
            colors: ['#5156be']
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + ' 次操作';
                }
            }
        }
    });
    activityChart.render();

    // 刷新按钮点击事件
    $('#refreshStats').click(function() {
        location.reload();
    });

    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endpush 