<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('admin.title') }}</title>

    <!-- Styles -->
    <link href="{{ asset('assets/css/bootstrap-5.1.3.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/bootstrap-icons-1.8.1.css') }}" rel="stylesheet">
    @stack('styles')

    <!-- Scripts -->
    <script src="{{ asset('assets/js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap-5.1.3.bundle.min.js') }}"></script>
    
    <style>
        :root {
            --bs-primary: #667eea;
            --bs-primary-dark: #5a67d8;
            --bs-secondary: #74788d;
            --bs-success: #48cab2;
            --bs-danger: #ff6b6b;
            --bs-warning: #feca57;
            --bs-info: #4facfe;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #48cab2 0%, #2dd4bf 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            --gradient-warning: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --shadow-sm: 0 2px 4px rgba(0,0,0,.08);
            --shadow-md: 0 4px 15px rgba(0,0,0,.1);
            --shadow-lg: 0 8px 25px rgba(0,0,0,.15);
            --border-radius: 12px;
            --border-radius-sm: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .navbar {
            background: var(--gradient-primary) !important;
            box-shadow: var(--shadow-md);
            padding: 1rem 1.5rem;
            position: fixed;
            top: 0;
            right: 0;
            left: 0;
            z-index: 1030;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .navbar-brand {
            font-weight: 700;
            padding: 0;
            font-size: 1.5rem;
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand i {
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-right: 0.75rem;
            font-size: 1.75rem;
        }

        .nav-link {
            padding: 0.75rem 1.25rem;
            color: rgba(255,255,255,.9) !important;
            font-weight: 500;
            position: relative;
            transition: var(--transition);
            border-radius: var(--border-radius-sm);
            margin: 0 0.25rem;
            backdrop-filter: blur(10px);
        }

        .nav-link:hover {
            color: #fff !important;
            background: rgba(255,255,255,.15);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .nav-link.active {
            color: #fff !important;
            background: rgba(255,255,255,.2);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -8px;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .btn-soft-primary {
            color: var(--bs-primary);
            background-color: rgba(81,86,190,.1);
            border-color: transparent;
        }
        .btn-soft-primary:hover {
            color: #fff;
            background-color: var(--bs-primary);
        }
        .btn-soft-success {
            color: #2ab57d;
            background-color: rgba(42,181,125,.1);
            border-color: transparent;
        }
        .btn-soft-success:hover {
            color: #fff;
            background-color: #2ab57d;
        }
        .card {
            box-shadow: var(--shadow-md);
            border-radius: var(--border-radius);
            border: none;
            transition: var(--transition);
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        .card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }
        .card-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
            border-bottom: 1px solid rgba(226,232,240,0.8);
            padding: 1.5rem 2rem;
            backdrop-filter: blur(10px);
        }
        .card-title {
            font-weight: 700;
            margin-bottom: 0;
            color: #1a202c;
            font-size: 1.25rem;
        }
        .card-body {
            padding: 2rem;
            background: rgba(255,255,255,0.98);
        }
        .breadcrumb {
            padding: 0.5rem 0;
            margin: 0;
            background: transparent;
        }
        .breadcrumb-item a {
            color: var(--bs-secondary);
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: var(--bs-primary);
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.15);
            border-radius: 0.5rem;
            padding: 0.5rem;
        }
        .dropdown-item {
            padding: 0.5rem 1rem;
            color: #495057;
            border-radius: 0.25rem;
        }
        .dropdown-item:hover {
            background-color: rgba(81,86,190,.1);
            color: var(--bs-primary);
        }
        .dropdown-item i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }
        .page-content {
            margin-top: 90px;
            padding: 2.5rem;
            min-height: calc(100vh - 90px);
        }

        /* 现代化按钮样式 */
        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-soft-primary {
            background: var(--gradient-primary) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-soft-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white !important;
        }

        .btn-soft-danger {
            background: var(--gradient-danger) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-soft-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            color: white !important;
        }

        .btn-soft-success {
            background: var(--gradient-success) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(72, 202, 178, 0.3);
        }

        .btn-soft-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 202, 178, 0.4);
            color: white !important;
        }

        .btn-soft-warning {
            background: var(--gradient-warning) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(254, 202, 87, 0.3);
        }

        .btn-soft-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(254, 202, 87, 0.4);
            color: white !important;
        }

        .btn-soft-info {
            background: var(--gradient-info) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-soft-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
            color: white !important;
        }
        /* 操作按钮美化 */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: flex-start;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius-sm);
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }

        .btn-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-action:hover::before {
            left: 100%;
        }

        .btn-action-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-action-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-action-danger {
            background: var(--gradient-danger);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-action-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .btn-action-success {
            background: var(--gradient-success);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 202, 178, 0.3);
        }

        .btn-action-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 202, 178, 0.4);
            color: white;
        }

        .btn-action-warning {
            background: var(--gradient-warning);
            color: white;
            box-shadow: 0 4px 15px rgba(254, 202, 87, 0.3);
        }

        .btn-action-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(254, 202, 87, 0.4);
            color: white;
        }

        /* 禁用状态 */
        .btn:disabled, .btn-action:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn:disabled::before, .btn-action:disabled::before {
            display: none;
        }

        /* 工具提示样式 */
        .btn-action[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            animation: fadeInTooltip 0.3s ease;
        }

        @keyframes fadeInTooltip {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(5px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        /* 表格美化 */
        .table {
            background: rgba(255,255,255,0.9);
            border-radius: var(--border-radius-sm);
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 2px solid rgba(226,232,240,0.8);
            font-weight: 600;
            color: #374151;
            padding: 1rem 1.5rem;
        }

        .table tbody tr {
            transition: var(--transition);
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .table tbody td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(226,232,240,0.5);
            vertical-align: middle;
        }

        /* 徽章美化 */
        .badge {
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            padding: 0.5rem 0.75rem;
        }

        .badge-soft-success {
            background: var(--gradient-success);
            color: white;
        }

        .badge-soft-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .badge-soft-danger {
            background: var(--gradient-danger);
            color: white;
        }

        .badge-soft-info {
            background: var(--gradient-info);
            color: white;
        }

        @media (max-width: 768px) {
            .page-content {
                padding: 1.5rem;
            }
        }
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background: var(--bs-primary);
                margin: 0.5rem -1.5rem -0.75rem;
                padding: 1rem 1.5rem;
                border-top: 1px solid rgba(255,255,255,.1);
            }
            .navbar-nav {
                padding: 0.5rem 0;
            }
            .nav-link {
                padding: 0.75rem 1rem;
                border-radius: 4px;
            }
            .navbar-nav .dropdown-menu {
                background: rgba(255,255,255,.05);
                border: none;
                padding: 0.5rem;
                margin-top: 0.5rem;
                box-shadow: none;
            }
            .navbar-nav .dropdown-item {
                color: rgba(255,255,255,.85);
                padding: 0.75rem 1rem;
                border-radius: 4px;
            }
            .navbar-nav .dropdown-item:hover {
                background: rgba(255,255,255,.1);
                color: #fff;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{{ route('admin.dashboard') }}">
                <i class="bi bi-cloud me-2"></i>
                <span>{{ \App\Models\Setting::get('dashboard_title', config('app.name')) }}</span>
            </a>
            <button class="navbar-toggler border-0 shadow-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                            <i class="bi bi-speedometer2 me-2"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center {{ request()->routeIs('admin.activation-codes.*') ? 'active' : '' }}" href="{{ route('admin.activation-codes.index') }}">
                            <i class="bi bi-key me-2"></i>
                            <span>激活码管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}">
                            <i class="bi bi-people me-2"></i>
                            <span>会员管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center {{ request()->routeIs('admin.accounts.*') ? 'active' : '' }}" href="{{ route('admin.accounts.index') }}">
                            <i class="bi bi-person-badge me-2"></i>
                            <span>账户管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}">
                            <i class="bi bi-gear me-2"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <span>管理员</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                                <li style="padding-right:.5rem!important;padding-left:.5rem!important;">
                                    <a class="dropdown-item d-flex align-items-center" href="{{ route('admin.profile') }}">
                                        <i class="bi bi-person me-2"></i>个人资料
                                    </a>
                                </li>
                                <li style="padding-right:.5rem!important;padding-left:.5rem!important;">
                                    <a class="dropdown-item d-flex align-items-center" href="https://t.me/Kax0rs" target="_blank">
                                        <i class="bi bi-headset me-2"></i>联系客服
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('admin.logout') }}" method="POST" class="px-2">
                                    @csrf
                                    <button type="submit" class="dropdown-item d-flex align-items-center">
                                        <i class="bi bi-box-arrow-right me-2"></i>
                                        <span>退出登录</span>
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="page-content">
        <div class="container-fluid">
            @yield('content')
        </div>
    </main>



    @stack('scripts')
</body>
</html>