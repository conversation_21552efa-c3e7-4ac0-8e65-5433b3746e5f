<?php

namespace App\Services;

use App\Models\AwsAccount;
use App\Models\Setting;
use Aws\Credentials\Credentials;
use Aws\Exception\AwsException;
use Aws\Iam\IamClient;
use Aws\Ec2\Ec2Client;
use Aws\ServiceQuotas\ServiceQuotasClient;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Handler\CurlHandler;
use Aws\Account\AccountClient;

class AwsService
{
    protected $regions = [
        'us-east-1' => '美国东部（弗吉尼亚北部）',
        'us-east-2' => '美国东部（俄亥俄）',
        'us-west-1' => '美国西部（加利福尼亚北部）',
        'us-west-2' => '美国西部（俄勒冈）',
        'ap-east-1' => '亚太地区（香港）',
        'ap-south-1' => '亚太地区（孟买）',
        'ap-northeast-1' => '亚太地区（东京）',
        'ap-northeast-2' => '亚太地区（首尔）',
        'ap-northeast-3' => '亚太地区（大阪）',
        'ap-southeast-1' => '亚太地区（新加坡）',
        'ap-southeast-2' => '亚太地区（悉尼）',
        'ca-central-1' => '加拿大（中部）',
        'eu-central-1' => '欧洲（法兰克福）',
        'eu-west-1' => '欧洲（爱尔兰）',
        'eu-west-2' => '欧洲（伦敦）',
        'eu-west-3' => '欧洲（巴黎）',
        'eu-north-1' => '欧洲（斯德哥尔摩）',
        'sa-east-1' => '南美洲（圣保罗）',
    ];

    public function getRegions()
    {
        return $this->regions;
    }

    protected function createClient($class, AwsAccount $account, $region = null)
    {
        $user = auth()->user();

        \Log::info("创建AWS客户端", [
            'class' => $class,
            'region' => $region ?? 'us-east-1',
            'access_key' => $account->access_key,
            'secret_key' => substr($account->secret_key, 0, 10) . '...',
            'verify_ssl' => config('aws.disable_ssl_verification', false),
            'user_id' => $user ? $user->id : null,
            'proxy_mode' => $user ? $user->proxy_mode : 'local'
        ]);

        $config = [
            'version' => 'latest',
            'region'  => $region ?? 'us-east-1',
            'credentials' => [
                'key'    => $account->access_key,
                'secret' => $account->secret_key,
            ],
            'http' => [
                'connect_timeout' => 5,    // 连接超时时间
                'timeout' => 10            // 请求超时时间
            ]
        ];

        // 根据配置决定是否禁用SSL验证
        if (config('aws.disable_ssl_verification', false)) {
            $config['http']['verify'] = false;
        }

        // 应用用户代理配置
        $this->applyProxyConfig($config, $user);

        try {
            $client = new $class($config);
            \Log::info("AWS客户端创建成功");
            return $client;
        } catch (\Exception $e) {
            \Log::error("AWS客户端创建失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                \Log::info("AWS客户端创建时检测到代理异常，直接抛出", [
                    'error_message' => $e->getMessage()
                ]);
                // 直接抛出代理异常，让上层处理
                throw $e;
            }
            
            throw $e;
        }
    }

    /**
     * 应用用户代理配置到AWS SDK配置
     */
    protected function applyProxyConfig(&$config, $user = null)
    {
        if (!$user) {
            \Log::info("AWS SDK请求模式: 本地模式 (无用户认证)");
            return;
        }

        // 如果用户选择了代理模式，必须检查代理状态
        if ($user->proxy_mode === 'proxy') {
            // 检查代理状态是否异常
            if ($user->proxy_status !== 'active') {
                \Log::error("代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'proxy_status' => $user->proxy_status,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('当前代理模式IP异常，请切换其他模式进行操作');
            }

            // 检查代理配置是否完整
            if (!$user->proxy_host || !$user->proxy_port) {
                \Log::error("代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('代理配置不完整，请重新配置代理或切换到本地模式');
            }

            // 构建代理URL
            $proxyUrl = $this->buildProxyUrl(
                $user->proxy_type,
                $user->proxy_host,
                $user->proxy_port,
                $user->proxy_username,
                $user->proxy_password
            );

            // 应用代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("AWS SDK请求模式: 代理模式", [
                'user_id' => $user->id,
                'proxy_type' => $user->proxy_type,
                'proxy_host' => $user->proxy_host,
                'proxy_port' => $user->proxy_port,
                'proxy_username' => $user->proxy_username,
                'proxy_status' => $user->proxy_status,
                'actual_proxy_ip' => $user->proxy_host, // 实际使用的代理IP
                'proxy_url_format' => $user->proxy_type . '://' . $user->proxy_host . ':' . $user->proxy_port
            ]);
        } elseif ($user->proxy_mode === 'free_proxy') {
            // 免费代理模式
            $freeProxyStatus = Setting::get('free_proxy_status', 'inactive');

            if ($freeProxyStatus !== 'active') {
                \Log::error("免费代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'free_proxy_status' => $freeProxyStatus
                ]);

                throw new \Exception('当前免费代理模式IP异常，请切换其他模式进行操作');
            }

            // 获取免费代理配置
            $freeProxyType = Setting::get('free_proxy_type', 'http');
            $freeProxyHost = Setting::get('free_proxy_host', '');
            $freeProxyPort = Setting::get('free_proxy_port', '');
            $freeProxyUsername = Setting::get('free_proxy_username', '');
            $freeProxyPassword = Setting::get('free_proxy_password', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                \Log::error("免费代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id
                    // 敏感信息已移除：不记录具体的配置信息
                ]);

                throw new \Exception('免费代理配置不完整，请联系管理员');
            }

            // 构建免费代理URL
            $proxyUrl = $this->buildProxyUrl(
                $freeProxyType,
                $freeProxyHost,
                $freeProxyPort,
                $freeProxyUsername,
                $freeProxyPassword
            );

            // 应用免费代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("AWS SDK请求模式: 免费代理模式", [
                'user_id' => $user->id,
                'free_proxy_status' => $freeProxyStatus
                // 敏感信息已移除：不记录IP、端口、用户名等
            ]);
        } else {
            \Log::info("AWS SDK请求模式: 本地模式", [
                'user_id' => $user->id,
                'proxy_mode' => $user->proxy_mode,
                'proxy_status' => $user->proxy_status ?? 'inactive'
            ]);
        }
    }

    /**
     * 构建代理URL
     */
    protected function buildProxyUrl($type, $host, $port, $username = null, $password = null)
    {
        $proxyUrl = $type . '://';

        if ($username && $password) {
            $proxyUrl .= urlencode($username) . ':' . urlencode($password) . '@';
        }

        $proxyUrl .= $host . ':' . $port;

        return $proxyUrl;
    }

    /**
     * 检查错误是否为代理相关错误
     */
    public function isProxyRelatedError($errorMessage)
    {
        $proxyErrorIndicators = [
            '407', 'Proxy Authentication Required',
            '502', 'Bad Gateway',
            '503', 'Service Unavailable',
            '504', 'Gateway Timeout',
            'CONNECT tunnel failed',
            'Proxy CONNECT aborted',
            'cURL error 7', 'cURL error 28', 'cURL error 35',
            'cURL error 52', 'cURL error 56',
            'Empty reply from server',
            'Connection refused',
            'Operation timed out'
        ];

        foreach ($proxyErrorIndicators as $indicator) {
            if (strpos($errorMessage, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    protected function createAccountClient(AwsAccount $account)
    {
        $user = auth()->user();

        \Log::info("创建AWS Account客户端", [
            'access_key' => $account->access_key,
            'secret_key' => substr($account->secret_key, 0, 10) . '...',
            'verify_ssl' => config('aws.disable_ssl_verification', false),
            'user_id' => $user ? $user->id : null,
            'proxy_mode' => $user ? $user->proxy_mode : 'local'
        ]);

        $config = [
            'version' => 'latest',
            'region'  => 'us-east-1',
            'credentials' => new Credentials(
                $account->access_key,
                $account->secret_key
            ),
            'http' => [
                'connect_timeout' => 5,    // 连接超时时间
                'timeout' => 10            // 请求超时时间
            ]
        ];

        // 根据配置决定是否禁用SSL验证
        if (config('aws.disable_ssl_verification', false)) {
            $config['http']['verify'] = false;
        }

        // 应用用户代理配置
        $this->applyProxyConfig($config, $user);

        try {
            $client = new \Aws\Account\AccountClient($config);
            \Log::info("AWS Account客户端创建成功");
            return $client;
        } catch (\Exception $e) {
            \Log::error("AWS Account客户端创建失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function checkAccount(AwsAccount $account)
    {
        try {
            \Log::info("开始检查AWS账户", [
                'account_id' => $account->id,
                'account_name' => $account->account_name
            ]);
            
            $iamClient = $this->createClient(IamClient::class, $account);
            \Log::info("开始获取用户列表");
            
            $result = $iamClient->listUsers();
            \Log::info("获取用户列表成功", [
                'result' => json_encode($result)
            ]);
            
            \Log::info("AWS IAM ListUsers API调用成功", [
                'response_data' => $result->toArray(),
                'request_id' => $result['@metadata']['requestId'] ?? null,
                'http_status_code' => $result['@metadata']['statusCode'] ?? null
            ]);
            
            // 更新账户状态为正常
            $account->status = 1;
            $account->last_check_at = now();
            $account->save();
            
            \Log::info("==================== AwsService.checkAccount完成（账户正常）====================");
            return ['success' => true, 'status' => 1];
        } catch (AwsException $e) {
            \Log::error("AWS异常", [
                'error_code' => $e->getAwsErrorCode(),
                'error_message' => $e->getMessage(),
                'error_type' => $e->getAwsErrorType(),
                'request_id' => $e->getAwsRequestId()
            ]);

            // 只有在代理模式下才检查代理相关错误
            $user = auth()->user();
            if ($user && ($user->proxy_mode === 'proxy' || $user->proxy_mode === 'free_proxy') && $this->isProxyRelatedError($e->getMessage())) {
                // 如果是免费代理模式，更新免费代理状态为异常
                if ($user->proxy_mode === 'free_proxy') {
                    Setting::set('free_proxy_status', 'inactive');
                    Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                    Setting::set('free_proxy_error_message', '代理连接失败: ' . $e->getMessage());

                    \Log::error("免费代理IP异常，已更新状态", [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);

                    // 直接抛出代理异常，让上层处理
                    throw new \Exception('当前免费代理模式IP异常，请切换其他模式进行操作');
                }

                // 直接抛出代理异常，让上层处理
                throw new \Exception('当前代理模式IP异常，请切换其他模式进行操作');
            }

            $errorCode = $e->getAwsErrorCode();
            if ($errorCode === 'SignatureDoesNotMatch') {
                return ['success' => false, 'status' => 3, 'error' => '凭证无效'];
            } elseif ($errorCode === 'UnauthorizedOperation' or $errorCode === 'InvalidClientTokenId') {
                return ['success' => false, 'status' => 2, 'error' => '账户已被封禁'];
            } else {
                return ['success' => false, 'status' => 3, 'error' => $e->getMessage()];
            }
        } catch (\Exception $e) {
            \Log::error("检查账户系统错误", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                // 直接抛出代理异常，让上层处理
                throw $e;
            }
            
            return ['success' => false, 'status' => 3, 'error' => $e->getMessage()];
        }
    }

    public function checkQuotas(AwsAccount $account)
    {
        // 如果账户状态不是未测试(0)或正常(1)，直接返回配额为0
        if (!in_array($account->status, [0, 1])) {
            return [
                'quota' => '0 x 0',
                'error' => '账户状态异常，无法检测配额'
            ];
        }

        try {
            // 创建ServiceQuotas客户端
            $client = $this->createClient(ServiceQuotasClient::class, $account, 'us-east-1');
            
            // 获取EC2实例配额
                            $result = $client->getServiceQuota([
                'ServiceCode' => 'ec2',
                'QuotaCode' => 'L-1216C47A'  // Running On-Demand Standard (A, C, D, H, I, M, R, T, Z) instances
            ]);
            
            $maxInstances = (int)($result['Quota']['Value'] ?? 0);
            
            // 获取Spot实例配额
            $spotResult = $client->getServiceQuota([
                'ServiceCode' => 'ec2',
                'QuotaCode' => 'L-34B43A08'  // All Standard (A, C, D, H, I, M, R, T, Z) Spot Instance Requests
            ]);
            
            $maxSpotInstances = (int)($spotResult['Quota']['Value'] ?? 0);

            return [
                'quota' => "{$maxInstances} x {$maxSpotInstances}",
                'error' => null
            ];

        } catch (\Aws\Exception\AwsException $e) {
            \Log::error("AWS配额检测错误: " . $e->getMessage(), [
                'error_code' => $e->getAwsErrorCode(),
                'error_message' => $e->getMessage(),
                'error_type' => $e->getAwsErrorType(),
                'request_id' => $e->getAwsRequestId()
            ]);

            // 只有在代理模式下才检查代理相关错误
            $user = auth()->user();
            if ($user && ($user->proxy_mode === 'proxy' || $user->proxy_mode === 'free_proxy') && $this->isProxyRelatedError($e->getMessage())) {
                // 如果是免费代理模式，更新免费代理状态为异常
                if ($user->proxy_mode === 'free_proxy') {
                    Setting::set('free_proxy_status', 'inactive');
                    Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                    Setting::set('free_proxy_error_message', '代理连接失败: ' . $e->getMessage());

                    return [
                        'quota' => '0 x 0',
                        'error' => '当前免费代理模式IP异常，请切换其他模式进行操作'
                    ];
                }

                return [
                    'quota' => '0 x 0',
                    'error' => '当前代理模式IP异常，请切换其他模式进行操作'
                ];
            }

            // 根据错误类型返回不同的错误信息
            $errorCode = $e->getAwsErrorCode();
            if ($errorCode === 'SignatureDoesNotMatch') {
                return [
                    'quota' => '0 x 0',
                    'error' => '凭证无效'
                ];
            } elseif ($errorCode === 'UnauthorizedOperation' || $errorCode === 'InvalidClientTokenId') {
                return [
                    'quota' => '0 x 0',
                    'error' => '账户已被封禁'
                ];
            } else {
                return [
                    'quota' => '0 x 0',
                    'error' => $e->getMessage()
                ];
            }
        } catch (\Exception $e) {
            \Log::error("配额检测系统错误: " . $e->getMessage());
            return [
                'quota' => '0 x 0',
                'error' => $e->getMessage()
            ];
        }
    }

    public function enableRegion($accessKey, $secretKey, $region)
    {
        try {
            \Log::info("开始执行enableRegion", [
                'region' => $region,
                'access_key' => $accessKey,
                'secret_key' => substr($secretKey, 0, 10) . '...'
            ]);

            // 创建临时账户对象用于创建客户端
            $tempAccount = new \App\Models\AwsAccount([
                'access_key' => $accessKey,
                'secret_key' => $secretKey
            ]);

            // 查找实际的账户记录
            $account = \App\Models\AwsAccount::where('access_key', $accessKey)
                ->where('secret_key', $secretKey)
                ->first();

            $accountStatus = null;

            // 如果找到账户且状态为0或1，进行测号
            if ($account && in_array($account->status, [0, 1])) {
                $checkResult = $this->checkAccount($account);
                
                // 更新账户状态
                $account->status = $checkResult['status'];
                $account->last_check_at = now();
                $accountStatus = $checkResult['status'];
                
                // 如果有错误信息，保存到备注
                if (isset($checkResult['error'])) {
                    $account->remarks = $checkResult['error'];
                }
                
                $account->save();

                // 如果测号结果不是正常(status=1)，则停止开通
                if ($checkResult['status'] !== 1) {
                    return [
                        'success' => false,
                        'responses' => [
                            'error' => '账户测试未通过，无法开通区域'
                        ],
                        'account_status' => $accountStatus
                    ];
                }
            }

            $client = $this->createClient(AccountClient::class, $tempAccount);
            \Log::info("AWS客户端创建成功");
            
            // 存储API响应
            $responses = [
                'describeRegions' => null,
                'error' => null
            ];

            // 1. 获取当前可用区域
            try {
                \Log::info("正在获取区域列表...");
                $result = $client->listRegions();
                $resultArray = $result->toArray();
                \Log::info("获取区域列表成功", ['response' => $resultArray]);
                $responses['describeRegions'] = $resultArray;
            } catch (AwsException $e) {
                \Log::error("获取区域列表失败", [
                    'error_code' => $e->getAwsErrorCode(),
                    'error_message' => $e->getMessage(),
                    'error_type' => $e->getAwsErrorType(),
                    'request_id' => $e->getAwsRequestId(),
                    'trace' => $e->getTraceAsString()
                ]);
                $responses['error'] = "获取区域列表失败: " . $e->getMessage();
                return [
                    'success' => false,
                    'responses' => $responses,
                    'account_status' => $accountStatus
                ];
            } catch (\Exception $e) {
                \Log::error("获取区域列表失败", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $responses['error'] = "获取区域列表失败: " . $e->getMessage();
                return [
                    'success' => false,
                    'responses' => $responses,
                    'account_status' => $accountStatus
                ];
            }

            // 2. 开通区域
            try {
                \Log::info("正在开通区域...", ['region' => $region]);
                $result = $client->enableRegion(['RegionName' => $region]);
                $resultArray = $result->toArray();
                \Log::info("开通区域成功", ['response' => $resultArray]);
                $responses['enableRegion'] = $resultArray;

                // 如果找到账户，更新开通结果
                if ($account) {
                    $account->enable_result = json_encode([
                        'success' => true,
                        'responses' => $responses
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                }

                return [
                    'success' => true,
                    'responses' => $responses,
                    'account_status' => $accountStatus
                ];
            } catch (AwsException $e) {
                \Log::error("开通区域失败", [
                    'error_code' => $e->getAwsErrorCode(),
                    'error_message' => $e->getMessage(),
                    'error_type' => $e->getAwsErrorType(),
                    'request_id' => $e->getAwsRequestId(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 只有在代理模式下才检查代理相关错误
                $user = auth()->user();
                if ($user && ($user->proxy_mode === 'proxy' || $user->proxy_mode === 'free_proxy') && $this->isProxyRelatedError($e->getMessage())) {
                    // 如果是免费代理模式，更新免费代理状态为异常
                    if ($user->proxy_mode === 'free_proxy') {
                        Setting::set('free_proxy_status', 'inactive');
                        Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                        Setting::set('free_proxy_error_message', '代理连接失败: ' . $e->getMessage());

                        $responses['error'] = "当前免费代理模式IP异常，请切换其他模式进行操作";
                    } else {
                        $responses['error'] = "当前代理模式IP异常，请切换其他模式进行操作";
                    }
                    
                    // 直接抛出代理异常，让上层处理
                    throw new \Exception($responses['error']);
                } else {
                    $responses['error'] = "开通区域失败: " . $e->getMessage();
                }

                // 如果找到账户，更新开通结果
                if ($account) {
                    $account->enable_result = json_encode([
                        'success' => false,
                        'responses' => $responses
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                }

                return [
                    'success' => false,
                    'responses' => $responses,
                    'account_status' => $accountStatus
                ];
            } catch (\Exception $e) {
                \Log::error("开通区域失败", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                // 检查是否是代理异常错误
                if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                       strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                    // 直接抛出代理异常，让上层处理
                    throw $e;
                }
                
                $responses['error'] = "开通区域失败: " . $e->getMessage();

                // 如果找到账户，更新开通结果
                if ($account) {
                    $account->enable_result = json_encode([
                        'success' => false,
                        'responses' => $responses
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                }

                return [
                    'success' => false,
                    'responses' => $responses,
                    'account_status' => $accountStatus
                ];
            }
        } catch (AwsException $e) {
            \Log::error("区域开通AWS错误", [
                'error_code' => $e->getAwsErrorCode(),
                'error_message' => $e->getMessage(),
                'error_type' => $e->getAwsErrorType(),
                'request_id' => $e->getAwsRequestId(),
                'trace' => $e->getTraceAsString()
            ]);

            // 只有在代理模式下才检查代理相关错误
            $user = auth()->user();
            $errorMessage = "AWS错误: " . $e->getMessage();

            if ($user && ($user->proxy_mode === 'proxy' || $user->proxy_mode === 'free_proxy') && $this->isProxyRelatedError($e->getMessage())) {
                // 如果是免费代理模式，更新免费代理状态为异常
                if ($user->proxy_mode === 'free_proxy') {
                    Setting::set('free_proxy_status', 'inactive');
                    Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                    Setting::set('free_proxy_error_message', '代理连接失败: ' . $e->getMessage());

                    $errorMessage = "当前免费代理模式IP异常，请切换其他模式进行操作";
                } else {
                    $errorMessage = "当前代理模式IP异常，请切换其他模式进行操作";
                }
                
                // 直接抛出代理异常，让上层处理
                throw new \Exception($errorMessage);
            }

            return [
                'success' => false,
                'responses' => [
                    'error' => $errorMessage
                ],
                'account_status' => $accountStatus
            ];
        } catch (\Exception $e) {
            \Log::error("区域开通系统错误", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                \Log::info("检测到代理异常，直接抛出", [
                    'error_message' => $e->getMessage(),
                    'error_type' => 'proxy_error'
                ]);
                // 直接抛出代理异常，让上层处理
                throw $e;
            }
            
            \Log::info("非代理异常，作为普通错误处理", [
                'error_message' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'responses' => [
                    'error' => "系统错误: " . $e->getMessage()
                ],
                'account_status' => $accountStatus
            ];
        }
    }

    /**
     * 预检查账户状态
     * @param AwsAccount $account
     * @return array ['success' => bool, 'status' => int, 'message' => string]
     */
    private function preCheckAccountStatus(AwsAccount $account)
    {
        // 检查账户状态是否为封禁或无效
        if (in_array($account->status, [2, 3])) {
            return [
                'success' => false,
                'status' => $account->status,
                'message' => $account->status == 2 ? '账户已封禁' : '账户无效'
            ];
        }

        // 如果账户状态为未检测或正常,进行一键测号
        if (in_array($account->status, [0, 1])) {
            $checkResult = $this->checkAccount($account);
            
            // 如果测号结果为封禁或无效
            if (!$checkResult['success']) {
                // 更新账户状态
                $account->status = $checkResult['status'];
                $account->last_check_at = now();
                $account->save();
                
                return [
                    'success' => false,
                    'status' => $checkResult['status'],
                    'message' => $checkResult['error']
                ];
            }
            
            return [
                'success' => true,
                'status' => 1,
                'message' => '账户正常'
            ];
        }

        return [
            'success' => false,
            'status' => $account->status,
            'message' => '未知状态'
        ];
    }

    /**
     * 批量开通区域
     * @param array $accounts AwsAccount数组
     * @param string $region 区域代码
     * @return array ['success' => array, 'failed' => array]
     */
    public function batchEnableRegion($accounts, $region)
    {
        $results = [
            'success' => [],
            'failed' => []
        ];

        foreach ($accounts as $account) {
            // 预检查账户状态
            $preCheck = $this->preCheckAccountStatus($account);
            
            if (!$preCheck['success']) {
                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => $preCheck['message'],
                    'enable_result' => json_encode([
                        'success' => false,
                        'responses' => [
                            'error' => $preCheck['message']
                        ],
                        'account_status' => $preCheck['status']
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                ];
                
                // 更新账户开通结果
                $account->enable_result = json_encode([
                    'success' => false,
                    'responses' => [
                        'error' => $preCheck['message']
                    ],
                    'account_status' => $preCheck['status']
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                $account->save();
                continue;
            }

            // 账户正常,尝试开通区域
            try {
                $enableResult = $this->enableRegion(
                    $account->access_key,
                    $account->secret_key,
                    $region
                );

                if ($enableResult['success']) {
                    $results['success'][] = [
                        'account_id' => $account->id,
                        'message' => '开通成功',
                        'enable_result' => json_encode([
                            'success' => true,
                            'responses' => $enableResult['responses'],
                            'account_status' => $enableResult['account_status']
                        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ];
                    
                    // 更新账户开通结果
                    $account->enable_result = json_encode([
                        'success' => true,
                        'responses' => $enableResult['responses'],
                        'account_status' => $enableResult['account_status']
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                } else {
                    $results['failed'][] = [
                        'account_id' => $account->id,
                        'message' => '开通失败',
                        'enable_result' => json_encode([
                            'success' => false,
                            'responses' => $enableResult['responses'],
                            'account_status' => $enableResult['account_status']
                        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ];
                    
                    // 更新账户开通结果
                    $account->enable_result = json_encode([
                        'success' => false,
                        'responses' => $enableResult['responses'],
                        'account_status' => $enableResult['account_status']
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                }
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => $e->getMessage(),
                    'enable_result' => json_encode([
                        'success' => false,
                        'responses' => [
                            'error' => $e->getMessage()
                        ],
                        'account_status' => $account->status
                    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                ];
                
                // 更新账户开通结果
                $account->enable_result = json_encode([
                    'success' => false,
                    'responses' => [
                        'error' => $e->getMessage()
                    ],
                    'account_status' => $account->status
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                $account->save();
            }
        }

        return $results;
    }

    /**
     * 批量检查配额
     * @param array $accounts AwsAccount数组
     * @return array ['success' => array, 'failed' => array]
     */
    public function batchCheckQuotas($accounts)
    {
        $results = [
            'success' => [],
            'failed' => []
        ];

        foreach ($accounts as $account) {
            // 预检查账户状态
            $preCheck = $this->preCheckAccountStatus($account);
            
            if (!$preCheck['success']) {
                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => $preCheck['message']
                ];
                // 更新账户配额为0
                $account->quota = '0 x 0';
                $account->save();
                continue;
            }

            // 账户正常,检查配额
            try {
                $quotaResult = $this->checkQuotas($account);
                
                if (!isset($quotaResult['error'])) {
                    $results['success'][] = [
                        'account_id' => $account->id,
                        'quota' => $quotaResult['quota']
                    ];
                    
                    // 更新账户配额
                    $account->quota = $quotaResult['quota'];
                    $account->save();
                } else {
                    $results['failed'][] = [
                        'account_id' => $account->id,
                        'message' => $quotaResult['error']
                    ];
                    
                    // 更新账户配额为0
                    $account->quota = '0 x 0';
                    $account->save();
                }
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => $e->getMessage()
                ];
                
                // 更新账户配额为0
                $account->quota = '0 x 0';
                $account->save();
        }
        }

        return $results;
    }

    public function enableRegions(AwsAccount $account, array $regions)
    {
        try {
            // 创建AWS客户端
            $client = $this->createClient(AccountClient::class, $account);
            
            // 存储API响应
            $apiResponses = [
                'describeRegions' => null,
                'enableRegion' => [],
                'checkStatus' => [],
                'error' => null
            ];
            
            // 1. 获取当前可用区域
            try {
                $result = $client->listRegions();
                $apiResponses['describeRegions'] = json_encode($result->toArray(), JSON_PRETTY_PRINT);
            } catch (\Exception $e) {
                $apiResponses['error'] = "获取区域列表失败: " . $e->getMessage();
                return [
                    'success' => false,
                    'enable_result' => json_encode($apiResponses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                ];
            }

            // 2. 开通每个区域
            foreach ($regions as $region) {
                try {
                    // 开通区域
                    $result = $client->enableRegion([
                        'RegionName' => $region
                    ]);
                    $apiResponses['enableRegion'][$region] = json_encode($result->toArray(), JSON_PRETTY_PRINT);

                    // 检查状态
                    $status = $client->getRegionOptStatus([
                        'RegionName' => $region
                    ]);
                    $apiResponses['checkStatus'][$region] = json_encode($status->toArray(), JSON_PRETTY_PRINT);
                } catch (\Exception $e) {
                    $apiResponses['error'] = "开通区域 {$region} 失败: " . $e->getMessage();
                    return [
                        'success' => false,
                        'enable_result' => json_encode($apiResponses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ];
                }
            }

            // 更新账户状态
            $account->update([
                'enable_status' => 'success',
                'enable_result' => json_encode($apiResponses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ]);

            return [
                'success' => true,
                'enable_result' => json_encode($apiResponses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ];
        } catch (\Exception $e) {
            $apiResponses['error'] = "系统错误: " . $e->getMessage();
            return [
                'success' => false,
                'enable_result' => json_encode($apiResponses, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            ];
        }
    }
} 