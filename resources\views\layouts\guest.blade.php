<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ \App\Models\Setting::get('site_name', config('app.name')) }} - 会员控制中心</title>

    <!-- Bootstrap CSS -->
    <link href="{{ asset('assets/css/bootstrap-5.1.3.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/bootstrap-icons-1.8.1.css') }}" rel="stylesheet">

    <!-- Styles -->
    <style>
        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: white !important;
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.1);
        }

        /* 移动端导航栏样式 */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
                border-radius: 0 0 8px 8px;
                padding: 0.5rem;
                margin-top: 0.5rem;
            }

            .nav-link {
                padding: 0.75rem 1rem;
                text-align: left;
                width: 100%;
            }
        }
        
        /* 内容区域样式 */
        .auth-container {
            min-height: calc(100vh - 76px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            background-color: #f8f9fa;
        }
        
        .auth-card {
            width: 100%;
            max-width: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 1rem;
        }
        
        .auth-card h1 {
            font-size: 1.75rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }
        
        .auth-card p {
            color: #6b7280;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 1rem;
        }
        
        /* 表单样式 */
        .form-label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .form-control {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            background-image: none;
        }

        .invalid-feedback {
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.25rem;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
            padding: 0.75rem 1rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 1.5rem;
            padding: 1rem;
        }

        /* 响应式调整 */
        @media (max-width: 576px) {
            .auth-card {
                margin: 0.5rem;
                padding: 1.5rem;
            }

            .auth-card h1 {
                font-size: 1.5rem;
            }

            .form-control, .btn {
                font-size: 1rem;
                padding: 0.625rem 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url('/') }}">
                {{ \App\Models\Setting::get('dashboard_title', config('app.name')) }}
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('renewal.index') }}">
                            <i class="bi bi-clock-history me-1"></i>会员续费
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>会员登录
                        </a>
                    </li>
                    @if(\App\Models\Setting::getBool('register_enabled', true))
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('register') }}">
                            <i class="bi bi-person-plus me-1"></i>会员注册
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" href="https://t.me/Kax0rs" target="_blank">
                            <i class="bi bi-headset me-1"></i>联系客服
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="auth-container">
        <div class="auth-card">
            @yield('content')
        </div>
    </div>



    <!-- Scripts -->
    <script src="{{ asset('assets/js/bootstrap-5.1.3.bundle.min.js') }}"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化 Bootstrap 导航栏
        var navbarToggler = document.querySelector('.navbar-toggler');
        var navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', function() {
                navbarCollapse.classList.toggle('show');
            });
            
            // 点击导航链接后自动收起菜单
            var navLinks = navbarCollapse.querySelectorAll('.nav-link');
            navLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    navbarCollapse.classList.remove('show');
                });
            });
            
            // 点击页面其他地方时收起菜单
            document.addEventListener('click', function(e) {
                if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                    navbarCollapse.classList.remove('show');
                }
            });
        }
    });
    </script>
</body>
</html> 