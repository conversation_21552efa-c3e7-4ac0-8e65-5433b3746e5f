@php
    $user = Auth::user();
    $proxyMode = $user->proxy_mode ?? 'local';
    $proxyStatus = $user->proxy_status ?? 'inactive';
    $isFirstTime = !$user->proxy_last_test && $proxyMode === 'local';
@endphp

<!-- 代理状态栏 -->
<div class="proxy-status-bar mb-3" id="proxyStatusBar">
    <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <!-- 代理模式显示 -->
            <div class="proxy-mode-display me-3">
                <i class="bi bi-globe me-1"></i>
                <span class="fw-medium">请求模式:</span>
                <div class="dropdown d-inline-block ms-1">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="proxyModeDropdown" data-bs-toggle="dropdown">
                        @if($proxyMode === 'local')
                            <i class="bi bi-server me-1"></i>本地模式
                        @elseif($proxyMode === 'free_proxy')
                            <i class="bi bi-gift me-1"></i>免费代理
                        @else
                            <i class="bi bi-shield-check me-1"></i>代理模式
                        @endif
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item {{ $proxyMode === 'local' ? 'active' : '' }}" href="#" data-mode="local">
                                <i class="bi bi-server me-2"></i>本地模式
                            </a>
                        </li>
                        @if(\App\Models\Setting::getBool('free_proxy_enabled', false))
                        <li>
                            <a class="dropdown-item {{ $proxyMode === 'free_proxy' ? 'active' : '' }}" href="#" data-mode="free_proxy">
                                <i class="bi bi-gift me-2"></i>免费代理
                            </a>
                        </li>
                        @endif
                        <li>
                            <a class="dropdown-item {{ $proxyMode === 'proxy' ? 'active' : '' }}" href="#" data-mode="proxy">
                                <i class="bi bi-shield-check me-2"></i>代理模式
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 状态显示 -->
            <div class="proxy-status-display me-3">
                <span class="fw-medium">状态:</span>
                @if($proxyMode === 'local')
                    <span class="badge bg-success ms-1">
                        <i class="bi bi-check-circle me-1"></i>正常
                    </span>
                @elseif($proxyMode === 'free_proxy')
                    @php
                        $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');
                    @endphp
                    @if($freeProxyStatus === 'active')
                        <span class="badge bg-success ms-1">
                            <i class="bi bi-check-circle me-1"></i>正常
                        </span>
                    @else
                        <span class="badge bg-danger ms-1">
                            <i class="bi bi-x-circle me-1"></i>异常
                        </span>
                    @endif
                @else
                    @if($proxyStatus === 'active')
                        <span class="badge bg-success ms-1">
                            <i class="bi bi-check-circle me-1"></i>正常
                        </span>
                        @if($user->proxy_host)
                            <small class="text-muted ms-1">({{ $user->proxy_host }}:{{ $user->proxy_port }})</small>
                        @endif
                    @elseif($proxyStatus === 'testing')
                        <span class="badge bg-warning ms-1">
                            <i class="bi bi-hourglass-split me-1"></i>检测中
                        </span>
                    @else
                        <span class="badge bg-danger ms-1">
                            <i class="bi bi-x-circle me-1"></i>异常
                        </span>
                    @endif
                @endif
            </div>

            <!-- 首次使用提示 -->
            @if($isFirstTime)
            <div class="first-time-tip me-3">
                <span class="badge bg-info">
                    <i class="bi bi-lightbulb me-1"></i>使用代理模式降低账户风控
                </span>
            </div>
            @endif
        </div>

        <!-- 操作按钮 -->
        <div class="proxy-actions">
            @if($proxyMode === 'proxy')
                <button class="btn btn-sm btn-outline-secondary me-2" id="quickTestProxy">
                    <i class="bi bi-arrow-clockwise me-1"></i>检测
                </button>
            @endif
            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#proxySettingsModal">
                <i class="bi bi-gear me-1"></i>设置
            </button>
        </div>
    </div>

    <!-- 错误信息显示 -->
    @if($proxyMode === 'proxy' && $proxyStatus !== 'active' && $user->proxy_error_message)
    <div class="proxy-error-message mt-2">
        <div class="alert alert-warning alert-sm mb-0 py-2">
            <i class="bi bi-exclamation-triangle me-1"></i>
            <small>{{ $user->proxy_error_message }}</small>
            <button class="btn btn-sm btn-link p-0 ms-2" data-bs-toggle="modal" data-bs-target="#proxySettingsModal">
                修改配置
            </button>
        </div>
    </div>
    @endif
</div>

<!-- 智能提醒弹窗 -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="proxyToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="bi bi-globe text-primary me-2"></i>
            <strong class="me-auto">代理提醒</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="proxyToastBody">
            <!-- 动态内容 -->
        </div>
    </div>
</div>

<style>
.proxy-status-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.proxy-mode-display .dropdown-toggle {
    border: none;
    background: rgba(67, 97, 238, 0.1);
    color: #4361ee;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

.proxy-mode-display .dropdown-toggle:hover {
    background: #4361ee;
    color: white;
}

/* 优化下拉菜单样式 - 垂直展开不调整宽度 */
.proxy-mode-display .dropdown-menu {
    min-width: auto !important;
    width: max-content !important;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    margin-top: 0.25rem;
}

.proxy-mode-display .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.proxy-mode-display .dropdown-item:hover {
    background: rgba(67, 97, 238, 0.1);
    color: #4361ee;
}

.proxy-mode-display .dropdown-item.active {
    background: #4361ee;
    color: white;
}

.proxy-mode-display .dropdown-item i {
    width: 16px;
    text-align: center;
}

.proxy-status-display .badge {
    font-size: 0.75rem;
}

.proxy-actions .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

.first-time-tip .badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.alert-sm {
    font-size: 0.875rem;
}

.toast {
    min-width: 300px;
}

/* 确保toast在模态框之上显示 */
.toast-container {
    z-index: 1070 !important; /* Bootstrap模态框的z-index是1055，backdrop是1050 */
}

/* 确保toast本身也有足够高的z-index */
.toast {
    z-index: 1070 !important;
    position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .proxy-status-bar {
        padding: 0.5rem;
    }
    
    .proxy-status-bar .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .proxy-actions {
        margin-top: 0.5rem;
        width: 100%;
    }
    
    .first-time-tip {
        margin-top: 0.5rem;
        margin-right: 0 !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 快速模式切换
    document.querySelectorAll('[data-mode]').forEach(function(item) {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const mode = this.dataset.mode;
            quickSwitchMode(mode);
        });
    });

    // 快速检测代理
    const quickTestBtn = document.getElementById('quickTestProxy');
    if (quickTestBtn) {
        quickTestBtn.addEventListener('click', function() {
            quickTestProxy();
        });
    }

    // 监听其他页面的代理模式变更
    window.addEventListener('storage', function(e) {
        if (e.key === 'proxy_mode_changed') {
            const newMode = e.newValue;
            if (newMode) {
                updateProxyModeDisplay(newMode);
                showProxyToast('info', '代理模式已同步为: ' + (newMode === 'local' ? '本地模式' : '代理模式'));
            }
        }
    });
});

// 快速切换模式（全局同步版本）
function quickSwitchMode(mode) {
    // 立即更新当前页面显示
    updateProxyModeDisplay(mode);

    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('proxy_mode', mode);

    fetch('/user/proxy/quick-switch', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 广播模式变更到其他页面
            broadcastProxyModeChange(mode);
            let modeText = '本地模式';
            if (mode === 'free_proxy') {
                modeText = '免费代理';
            } else if (mode === 'proxy') {
                modeText = '代理模式';
            }
            showProxyToast('success', '代理模式已切换为: ' + modeText);

            // 立即刷新页面以更新状态显示
            setTimeout(() => location.reload(), 1000);
        } else {
            showProxyToast('error', data.message);
            // 失败时恢复显示
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        showProxyToast('error', '切换失败，请配置好代理IP');
        // 失败时恢复显示
        setTimeout(() => location.reload(), 1000);
    });
}

// 快速检测代理
function quickTestProxy() {
    const btn = document.getElementById('quickTestProxy');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>检测中...';
    btn.disabled = true;
    
    fetch('/user/proxy/quick-test', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showProxyToast('success', '代理连接正常');
        } else {
            showProxyToast('error', '代理连接失败: ' + data.message);
        }
        // 无论成功或失败都刷新页面以更新状态显示
        setTimeout(() => location.reload(), 1000);
    })
    .catch(error => {
        showProxyToast('error', '检测失败，请重试');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 更新代理模式显示
function updateProxyModeDisplay(mode) {
    // 更新模式选择按钮
    const modeButtons = document.querySelectorAll('[data-mode]');
    modeButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.mode === mode) {
            btn.classList.add('active');
        }
    });

    // 更新状态显示文字
    const modeText = document.querySelector('.proxy-mode-text');
    if (modeText) {
        let displayText = '本地模式';
        if (mode === 'free_proxy') {
            displayText = '免费代理';
        } else if (mode === 'proxy') {
            displayText = '代理模式';
        }
        modeText.textContent = displayText;
    }
}

// 广播代理模式变更到其他页面
function broadcastProxyModeChange(mode) {
    localStorage.setItem('proxy_mode_changed', mode);
    // 立即清除，避免重复触发
    setTimeout(() => localStorage.removeItem('proxy_mode_changed'), 100);
}

// 显示提醒弹窗
function showProxyToast(type, message) {
    const toast = document.getElementById('proxyToast');
    const toastBody = document.getElementById('proxyToastBody');

    let icon;
    switch(type) {
        case 'success':
            icon = 'bi-check-circle text-success';
            break;
        case 'error':
            icon = 'bi-exclamation-triangle text-danger';
            break;
        default:
            icon = 'bi-info-circle text-info';
    }
    toastBody.innerHTML = `<i class="bi ${icon} me-2"></i>${message}`;

    const bsToast = new bootstrap.Toast(toast, {
        delay: 3000  // 3秒后自动消失
    });
    bsToast.show();
}
</script>
