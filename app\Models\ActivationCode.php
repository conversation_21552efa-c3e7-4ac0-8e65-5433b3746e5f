<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ActivationCode extends Model
{
    protected $fillable = [
        'code',
        'valid_days',
        'expires_at',
    ];

    protected $guarded = [
        'id',
        'is_used',
        'user_id',
        'created_by_admin_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function createdByAdmin()
    {
        return $this->belongsTo(Admin::class, 'created_by_admin_id');
    }

    /**
     * 获取精确的剩余天数（用于计算）
     */
    public function getRemainingDaysAttribute()
    {
        if ($this->is_used) {
            // 如果已使用，返回0
            return 0;
        } else {
            // 如果未使用，显示设定的有效天数
            return $this->valid_days;
        }
    }

    /**
     * 获取格式化的剩余时间显示
     */
    public function getFormattedRemainingTimeAttribute()
    {
        if ($this->is_used) {
            return '已使用';
        } else {
            return $this->valid_days . '天';
        }
    }

    public function getStatusAttribute()
    {
        if ($this->is_used) {
            return '已使用';
        }
        return '未使用';
    }

    public function canBeDeleted()
    {
        // 只要未使用就可以删除
        return !$this->is_used;
    }

    public static function generateCode()
    {
        do {
            $code = Str::random(10); // 生成10位随机字符串（包含大小写字母和数字）
        } while (static::where('code', $code)->exists());

        return $code;
    }
} 