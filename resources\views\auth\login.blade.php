@extends('layouts.guest')

@section('content')
<h1>{{ \App\Models\Setting::get('login_title', config('app.name')) }}</h1>
<p>会员登录</p>

<form method="POST" action="{{ route('login') }}" class="space-y-4">
    @csrf
    
    <div class="mb-3">
        <label for="username" class="form-label">用户名</label>
        <input id="username" type="text" name="username" value="{{ old('username') ?? session('remembered_username') }}" 
            class="form-control @error('username') is-invalid @enderror" required autofocus>
        @error('username')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="password" class="form-label">密码</label>
        <input id="password" type="password" name="password" 
            class="form-control @error('password') is-invalid @enderror" required>
        @error('password')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3 d-flex justify-content-between align-items-center">
        <div class="form-check">
            <input type="checkbox" name="remember" id="remember" class="form-check-input" 
                {{ old('remember') ? 'checked' : '' }}>
            <label class="form-check-label" for="remember">记住我</label>
        </div>
        @if(\App\Models\Setting::getBool('register_enabled', true))
        <div>
            <a href="{{ route('register') }}" class="text-decoration-none">还没有账号？立即注册</a>
        </div>
        @endif
    </div>

    @error('expired')
        <div class="alert alert-danger">{{ $message }}</div>
    @enderror

    @if(session('error'))
        <div class="alert alert-danger">{{ session('error') }}</div>
    @endif

    <button type="submit" class="btn btn-primary w-100">
        登录
    </button>
</form>
@endsection 