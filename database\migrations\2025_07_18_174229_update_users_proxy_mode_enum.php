<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 修改 proxy_mode 字段的枚举值，添加 free_proxy
        DB::statement("ALTER TABLE users MODIFY COLUMN proxy_mode ENUM('local', 'free_proxy', 'proxy') DEFAULT 'local'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚时移除 free_proxy 选项，先将所有 free_proxy 改为 local
        DB::statement("UPDATE users SET proxy_mode = 'local' WHERE proxy_mode = 'free_proxy'");
        DB::statement("ALTER TABLE users MODIFY COLUMN proxy_mode ENUM('local', 'proxy') DEFAULT 'local'");
    }
};
