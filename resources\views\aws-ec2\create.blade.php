@extends('layouts.app')

@section('title', 'EC2实例 - 创建实例')

@push('scripts')
<!-- AWS统一数据管理器 -->
<script src="{{ asset('js/aws-unified-data-manager.js') }}"></script>
@endpush

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 0; /* 全屏模式去掉圆角 */
    box-shadow: none; /* 全屏模式去掉阴影 */
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 0; /* 全屏模式去掉底部间距 */
}

/* 只对主卡片应用全屏高度 */
.container-fluid > .row > .col-12 > .card {
    min-height: 100vh; /* 确保全屏高度 */
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: #f8f9fa; /* 全屏模式添加背景色 */
    border-bottom: 1px solid rgba(0,0,0,.1);
    padding: 1.5rem; /* 统一内边距 */
}

.card-body {
    padding: 2rem; /* 增加内边距 */
    background: #fff;
}

.steps-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.1);
}

.step-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-label {
    color: #667eea;
    font-weight: 600;
}

.step-connector {
    width: 60px;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    margin-top: -20px;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.section-title {
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.section-icon {
    color: #667eea;
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.os-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 1.5rem;
}

.os-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.8rem 0.4rem 0.6rem 0.4rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    aspect-ratio: 1.4/1; /* 进一步减少高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.os-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.os-card:hover::before {
    left: 100%;
}

.os-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.os-card.selected {
    border-color: #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4) !important;
    border-width: 3px !important;
}

.os-card.selected::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 15px;
    background: #667eea;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.os-icon {
    width: 36px;
    height: 36px;
    margin: 0 auto 0.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.os-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* SUSE图标备用样式 */
.os-card[data-os="SUSE Linux"] .os-icon .suse-fallback {
    font-size: 1.5rem;
    color: #0c322c;
    display: none;
    text-align: center;
    line-height: 36px;
}

.os-card[data-os="SUSE Linux"] .os-icon img.error {
    display: none;
}

.os-card[data-os="SUSE Linux"] .os-icon img.error + .suse-fallback {
    display: block;
}

.os-name {
    font-weight: 600;
    color: #333;
    font-size: 0.8rem;
    text-align: center;
    line-height: 1.1;
}

.ami-selection {
    display: none;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.ami-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.8rem;
}

.ami-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.5rem; /* 减少内边距 */
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    align-items: flex-start; /* 水平左对齐 */
    text-align: left; /* 文字左对齐 */
}

.ami-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.15);
}

.ami-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.ami-card.selected::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 15px;
    background: #667eea;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

.ami-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.ami-description {
    display: none; /* 隐藏AMI描述 */
}

.ami-id {
    display: none; /* 隐藏AMI ID */
}

/* AWS账户选择器样式 */
.account-select-container {
    position: relative;
}

.account-search {
    border-radius: 6px;
    border: 1px solid #ced4da;
    cursor: pointer;
    background-color: white; /* 确保背景是白色 */
}

.account-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 120px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.account-option:hover {
    background-color: #f8f9fa;
}

.account-option:last-child {
    border-bottom: none;
}

.account-option.highlighted {
    background-color: #e3f2fd;
}

.account-option.selected {
    background-color: #c5f1ff;
    border-left: 3px solid #28a745;
}

/* 禁用账户样式 */
.account-option.disabled {
    opacity: 0.5;
    background: #f8f9fa;
    cursor: not-allowed;
    color: #6c757d;
}

.account-option.disabled:hover {
    background: #f8f9fa;
}

/* 统一下拉框样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: white;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: 0;
}

.form-control:hover {
    border-color: #adb5bd;
}

select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}



/* OS卡片禁用状态 */
.os-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.os-card.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 密码输入框样式 */
.password-input-wrapper {
    position: relative;
}

.password-input-wrapper input {
    padding-right: 45px;
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    font-size: 16px;
    transition: color 0.3s ease;
    z-index: 10;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle-btn:hover {
    color: #495057;
}

.password-toggle-btn:focus {
    outline: none;
}

.instance-type-search {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.instance-type-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.instance-type-dropdown.show {
    display: block;
}

.instance-type-option {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.instance-type-option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.instance-type-option:last-child {
    border-bottom: none;
}

.instance-type-name {
    font-weight: 600;
    color: #2c3e50;
}

.instance-type-specs {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 选中的实例类型高亮 */
.instance-type-option.selected {
    background: rgba(102, 126, 234, 0.15);
    border-left: 4px solid #667eea;
}

.instance-type-option.selected .instance-type-name {
    color: #667eea;
    font-weight: 700;
}

/* 搜索匹配项高亮 
.instance-type-option.match {
    background: rgba(255, 193, 7, 0.1);
}*/

/* 搜索时隐藏非匹配项 */
.instance-type-dropdown.searching .instance-type-option:not(.match) {
    display: none;
}

/* 当没有搜索时，显示所有项 */
.instance-type-dropdown:not(.searching) .instance-type-option {
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-label {
    color: #2c3e50;
    font-weight: 500;
}

@media (max-width: 768px) {
    .os-selection-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
    }

    .steps-indicator {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .step-connector {
        display: none;
    }

    .main-card {
        margin: 0 1rem;
        padding: 1rem;
    }

    .header-title {
        font-size: 2rem;
    }
}



/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}




</style>
@endpush

@section('content')
<div class="container-fluid px-0">
    <div class="row g-0">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="bi bi-server me-2"></i>
                                创建EC2实例
                            </h4>
                            <p class="text-muted mb-0 mt-1">配置您的云服务器实例</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <a href="{{ route('aws-ec2.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>

                <div class="card-body">

            <!-- 步骤指示器 -->
            <div class="steps-indicator">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-label">选择系统</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-label">配置实例</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-label">存储配置</div>
                </div>
                <div class="step-connector"></div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">网络安全</div>
                </div>
            </div>

            <!-- 表单内容 -->
            <div class="content-section">
                <form id="createInstanceForm" action="{{ route('aws-ec2.create-instance') }}" method="POST">
                    @csrf

                    <!-- 账户选择 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-user-circle section-icon"></i>
                            选择AWS账户
                        </h3>

                        <!-- 单个账户选择模式 -->
                        <div class="row">
                            <div class="col-md-6">
                                <label for="account_id" class="form-label">AWS账户 *</label>
                                <div class="account-select-container">
                                    <input type="text" class="form-control account-search" id="account_search" placeholder="搜索AWS账户..." autocomplete="off">
                                    <input type="hidden" id="account_id" name="account_id" required>
                                    <div class="account-dropdown" id="accountDropdown" style="display: none;">
                                        @forelse($accounts as $account)
                                            @php
                                                $isAvailable = $account->status === 0 || $account->status === 1;
                                                $statusConfig = [
                                                    0 => ['icon' => 'bi-question-circle-fill', 'color' => 'text-secondary', 'text' => '未测'],
                                                    1 => ['icon' => 'bi-check-circle-fill', 'color' => 'text-success', 'text' => '可用'],
                                                    2 => ['icon' => 'bi-x-circle-fill', 'color' => 'text-danger', 'text' => '封禁'],
                                                    3 => ['icon' => 'bi-exclamation-triangle-fill', 'color' => 'text-warning', 'text' => '无效']
                                                ];
                                                $status = $statusConfig[$account->status] ?? $statusConfig[0];
                                            @endphp
                                            <div class="account-option {{ $isAvailable ? '' : 'disabled' }}"
                                                 data-value="{{ $isAvailable ? $account->id : '' }}"
                                                 data-status="{{ $account->status }}">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span>{{ $account->account_name }}</span>
                                                    <small class="ms-2">
                                                        <i class="bi {{ $status['icon'] }} {{ $status['color'] }}"></i>
                                                        {{ $status['text'] }}
                                                    </small>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="account-option" data-value="">暂无账户</div>
                                        @endforelse
                                    </div>
                                </div>
                                @if(count($accounts) == 0)
                                    <small class="text-muted">请先在账户管理中添加AWS账户</small>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <label for="region" class="form-label">地区 *</label>
                                <select class="form-control" id="region" name="region" required>
                                    <option value="">请先选择账户</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 操作系统选择 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-desktop section-icon"></i>
                            选择操作系统
                        </h3>
                        <div class="os-selection-grid" id="osSelectionGrid">
                            <!-- 操作系统选项将通过JavaScript从后端动态加载 -->
                            <div class="loading-placeholder" id="osLoadingPlaceholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                正在加载操作系统...
                            </div>
                        </div>

                        <!-- AMI版本选择 -->
                        <div class="ami-selection" id="amiSelection" style="display: none;">
                            <h4 class="mb-3">
                                <i class="fas fa-code-branch"></i>
                                选择系统版本
                            </h4>
                            <div class="ami-grid" id="amiGrid">
                                <!-- AMI选项将通过JavaScript动态加载 -->
                            </div>
                        </div>

                        <input type="hidden" id="os_type" name="os_type">
                        <input type="hidden" id="ami_id" name="ami_id">
                    </div>

                    <!-- 实例配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs section-icon"></i>
                            实例配置
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="instance_name" class="form-label">实例名称 *</label>
                                <input type="text" class="form-control" id="instance_name" name="instance_name" required placeholder="输入实例名称">
                            </div>
                            <div class="col-md-6">
                                <label for="instance_type" class="form-label">实例类型 *</label>
                                <div class="instance-type-search">
                                    <input type="text" class="search-input" id="instance_type" name="instance_type" placeholder="搜索实例类型，如：t3.micro" required>
                                    <div class="instance-type-dropdown" id="instanceTypeDropdown">
                                        <!-- 实例类型选项将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="instance_count" class="form-label">实例数量 *</label>
                                <input type="number" class="form-control" id="instance_count" name="instance_count" required min="1" max="20" value="1" placeholder="输入实例数量">
                                <small class="text-muted">可创建1-20个实例</small>
                            </div>
                        </div>
                    </div>

                    <!-- 存储配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-hdd section-icon"></i>
                            存储配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 存储配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>IOPS</strong>：每秒输入/输出操作数，决定磁盘的读写性能。数值越高，磁盘响应越快</li>
                                <li><strong>吞吐量 (MiB/s)</strong>：每秒传输的数据量，影响大文件传输速度。适用于需要高带宽的应用</li>
                                <li><strong>gp3</strong>：最新通用SSD，性价比最高，可独立配置IOPS和吞吐量</li>
                                <li><strong>io1/io2</strong>：高性能SSD，适用于数据库等对IOPS要求极高的应用</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="volume_size" class="form-label">根卷大小 (GB) *</label>
                                <input type="number" class="form-control" id="volume_size" name="volume_size" value="30" min="8" max="16384" required>
                            </div>
                            <div class="col-md-3">
                                <label for="volume_type" class="form-label">卷类型 *</label>
                                <select class="form-control" id="volume_type" name="volume_type" required>
                                    <option value="gp3">gp3 (通用SSD)</option>
                                    <option value="gp2">gp2 (通用SSD)</option>
                                    <option value="io1">io1 (预配置IOPS SSD)</option>
                                    <option value="io2">io2 (预配置IOPS SSD)</option>
                                    <option value="st1">st1 (吞吐量优化HDD)</option>
                                    <option value="sc1">sc1 (冷HDD)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="iops" class="form-label">IOPS</label>
                                <input type="number" class="form-control" id="iops" name="iops" placeholder="自动" min="100" max="64000">
                            </div>
                            <div class="col-md-3">
                                <label for="throughput" class="form-label">吞吐量 (MiB/s)</label>
                                <input type="number" class="form-control" id="throughput" name="throughput" placeholder="自动" min="125" max="1000">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="hidden" name="delete_on_termination" value="0">
                                    <input class="form-check-input" type="checkbox" id="delete_on_termination" name="delete_on_termination" value="1" checked>
                                    <label class="form-check-label" for="delete_on_termination">
                                        终止时删除卷
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="encrypted" name="encrypted">
                                    <label class="form-check-label" for="encrypted">
                                        加密卷
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 网络配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-network-wired section-icon"></i>
                            网络配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 网络配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>子网 (Subnet)</strong>：VPC内的网络分段，决定实例的网络位置和可用区。公有子网可访问互联网，私有子网仅内网访问</li>
                                <li><strong>安全组</strong>：虚拟防火墙，控制实例的入站和出站流量规则</li>
                                <li><strong>公网IP</strong>：是否为实例分配可从互联网访问的IP地址</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="subnet_id" class="form-label">子网</label>
                                <select class="form-control" id="subnet_id" name="subnet_id">
                                    <option value="">请先选择地区</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="security_group_ids" class="form-label">安全组 *</label>
                                <select class="form-control" id="security_group_ids" name="security_group_ids[]" multiple required>
                                    <option value="">请先选择地区</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="hidden" name="associate_public_ip" value="0">
                                    <input class="form-check-input" type="checkbox" id="associate_public_ip" name="associate_public_ip" value="1" checked>
                                    <label class="form-check-label" for="associate_public_ip">
                                        分配公网IP
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 密钥对和密码配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-key section-icon"></i>
                            访问配置
                        </h3>
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle"></i> <strong>访问配置要求：</strong>密钥对和登录密码至少需要配置一个，用于实例登录访问
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="key_name" class="form-label">密钥对 <span class="text-muted">(二选一)</span></label>
                                <select class="form-control" id="key_name" name="key_name">
                                    <option value="">请先选择地区</option>
                                </select>
                                <small class="form-text text-muted">推荐使用密钥对，更安全</small>
                            </div>
                            <div class="col-md-6">
                                <label for="password" class="form-label">登录密码 <span class="text-muted">(二选一)</span></label>
                                <div class="password-input-wrapper">
                                    <input type="password" class="form-control" id="password" name="password" placeholder="设置登录密码（至少8位）" minlength="8">
                                    <button type="button" class="password-toggle-btn" id="passwordToggle">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">Windows系统为Administrator密码，Linux系统为root密码</small>
                            </div>
                        </div>
                    </div>

                    <!-- 高级配置 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-sliders-h section-icon"></i>
                            高级配置
                        </h3>
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle"></i> 高级配置说明：</h6>
                            <ul class="mb-0">
                                <li><strong>启用详细监控</strong>：提供更详细的CloudWatch监控指标，1分钟间隔（默认5分钟），需额外费用</li>
                                <li><strong>EBS优化</strong>：为实例提供专用的EBS带宽，提高存储性能，适用于I/O密集型应用</li>
                                <li><strong>终止保护</strong>：防止意外删除实例，启用后需要先禁用保护才能终止实例</li>
                            </ul>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="monitoring" name="monitoring">
                                    <label class="form-check-label" for="monitoring">
                                        启用详细监控
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ebs_optimized" name="ebs_optimized">
                                    <label class="form-check-label" for="ebs_optimized">
                                        EBS优化
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="termination_protection" name="termination_protection">
                                    <label class="form-check-label" for="termination_protection">
                                        终止保护
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 开机脚本 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs section-icon"></i>
                            开机脚本
                        </h3>

                        <!-- 脚本配置功能卡片 - 显眼位置 -->
                        <div class="card border-primary mb-4" style="border-width: 2px;" id="userDataCard">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    脚本配置
                                    <span class="badge bg-light text-primary ms-2">可选功能</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle"></i> 功能说明：</h6>
                                            <ul class="mb-0 small">
                                                <li>在实例首次启动时自动执行命令</li>
                                                <li>具有管理员权限，可安装软件、配置服务</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb"></i> 使用场景：</h6>
                                            <ul class="mb-0 small">
                                                <li>自动安装Docker、Nginx等软件</li>
                                                <li>配置安全设置、部署应用</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enable_user_data" name="enable_user_data" style="transform: scale(1.2);">
                                            <label class="form-check-label fw-bold" for="enable_user_data" style="margin-left: 10px;">
                                                启用脚本配置功能
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div id="userDataSection" style="display: none;">
                                    <hr class="my-3">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="user_data_template" class="form-label fw-bold">选择预设模板</label>
                                            <select class="form-control" id="user_data_template" name="user_data_template">
                                                <option value="">请选择模板...</option>
                                            </select>
                                            <small class="text-muted">选择常用的预设脚本模板</small>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">脚本大小监控</label>
                                            <div class="form-control-plaintext">
                                                <span id="script_size" class="fw-bold">0</span> / 16,384 字符
                                                <div class="progress mt-1" style="height: 6px;">
                                                    <div class="progress-bar" id="script_progress" role="progressbar" style="width: 0%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <label for="user_data" class="form-label fw-bold">脚本内容</label>
                                            <textarea class="form-control" id="user_data" name="user_data" rows="10"
                                                      placeholder="请输入脚本内容，例如：&#10;#!/bin/bash&#10;yum update -y&#10;yum install -y docker&#10;systemctl start docker"
                                                      style="font-family: 'Courier New', monospace; font-size: 13px;"></textarea>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-lightbulb text-primary"></i>
                                                    <strong>提示：</strong>Linux脚本以 <code>#!/bin/bash</code> 开头，Windows脚本使用 <code>&lt;powershell&gt;</code> 标签
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-secondary me-3" onclick="history.back()">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-rocket me-2"></i>创建实例
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 加载遮罩层 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 class="mb-3">正在创建EC2实例</h5>
            <p class="text-muted mb-3" id="progressText">正在处理请求...</p>
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// AWS AMI数据管理器初始化
let amiDataManager = null;
let amiDataReady = false;
let backendAmiData = null;

// 页面加载时使用统一数据管理器加载AMI数据
$(document).ready(function() {
    console.log('🚀 页面加载完成，开始使用统一数据管理器加载数据...');

    // 使用统一数据管理器加载AMI数据
    awsDataManager.getAmiData(function(amiData) {
        if (amiData) {
            backendAmiData = amiData;
            console.log('✅ 统一数据管理器AMI数据加载成功');
            initializePageWithBackendData();
        } else {
            console.error('❌ 统一数据管理器AMI数据加载失败');
            showErrorMessage('数据加载失败，请刷新页面重试');
        }
    });
});

// 使用后端数据初始化页面
function initializePageWithBackendData() {
    try {
        // 加载地区列表
        loadRegionsFromBackend();

        // 加载操作系统列表
        loadOperatingSystemsFromBackend();

        // 初始化事件监听器
        initializeEventListeners();

        // 初始化其他功能
        initializeOtherFeatures();

        console.log('✅ 页面初始化完成');
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        showErrorMessage('页面初始化失败，请刷新页面重试');
    }
}

// 从后端数据加载地区列表
function loadRegionsFromBackend() {
    if (!backendAmiData || !backendAmiData.regions) {
        console.error('❌ 后端地区数据不可用');
        return;
    }

    const regionSelect = $('#region');
    regionSelect.empty().append('<option value="">请选择地区</option>');

    Object.keys(backendAmiData.regions).forEach(regionCode => {
        const region = backendAmiData.regions[regionCode];
        regionSelect.append(`<option value="${regionCode}">${region.name}</option>`);
    });

    console.log(`✅ 已加载 ${Object.keys(backendAmiData.regions).length} 个地区`);
}

// 从后端数据加载操作系统列表
function loadOperatingSystemsFromBackend() {
    if (!backendAmiData || !backendAmiData.system_icons) {
        console.error('❌ 后端操作系统数据不可用');
        return;
    }

    const osGrid = $('#osSelectionGrid');
    osGrid.empty();

    Object.keys(backendAmiData.system_icons).forEach(osName => {
        const iconConfig = backendAmiData.system_icons[osName];
        const osCard = createOSCard(osName, iconConfig);
        osGrid.append(osCard);
    });

    console.log(`✅ 已加载 ${Object.keys(backendAmiData.system_icons).length} 个操作系统`);
}

// 创建操作系统卡片
function createOSCard(osName, iconConfig) {
    const iconHtml = iconConfig.type === 'local'
        ? `<img src="${iconConfig.path}" alt="${osName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
           <i class="${iconConfig.fallback}" style="display:none; font-size: 2rem;"></i>`
        : `<i class="${iconConfig.fallback}" style="font-size: 2rem;"></i>`;

    return `
        <div class="os-card" data-os="${osName}">
            <div class="os-icon">
                ${iconHtml}
            </div>
            <div class="os-name">${osName}</div>
        </div>
    `;
}

// 清空账户相关选项
function clearAccountRelatedOptions() {
    $('#subnet_id').empty().append('<option value="">请先选择账户和地区</option>');
    $('#security_group_ids').empty().append('<option value="">请先选择账户和地区</option>');
    $('#key_name').empty().append('<option value="">请先选择账户和地区</option>');
    console.log('🧹 已清空账户相关选项');
}

// 初始化事件监听器
function initializeEventListeners() {
    // 账户选择变化事件
    $('#account_id').on('change', function() {
        const accountId = $(this).val();
        if (accountId) {
            console.log('账户选择变化:', accountId);

            // 先保存旧的无效账户ID
            const oldInvalidAccountId = window.invalidAccountId;

            // 完全重置账户验证状态
            awsDataManager.resetAccountValidationState();

            // 强制重置全局状态，确保新账户能正常工作
            window.accountValidationFailed = false;
            window.invalidAccountId = null;
            console.log('🔄 账户切换：已强制重置验证状态');

            // 清除旧账户的缓存数据，确保使用新账户重新请求
            if (oldInvalidAccountId && oldInvalidAccountId !== accountId) {
                awsDataManager.clearAccountCache(oldInvalidAccountId);
                console.log(`🧹 账户切换：已清除旧无效账户 ${oldInvalidAccountId} 的缓存`);
            }
            // 也清除当前账户的缓存，确保重新请求
            awsDataManager.clearAccountCache(accountId);
            console.log(`🧹 账户切换：已清除当前账户 ${accountId} 的缓存`);

            // 重新启用创建实例按钮
            const $createButton = $('button[type="submit"], #createInstanceBtn, #create-instance-btn, .create-instance-btn');
            $createButton.prop('disabled', false)
                         .removeClass('disabled')
                         .css('background-color', '')
                         .css('cursor', '');

            // 加载地区
            loadRegions(accountId);

            // 立即加载账户相关数据（使用默认地区）
            loadAccountDataImmediately(accountId);

            // 检查操作系统卡片状态
            checkOSCardStatus();
        } else {
            // 清空选项
            $('#region').html('<option value="">请先选择账户</option>');
            clearAccountRelatedOptions();
            clearDependentFields();
        }
    });

    // 地区选择变化事件
    $('#region').on('change', function() {
        const selectedRegion = $(this).val();
        const accountId = $('#account_id').val();

        console.log('🔍 create页面地区选择变化:', {selectedRegion, accountId, accountValidationFailed: window.accountValidationFailed});

        updateSystemsForRegion(selectedRegion);
        clearSystemSelection();

        // 如果账户和地区都已选择，则加载账户相关数据
        // 注意：即使之前账户验证失败，切换账户后也应该重新尝试
        if (accountId && selectedRegion) {
            console.log(`✅ 条件满足，地区选择变化: ${selectedRegion}，开始加载账户 ${accountId} 的相关数据`);

            // 清除该账户在该地区的缓存，强制重新请求
            const cacheKey = `${accountId}_${selectedRegion}`;
            if (awsDataManager.cache.subnets[cacheKey]) {
                delete awsDataManager.cache.subnets[cacheKey];
                console.log(`🧹 已清除子网缓存: ${cacheKey}`);
            }
            if (awsDataManager.cache.securityGroups[cacheKey]) {
                delete awsDataManager.cache.securityGroups[cacheKey];
                console.log(`🧹 已清除安全组缓存: ${cacheKey}`);
            }
            if (awsDataManager.cache.keyPairs[cacheKey]) {
                delete awsDataManager.cache.keyPairs[cacheKey];
                console.log(`🧹 已清除密钥对缓存: ${cacheKey}`);
            }

            // 使用统一数据管理器加载账户相关数据
            // 分别调用各个方法，避免回调函数作用域问题
            awsDataManager.getSubnets(accountId, selectedRegion, function(subnets) {
                console.log('🔍 create页面子网回调被调用:', subnets);
                // 无论成功失败都调用更新函数，让更新函数自己处理
                updateSubnetOptions(subnets);
            });

            awsDataManager.getSecurityGroups(accountId, selectedRegion, function(securityGroups) {
                console.log('🔍 create页面安全组回调被调用:', securityGroups);
                // 无论成功失败都调用更新函数，让更新函数自己处理
                updateSecurityGroupOptions(securityGroups);
            });

            awsDataManager.getKeyPairs(accountId, selectedRegion, function(keyPairs) {
                console.log('🔍 create页面密钥对回调被调用:', keyPairs);
                // 无论成功失败都调用更新函数，让更新函数自己处理
                updateKeyPairOptions(keyPairs);
            });
        } else {
            console.log('⚠️ 条件不满足，清空账户相关选项:', {accountId, selectedRegion});
            // 清空账户相关选项
            clearAccountRelatedOptions();
        }
    });

    // 操作系统选择事件（使用事件委托）
    $('#osSelectionGrid').on('click', '.os-card', function() {
        if ($(this).hasClass('disabled')) {
            return false;
        }

        const selectedSystem = $(this).data('os');
        const selectedRegion = $('#region').val();

        if (!selectedRegion) {
            alert('请先选择地区');
            return false;
        }

        // 更新选中状态
        $('.os-card').removeClass('selected');
        $(this).addClass('selected');

        // 加载系统版本
        loadSystemVersions(selectedRegion, selectedSystem);
    });
}

// 根据地区更新可用的操作系统
function updateSystemsForRegion(regionCode) {
    if (!backendAmiData || !regionCode) {
        $('.os-card').hide(); // 隐藏所有系统
        return;
    }

    const regionData = backendAmiData.regions[regionCode];
    if (!regionData || !regionData.systems) {
        $('.os-card').hide(); // 隐藏所有系统
        return;
    }

    const supportedSystems = Object.keys(regionData.systems);

    $('.os-card').each(function() {
        const systemName = $(this).data('os');
        if (supportedSystems.includes(systemName)) {
            $(this).removeClass('disabled').show(); // 显示支持的系统
        } else {
            $(this).addClass('disabled').hide(); // 隐藏不支持的系统
        }
    });

    console.log(`✅ 地区 ${regionCode} 支持 ${supportedSystems.length} 个操作系统`);
}

// 清除系统选择
function clearSystemSelection() {
    $('.os-card').removeClass('selected');
    $('#amiSelection').hide();
    $('#os_type').val('');
    $('#ami_id').val('');
}

// 加载系统版本
function loadSystemVersions(regionCode, systemName) {
    if (!backendAmiData || !regionCode || !systemName) {
        console.error('❌ 缺少必要参数');
        return;
    }

    const regionData = backendAmiData.regions[regionCode];
    if (!regionData || !regionData.systems || !regionData.systems[systemName]) {
        console.error(`❌ 地区 ${regionCode} 不支持系统 ${systemName}`);
        return;
    }

    const systemData = regionData.systems[systemName];
    const versions = systemData.versions || [];

    if (versions.length === 0) {
        console.warn(`⚠️ 系统 ${systemName} 在地区 ${regionCode} 没有可用版本`);
        $('#amiSelection').hide();
        return;
    }

    // 显示版本选择区域
    displaySystemVersions(versions, systemName);

    console.log(`✅ 已加载系统 ${systemName} 的 ${versions.length} 个版本`);
}

// 显示系统版本选择
function displaySystemVersions(versions, systemName) {
    const amiGrid = $('#amiGrid');
    const amiSelection = $('#amiSelection');

    amiGrid.empty();

    versions.forEach(version => {
        const versionCard = `
            <div class="ami-card" data-ami-id="${version.ami_id}">
                <div class="ami-name">${version.display_name}</div>
                <div class="ami-description">${version.name}</div>
                <div class="ami-id">${version.ami_id}</div>
            </div>
        `;
        amiGrid.append(versionCard);
    });

    // 显示版本选择区域
    amiSelection.show();

    // 默认选择第一个版本
    if (versions.length > 0) {
        $('#ami_id').val(versions[0].ami_id);
        $('#os_type').val(systemName);
        amiGrid.find('.ami-card').first().addClass('selected');
    }

    // 添加版本选择事件
    amiGrid.find('.ami-card').on('click', function() {
        amiGrid.find('.ami-card').removeClass('selected');
        $(this).addClass('selected');
        $('#ami_id').val($(this).data('ami-id'));
    });
}

// 显示错误消息
function showErrorMessage(message) {
    // 可以使用toast通知或者alert
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        alert('错误: ' + message);
    }
}

// 根据系统更新版本列表
function updateVersionsForSystem(regionCode, systemName) {
    if (!amiDataManager || !regionCode || !systemName) {
        $('#amiSelection').hide();
        return;
    }

    const versions = amiDataManager.getSystemVersions(regionCode, systemName);
    const amiSelection = $('#amiSelection');
    const amiGrid = $('#amiGrid');

    if (versions.length > 0) {
        // 使用卡片网格显示版本
        let html = '';
        versions.forEach(version => {
            html += `
                <div class="ami-card" data-ami-id="${version.ami_id}">
                    <div class="ami-name">${version.display_name}</div>
                    <div class="ami-description">${version.name}</div>
                    <div class="ami-id">${version.ami_id}</div>
                </div>
            `;
        });
        amiGrid.html(html);

        // 显示版本选择区域
        amiSelection.show();

        // 默认选择第一个版本
        if (versions.length > 0) {
            $('#ami_id').val(versions[0].ami_id);
            $('#os_type').val(systemName);
            // 默认选中第一个卡片
            amiGrid.find('.ami-card').first().addClass('selected');
        }

        console.log(`✅ 系统 ${systemName} 在地区 ${regionCode} 有 ${versions.length} 个版本`);
    } else {
        amiSelection.hide();
        console.warn(`⚠️ 系统 ${systemName} 在地区 ${regionCode} 没有可用版本`);
    }
}

// 版本选择变化时更新隐藏字段
$(document).on('change', '#amiVersion', function() {
    const selectedAmi = $(this).val();
    const selectedOption = $(this).find('option:selected');
    const displayName = selectedOption.data('display');

    $('#ami_id').val(selectedAmi);

    if (selectedAmi) {
        console.log(`✅ 已选择版本: ${displayName} (${selectedAmi})`);
    }
});

// 初始化其他功能
function initializeOtherFeatures() {
    // 原有的其他初始化代码
    initPasswordToggle();
    initAccountSearch();
    initUserDataFeature();
    // ... 其他初始化
}

// 初始化密码显示/隐藏功能
function initPasswordToggle() {
    // 如果页面有密码字段，添加切换功能
    $('.password-toggle').on('click', function() {
        const input = $(this).siblings('input');
        const type = input.attr('type') === 'password' ? 'text' : 'password';
        input.attr('type', type);
        $(this).find('i').toggleClass('fa-eye fa-eye-slash');
    });
}

// 初始化账户搜索功能
function initAccountSearch() {
    // 账户搜索功能
    $('#account_search').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('#account_id option').each(function() {
            const text = $(this).text().toLowerCase();
            $(this).toggle(text.includes(searchTerm) || $(this).val() === '');
        });
    });
}

// 优化错误消息处理（全局函数）
function getOptimizedErrorMessage(message) {
    if (!message) return '创建失败，请检查网络连接或稍后重试';

    // 检查是否包含验证中的消息
    if (message.includes('Your request for accessing resources in this region is being validated')) {
        return '请求正在验证中';
    }

    // 检查是否包含vCPU限制的消息
    if (message.includes('You have requested more vCPU capacity than') || message.includes('VcpuLimitExceeded')) {
        return '开通的vCPU超过限制';
    }

    // 检查子网相关错误
    if (message.includes('InvalidSubnetID.NotFound') || message.includes('does not exist')) {
        return '子网配置错误';
    }

    // 检查安全组相关错误
    if (message.includes('InvalidGroup.NotFound') || (message.includes('security group') && message.includes('does not exist'))) {
        return '安全组配置错误';
    }

    // 检查其他常见AWS错误
    if (message.includes('InvalidAMIID.NotFound')) {
        return 'AMI镜像不存在';
    }

    if (message.includes('UnauthorizedOperation')) {
        return '权限不足';
    }

    if (message.includes('InvalidParameterValue')) {
        return '参数配置错误';
    }

    // 如果错误信息太长，截取前30个字符
    if (message.length > 30) {
        return message.substring(0, 30) + '...';
    }

    // 返回原始消息
    return message;
}

// 全局函数：更新子网选项
function updateSubnetOptions(subnets) {
    const $subnetSelect = $('#subnet_id');
    $subnetSelect.empty();

    // 如果是null或undefined，说明请求失败或账户验证失败
    if (subnets === null || subnets === undefined) {
        $subnetSelect.append('<option value="">该账户暂无可用子网</option>');
        console.log('⚠️ 子网数据为空，可能是账户验证失败或接口获取失败');
        return;
    }

    // 添加默认选项
    $subnetSelect.append('<option value="">请选择子网</option>');

    if (subnets && typeof subnets === 'object' && Object.keys(subnets).length > 0) {
        // 处理后端返回的对象格式 {subnet-id: "display-name"}
        const subnetKeys = Object.keys(subnets);
        subnetKeys.forEach(function(subnetId) {
            const displayName = subnets[subnetId];
            $subnetSelect.append(`<option value="${subnetId}">${displayName}</option>`);
        });

        // 默认选择第一个子网
        if (subnetKeys.length > 0) {
            $subnetSelect.val(subnetKeys[0]);
            console.log(`✅ 已更新 ${subnetKeys.length} 个子网选项，默认选择: ${subnetKeys[0]}`);
        }
    } else if (subnets && Array.isArray(subnets) && subnets.length > 0) {
        // 处理数组格式（备用）
        subnets.forEach(function(subnet) {
            $subnetSelect.append(`<option value="${subnet.id}">${subnet.name} (${subnet.cidr})</option>`);
        });

        // 默认选择第一个子网
        if (subnets.length > 0) {
            $subnetSelect.val(subnets[0].id);
            console.log(`✅ 已更新 ${subnets.length} 个子网选项，默认选择: ${subnets[0].id}`);
        }
    } else {
        // 当subnets为空对象或空数组时，显示该账户暂无子网
        $subnetSelect.append('<option value="">该账户暂无可用子网</option>');
        console.log('⚠️ 该账户暂无可用子网');
    }
}

// 全局函数：更新安全组选项
function updateSecurityGroupOptions(securityGroups) {
    const $securityGroupSelect = $('#security_group_ids');
    $securityGroupSelect.empty();

    // 如果是null或undefined，说明请求失败或账户验证失败
    if (securityGroups === null || securityGroups === undefined) {
        $securityGroupSelect.append('<option value="">该账户暂无可用安全组</option>');
        console.log('⚠️ 安全组数据为空，可能是账户验证失败或接口获取失败');
        return;
    }

    // 添加默认选项
    $securityGroupSelect.append('<option value="">请选择安全组</option>');

    if (securityGroups && Array.isArray(securityGroups) && securityGroups.length > 0) {
        // 处理数组格式 [{GroupId, GroupName, Description}]
        securityGroups.forEach(function(sg) {
            const displayName = `${sg.GroupName} (${sg.Description || sg.GroupId})`;
            $securityGroupSelect.append(`<option value="${sg.GroupId}">${displayName}</option>`);
        });

        // 默认选择第一个安全组
        if (securityGroups.length > 0) {
            $securityGroupSelect.val(securityGroups[0].GroupId);
            console.log(`✅ 已更新 ${securityGroups.length} 个安全组选项，默认选择: ${securityGroups[0].GroupId}`);
        }
    } else {
        // 当securityGroups为空数组时，显示该账户暂无安全组
        $securityGroupSelect.append('<option value="">该账户暂无可用安全组</option>');
        console.log('⚠️ 该账户暂无可用安全组');
    }
}

// 全局函数：更新密钥对选项
function updateKeyPairOptions(keyPairs) {
    const $keyPairSelect = $('#key_name');
    $keyPairSelect.empty();

    console.log('🔍 create页面updateKeyPairOptions被调用:', keyPairs);

    // 如果是null或undefined，说明请求失败或账户验证失败
    if (keyPairs === null || keyPairs === undefined) {
        $keyPairSelect.append('<option value="">该账户暂无可用密钥对</option>');
        console.log('⚠️ 密钥对数据为空，可能是账户验证失败或接口获取失败');
        return;
    }

    if (keyPairs && typeof keyPairs === 'object' && Object.keys(keyPairs).length > 0) {
        // 处理对象格式 {keyName: keyName}
        $keyPairSelect.append('<option value="">请选择密钥对</option>');
        Object.keys(keyPairs).forEach(function(keyName) {
            $keyPairSelect.append(`<option value="${keyName}">${keyName}</option>`);
        });
        console.log(`✅ create页面已更新 ${Object.keys(keyPairs).length} 个密钥对选项`);
    } else if (keyPairs && Array.isArray(keyPairs) && keyPairs.length > 0) {
        // 处理数组格式（备用）
        $keyPairSelect.append('<option value="">请选择密钥对</option>');
        keyPairs.forEach(function(kp) {
            $keyPairSelect.append(`<option value="${kp.name}">${kp.name}</option>`);
        });
        console.log(`✅ create页面已更新 ${keyPairs.length} 个密钥对选项`);
    } else {
        // 当keyPairs为空对象或空数组时，显示该账户暂无密钥对
        $keyPairSelect.append('<option value="">该账户暂无可用密钥对</option>');
        console.log('⚠️ create页面该账户暂无密钥对');
    }
}

$(document).ready(function() {
    let instanceTypes = [];
    let currentAMIs = [];
    let preloadedAMIs = {}; // 预加载的AMI缓存

    // 删除旧的awsFixedData，现在使用AMI数据管理器


    // 设置CSRF token
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // 页面加载时就加载实例类型（不依赖地区选择）
    loadInstanceTypes();

    // 删除旧的initializeFixedData调用



    // 初始化OS卡片状态检查
    checkOSCardStatus();

    // 初始化密码可见功能
    initPasswordToggle();

    // 初始化账户搜索功能
    initAccountSearch();

    // 页面加载时检查URL参数和默认选中的账户
    setTimeout(function() {
        // 首先检查URL参数中的account_id
        const urlParams = new URLSearchParams(window.location.search);
        const urlAccountId = urlParams.get('account_id');

        if (urlAccountId) {

            // 查找对应的账户选项
            const accountOption = $(`.account-option[data-value="${urlAccountId}"]`);
            if (accountOption.length > 0) {
                const accountName = accountOption.text();

                // 设置账户选择器的值和显示
                $('#account_id').val(urlAccountId);
                $('#account_search').val(accountName);

                // 触发change事件以更新相关数据
                $('#account_id').trigger('change');
                loadRegions(urlAccountId);
            }
        } else {
            // 如果URL没有参数，检查是否有默认选中的账户
            const accountId = $('#account_id').val();
            if (accountId) {
                loadRegions(accountId);
            }
        }

        // 初始化时禁用所有系统卡片（因为还没有选择地区）
        filterOSByRegion(null);


    }, 100); // 延迟100ms确保页面元素加载完成

    // 已合并到initializeEventListeners函数中，避免重复监听

    // 操作系统地区过滤函数
    function filterOSByRegion(regionCode) {
        if (!regionCode || !amiDataManager) {
            // 如果没有选择地区，禁用所有系统
            $('.os-card').addClass('disabled');
            return;
        }

        const supportedSystems = amiDataManager.getRegionSystems(regionCode);

        $('.os-card').each(function() {
            const systemName = $(this).data('os');
            const isSupported = supportedSystems.includes(systemName);

            if (isSupported) {
                $(this).removeClass('disabled');
            } else {
                $(this).addClass('disabled');
            }
        });

        console.log(`✅ 地区 ${regionCode} 操作系统过滤完成，支持 ${supportedSystems.length} 个系统`);
    }

    // 旧的地区选择变化处理器已删除，现在使用统一的事件处理器（第1276行）

    // 已删除按需加载网络资源的代码，现在在账户选择时自动加载

    // 操作系统选择（更新为使用AMI数据管理器）
    $('.os-card').click(function() {

        // 检查卡片是否被禁用或隐藏
        if ($(this).hasClass('disabled') || $(this).is(':hidden')) {
            return false;
        }

        // 检查是否已选择账户和地区
        const accountId = $('#account_id').val();
        const region = $('#region').val();

        if (!accountId || !region) {
            // 显示提示信息
            alert('请先选择账户和地区');
            return false;
        }

        const osType = $(this).data('os');

        // 使用AMI数据管理器检查系统支持
        if (amiDataManager && !amiDataManager.isSystemSupported(region, osType)) {
            alert('该操作系统在当前地区不受支持，请选择其他系统');
            return false;
        }

        $('.os-card').removeClass('selected');
        $(this).addClass('selected');

        $('#os_type').val(osType);

        // 使用新的版本选择逻辑
        if (amiDataReady) {
            updateVersionsForSystem(region, osType);
        } else {
            // 备用：显示警告
            console.warn('⚠️ AMI数据尚未加载完成');
            alert('系统数据加载中，请稍后再试');
        }

        updateStepIndicator(2);
    });

    // AMI选择
    $(document).on('click', '.ami-card', function() {
        $('.ami-card').removeClass('selected');
        $(this).addClass('selected');

        const amiId = $(this).data('ami-id');
        $('#ami_id').val(amiId);

        updateStepIndicator(3);
    });

    // 实例类型搜索
    $('#instance_type').click(function() {
        if (instanceTypes.length > 0) {
            $('#instanceTypeDropdown').addClass('show');
        }
    });

    $(document).click(function(e) {
        if (!$(e.target).closest('.instance-type-search').length) {
            $('#instanceTypeDropdown').removeClass('show');
        }
    });

    $('#instance_type').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterInstanceTypes(searchTerm);
    });

    // 实例类型选择
    $(document).on('click', '.instance-type-option', function() {
        const instanceType = $(this).data('type');
        $('#instance_type').val(instanceType);

        // 更新选中状态
        $('.instance-type-option').removeClass('selected');
        $(this).addClass('selected');

        $('#instanceTypeDropdown').removeClass('show');
        updateStepIndicator(4);
    });

    // 卷类型变化时显示/隐藏相关选项
    $('#volume_type').change(function() {
        const volumeType = $(this).val();
        if (volumeType === 'io1' || volumeType === 'io2') {
            $('#iops').prop('disabled', false).attr('placeholder', '必填');
        } else {
            $('#iops').prop('disabled', true).attr('placeholder', '自动');
        }

        if (volumeType === 'gp3') {
            $('#throughput').prop('disabled', false).attr('placeholder', '可选');
        } else {
            $('#throughput').prop('disabled', true).attr('placeholder', '自动');
        }
    });

    // 表单提交
    $('#createInstanceForm').submit(function(e) {
        e.preventDefault();

        if (!validateForm()) {
            return false;
        }

        // 防重点击：检查是否正在提交
        const submitBtn = $(this).find('button[type="submit"]');
        if (submitBtn.prop('disabled')) {
            return false;
        }

        // 禁用提交按钮，防止重复点击
        submitBtn.prop('disabled', true);
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>创建中...');

        // 单个账户创建模式
        handleSingleCreate(submitBtn, originalText);
    });

    // 显示加载进度条
    function showLoadingProgress() {
        document.getElementById('loadingOverlay').style.display = 'flex';
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        let progress = 1;
        let lastUpdate = Date.now();

        // 模拟进度
        const progressInterval = setInterval(() => {
            const now = Date.now();
            if (progress < 90 && (now - lastUpdate) >= 1000) {
                progress += Math.floor(Math.random() * 15) + 5;
                if (progress > 89) progress = 89;
                progressBar.style.width = progress + '%';

                // 更新进度文本
                if (progress < 30) {
                    progressText.textContent = '正在验证参数...';
                } else if (progress < 60) {
                    progressText.textContent = '正在创建实例...';
                } else {
                    progressText.textContent = '正在配置网络...';
                }
                lastUpdate = now;
            }
        }, 500);

        return progressInterval;
    }

    // 隐藏加载进度条
    function hideLoadingProgress(progressInterval) {
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // 设置进度为100%
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        progressBar.style.width = '100%';
        progressText.textContent = '完成！';

        // 延迟隐藏
        setTimeout(() => {
            document.getElementById('loadingOverlay').style.display = 'none';
            progressBar.style.width = '0%';
            progressText.textContent = '正在处理请求...';
        }, 1000);
    }

    // 处理单个账户创建
    function handleSingleCreate(submitBtn, originalText) {
        const formData = $('#createInstanceForm').serialize();

        // 显示进度条
        const progressInterval = showLoadingProgress();

        $.ajax({
            url: $('#createInstanceForm').attr('action'),
            method: 'POST',
            data: formData,
            success: function(response) {
                hideLoadingProgress(progressInterval);

                // 恢复按钮状态
                submitBtn.prop('disabled', false).html(originalText);

                if (response.success) {
                    alert('EC2实例创建成功！');
                    window.location.href = '{{ route("aws-ec2.index") }}';
                } else {
                    // 检查是否是代理异常错误
                    if (response.message && (response.message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
                                           response.message.includes('当前代理模式IP异常，请切换其他模式进行操作'))) {
                        // 显示代理异常提示
                        if (typeof showProxyToast === 'function') {
                            showProxyToast('error', response.message);
                        } else {
                            alert(response.message);
                        }
                        return; // 不刷新页面，让用户切换代理模式
                    }

                    const optimizedMessage = getOptimizedErrorMessage(response.message);
                    alert('创建失败：' + optimizedMessage);
                }
            },
            error: function(xhr) {
                hideLoadingProgress(progressInterval);

                // 恢复按钮状态
                submitBtn.prop('disabled', false).html(originalText);

                const response = xhr.responseJSON;
                let errorMessage = '创建失败，请检查网络连接或稍后重试';

                if (response && response.message) {
                    // 检查是否是代理异常错误
                    if (response.message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
                        response.message.includes('当前代理模式IP异常，请切换其他模式进行操作')) {
                        // 显示代理异常提示
                        if (typeof showProxyToast === 'function') {
                            showProxyToast('error', response.message);
                        } else {
                            alert(response.message);
                        }
                        return; // 不刷新页面，让用户切换代理模式
                    }

                    errorMessage = '创建失败：' + getOptimizedErrorMessage(response.message);
                }

                alert(errorMessage);
            }
        });
    }



    // 加载地区列表（使用AMI数据管理器）
    function loadRegions(accountId) {
        if (!amiDataManager) {
            console.warn('⚠️ AMI数据管理器未就绪，无法加载地区');
            return;
        }

        let options = '<option value="">请选择地区</option>';
        const regions = amiDataManager.getRegions();

        Object.keys(regions).forEach(function(regionCode) {
            const region = regions[regionCode];
            options += `<option value="${regionCode}">${region.name}</option>`;
        });

        $('#region').html(options);
        console.log(`✅ 已加载 ${Object.keys(regions).length} 个地区到下拉框`);
    }

    // 加载子网列表
    function loadSubnets(accountId, region) {
        $('#subnet_id').html('<option value="">加载中...</option>');

        $.ajax({
            url: '{{ route("aws-ec2.subnets") }}',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                let options = '<option value="">请选择子网</option>';

                // 处理后端返回的对象格式 {subnet-id: "display-name"}
                if (response.success && response.data && typeof response.data === 'object' && Object.keys(response.data).length > 0) {
                    Object.keys(response.data).forEach(function(subnetId) {
                        const displayName = response.data[subnetId];
                        options += `<option value="${subnetId}">${displayName}</option>`;
                    });

                    // 自动选择第一个子网
                    const firstSubnetId = Object.keys(response.data)[0];
                    setTimeout(() => $('#subnet_id').val(firstSubnetId), 100);
                } else {
                    // 如果没有子网，提供默认选项
                    options += '<option value="default">使用默认子网</option>';
                    options += '<option value="" disabled>--- 暂无其他可用子网 ---</option>';
                }
                $('#subnet_id').html(options);
            },
            error: function(xhr, status, error) {
                console.error('加载子网失败:', xhr.responseText);
                
                // 检查是否是代理异常错误
                if (xhr.responseJSON && xhr.responseJSON.error_type === 'proxy_error') {
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', xhr.responseJSON.message);
                    } else {
                        alert(xhr.responseJSON.message);
                    }
                    return;
                }

                let errorMsg = '加载失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }

                // 即使加载失败，也提供默认选项让用户可以继续
                $('#subnet_id').html(`
                    <option value="">请选择子网</option>
                    <option value="default">使用默认子网</option>
                    <option value="" disabled>--- ${errorMsg} ---</option>
                `);

                // 自动选择默认子网
                setTimeout(() => $('#subnet_id').val('default'), 100);
            }
        });
    }

    // 加载安全组列表
    function loadSecurityGroups(accountId, region) {
        $('#security_group_ids').html('<option value="">加载中...</option>');

        $.ajax({
            url: '{{ route("aws-ec2.security-groups") }}',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                let options = '';
                if (Array.isArray(response) && response.length > 0) {
                    response.forEach(function(sg) {
                        const isDefault = sg.GroupName === 'default' ? ' (默认)' : '';
                        options += `<option value="${sg.GroupId}">${sg.GroupName} (${sg.GroupId})${isDefault}</option>`;
                    });
                    // 如果有默认安全组，自动选择
                    const defaultSG = response.find(sg => sg.GroupName === 'default');
                    if (defaultSG) {
                        setTimeout(() => $('#security_group_ids').val(defaultSG.GroupId), 100);
                    }
                } else {
                    options = '<option value="">暂无可用安全组</option>';
                }
                $('#security_group_ids').html(options);
            },
            error: function(xhr, status, error) {
                console.error('加载安全组失败:', xhr.responseText);
                let errorMsg = '加载失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }
                $('#security_group_ids').html(`
                    <option value="default">使用默认安全组</option>
                    <option value="" disabled>--- ${errorMsg} ---</option>
                `);
            }
        });
    }

    // 加载密钥对列表
    function loadKeyPairs(accountId, region) {
        $('#key_name').html('<option value="">加载中...</option>');

        $.ajax({
            url: '{{ route("aws-ec2.key-pairs") }}',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                let options = '<option value="">不使用密钥对</option>';
                if (Array.isArray(response) && response.length > 0) {
                    response.forEach(function(kp) {
                        options += `<option value="${kp.KeyName}">${kp.KeyName}</option>`;
                    });
                } else {
                    options += '<option value="" disabled>暂无可用密钥对</option>';
                }
                $('#key_name').html(options);
            },
            error: function(xhr, status, error) {
                console.error('加载密钥对失败:', xhr.responseText);
                let errorMsg = '加载失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }
                $('#key_name').html(`<option value="">${errorMsg}</option>`);
            }
        });
    }

    // 删除旧的initializeFixedData函数



    // 删除旧的generateRegionSpecificAmis函数




    // 删除旧的loadAMIs函数



    // 渲染AMI列表
    function renderAMIs(amis) {
        let html = '';
        amis.forEach(function(ami) {
            // 兼容不同的AMI数据格式
            const amiId = ami.ImageId || ami.id || ami.ami_id;
            const amiName = ami.Name || ami.name || amiId;
            const amiDescription = ami.Description || ami.description || '无描述';



            // 验证AMI ID格式，如果是占位符则警告
            if (amiId && amiId.includes('placeholder')) {
                console.warn(`⚠️ 检测到占位符AMI ID: ${amiId}，这可能导致验证失败`);
            }

            html += `
                <div class="ami-card" data-ami-id="${amiId}">
                    <div class="ami-name">${amiName}</div>
                    <div class="ami-description">${amiDescription}</div>
                    <div class="ami-id">${amiId}</div>
                </div>
            `;
        });
        $('#amiGrid').html(html);


    }

    // 加载实例类型列表
    function loadInstanceTypes() {
        $.ajax({
            url: '{{ route("aws-ec2.instance-types") }}',
            method: 'GET',
            success: function(response) {
                instanceTypes = response;
                renderInstanceTypes(response);
            },
            error: function(xhr, status, error) {
                console.error('加载实例类型失败:', xhr.responseText);
            }
        });
    }

    // 渲染实例类型列表
    function renderInstanceTypes(types) {
        const currentValue = $('#instance_type').val();
        let html = '';

        types.forEach(function(type) {
            const isSelected = type.name === currentValue;
            const selectedClass = isSelected ? 'selected' : '';

            html += `
                <div class="instance-type-option ${selectedClass}" data-type="${type.name}">
                    <div class="instance-type-name">${type.name}</div>
                    <div class="instance-type-specs">${type.specs}</div>
                </div>
            `;
        });
        $('#instanceTypeDropdown').html(html);
    }

    // 过滤实例类型（搜索时显示匹配结果，无搜索时显示完整列表）
    function filterInstanceTypes(searchTerm) {
        const currentValue = $('#instance_type').val();
        const isSearching = searchTerm.trim() !== '';
        let html = '';

        // 添加或移除搜索状态类
        if (isSearching) {
            $('#instanceTypeDropdown').addClass('searching');
        } else {
            $('#instanceTypeDropdown').removeClass('searching');
        }

        instanceTypes.forEach(function(type) {
            const isMatch = !isSearching ||
                           type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           type.specs.toLowerCase().includes(searchTerm.toLowerCase());
            const isSelected = type.name === currentValue;

            const matchClass = isMatch ? 'match' : '';
            const selectedClass = isSelected ? 'selected' : '';

            html += `
                <div class="instance-type-option ${matchClass} ${selectedClass}" data-type="${type.name}">
                    <div class="instance-type-name">${type.name}</div>
                    <div class="instance-type-specs">${type.specs}</div>
                </div>
            `;
        });

        $('#instanceTypeDropdown').html(html);
        $('#instanceTypeDropdown').addClass('show');
    }

    // 更新步骤指示器
    function updateStepIndicator(step) {
        $('.step').removeClass('active');
        for (let i = 1; i <= step; i++) {
            $(`.step:nth-child(${i * 2 - 1})`).addClass('active');
        }
    }

    // 清除依赖字段
    function clearDependentFields() {
        $('#subnet_id').html('<option value="">请先选择账户和地区</option>');
        $('#security_group_ids').html('<option value="">请先选择账户和地区</option>');
        $('#key_name').html('<option value="">请先选择账户和地区</option>');
        $('#amiSelection').hide();
        $('.os-card').removeClass('selected');
        $('#os_type').val('');
        $('#ami_id').val('');
        updateStepIndicator(1);
        console.log('✅ 已清除依赖字段');
    }

    // 根据AWS官方文档进行表单验证
    function validateForm() {
        const requiredFields = [
            'account_id', 'region', 'os_type', 'ami_id',
            'instance_name', 'instance_type', 'instance_count'
        ];

        for (let field of requiredFields) {
            if (!$(`#${field}`).val() && !$(`[name="${field}"]`).val()) {
                let fieldName = field;
                let scrollTarget = null;

                // 自定义字段名称和滚动目标
                if (field === 'os_type') {
                    fieldName = '请选择操作系统';
                    scrollTarget = $('.os-selection-container, .os-card').first();
                } else {
                    fieldName = `请填写必填字段：${field}`;
                    scrollTarget = $(`#${field}`);
                }

                alert(fieldName);

                // 自动滚动到对应位置
                if (scrollTarget && scrollTarget.length > 0) {
                    $('html, body').animate({
                        scrollTop: scrollTarget.offset().top - 100
                    }, 500);

                    // 如果是输入框，则聚焦
                    if (scrollTarget.is('input, select, textarea')) {
                        setTimeout(() => scrollTarget.focus(), 600);
                    }
                }

                return false;
            }
        }

        // 验证AWS地区格式
        const region = $('#region').val();
        if (region && !/^[a-z0-9-]+$/.test(region)) {
            alert('AWS地区格式不正确');
            return false;
        }

        // 验证AMI ID格式（支持真实AMI ID和通用AMI ID）
        const amiId = $('#ami_id').val();
        if (amiId && !/^(ami-[a-f0-9]{8,17}|ami-generic-.+)$/.test(amiId)) {
            alert('AMI ID格式不正确，应为 ami-xxxxxxxx 或 ami-generic-xxx 格式');
            return false;
        }

        // 验证实例类型格式
        const instanceType = $('#instance_type').val();
        if (instanceType && !/^[a-z0-9]+\.[a-z0-9]+$/.test(instanceType)) {
            alert('实例类型格式不正确，应为 family.size 格式（如 t3.micro）');
            return false;
        }

        // 验证子网ID格式
        const subnetId = $('#subnet_id').val();
        if (subnetId && subnetId !== 'default' && !/^subnet-[a-f0-9]{8,17}$/.test(subnetId)) {
            alert('子网ID格式不正确，应为 subnet-xxxxxxxx 格式');
            return false;
        }

        // 验证安全组
        const securityGroups = $('#security_group_ids').val();
        if (!securityGroups || securityGroups.length === 0) {
            alert('请至少选择一个安全组');
            return false;
        }

        // 验证安全组ID格式
        for (let sgId of securityGroups) {
            if (!/^sg-[a-f0-9]{8,17}$/.test(sgId)) {
                alert(`安全组ID格式不正确：${sgId}，应为 sg-xxxxxxxx 格式`);
                return false;
            }
        }

        // 验证密钥对或密码至少填写一个
        const keyName = $('#key_name').val();
        const password = $('#password').val();

        if (!keyName && !password) {
            alert('请至少选择一个密钥对或设置登录密码');
            return false;
        }

        // 验证密码强度（AWS要求）
        if (password) {
            if (password.length < 8 || password.length > 255) {
                alert('登录密码长度必须在8-255个字符之间');
                return false;
            }
            // 检查密码复杂性
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

            if (!(hasUpper && hasLower && hasNumber)) {
                alert('密码必须包含大写字母、小写字母和数字');
                return false;
            }
        }

        // 验证实例数量
        const instanceCount = parseInt($('#instance_count').val());
        if (instanceCount < 1 || instanceCount > 20) {
            alert('实例数量必须在1-20之间');
            return false;
        }

        // 验证实例名称长度
        const instanceName = $('#instance_name').val();
        if (instanceName && instanceName.length > 255) {
            alert('实例名称不能超过255个字符');
            return false;
        }

        return true;
    }



    // 检查OS卡片状态
    function checkOSCardStatus() {
        const accountId = $('#account_id').val();
        const region = $('#region').val();

        if (!accountId || !region) {
            $('.os-card').hide(); // 隐藏所有系统
        } else {
            // 重新执行地区筛选，显示该地区支持的系统
            if (region) {
                updateSystemsForRegion(region);
            } else {
                $('.os-card').show().removeClass('disabled');
            }
        }
    }

    // 已合并到initializeEventListeners函数中，避免重复监听

    // 密码可见功能
    function initPasswordToggle() {
        $('#passwordToggle').click(function() {
            const passwordInput = $('#password');
            const toggleIcon = $(this).find('i');

            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                toggleIcon.removeClass('bi-eye').addClass('bi-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                toggleIcon.removeClass('bi-eye-slash').addClass('bi-eye');
            }
        });
    }

    // 账户搜索功能
    function initAccountSearch() {
        const $searchInput = $('#account_search');
        const $dropdown = $('#accountDropdown');
        const $hiddenInput = $('#account_id');

        // 点击搜索框或获得焦点时显示下拉框
        $searchInput.on('click focus', function() {
            // 显示所有账户选项
            $('.account-option').show();
            // 高亮当前选中的账户
            updateSelectedHighlight();
            $dropdown.show();
        });

        // 搜索功能
        $searchInput.on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            $dropdown.show(); // 确保下拉框显示

            if (searchTerm === '') {
                // 如果搜索框为空，显示所有账户
                $('.account-option').show();
                // 更新选中状态的高亮
                updateSelectedHighlight();
            } else {
                // 如果有搜索内容，进行过滤
                filterOptions(searchTerm);
                // 更新选中状态的高亮
                updateSelectedHighlight();
            }

            // 清空隐藏字段，因为用户正在搜索
            $hiddenInput.val('');
        });

        // 过滤选项的函数
        function filterOptions(searchTerm) {
            const $options = $('.account-option');
            $options.each(function() {
                const optionText = $(this).text().toLowerCase();
                if (optionText.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        // 选择账户
        $('.account-option').on('click', function() {
            // 检查账户是否被禁用
            if ($(this).hasClass('disabled')) {
                return; // 禁用的账户不能选择
            }

            const value = $(this).data('value');
            const text = $(this).find('span').first().text() || $(this).text();

            $hiddenInput.val(value);
            $searchInput.val(text);
            $dropdown.hide();

            // 更新选中状态的高亮
            updateSelectedHighlight();

            // 触发账户选择事件
            if (value) {
                loadRegions();
            }
        });

        // 点击外部隐藏下拉框
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.account-select-container').length) {
                $dropdown.hide();
            }
        });

        // 键盘导航支持
        $searchInput.on('keydown', function(e) {
            const $visibleOptions = $('.account-option:visible:not(.disabled)');
            const $selected = $('.account-option.highlighted');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if ($selected.length === 0) {
                    $visibleOptions.first().addClass('highlighted');
                } else {
                    $selected.removeClass('highlighted');
                    const next = $selected.nextAll('.account-option:visible:not(.disabled)').first();
                    if (next.length > 0) {
                        next.addClass('highlighted');
                    } else {
                        $visibleOptions.first().addClass('highlighted');
                    }
                }
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if ($selected.length === 0) {
                    $visibleOptions.last().addClass('highlighted');
                } else {
                    $selected.removeClass('highlighted');
                    const prev = $selected.prevAll('.account-option:visible:not(.disabled)').first();
                    if (prev.length > 0) {
                        prev.addClass('highlighted');
                    } else {
                        $visibleOptions.last().addClass('highlighted');
                    }
                }
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if ($selected.length > 0 && !$selected.hasClass('disabled')) {
                    $selected.click();
                }
            } else if (e.key === 'Escape') {
                $dropdown.hide();
                $searchInput.blur();
            }
        });

        // 鼠标悬停高亮
        $('.account-option').on('mouseenter', function() {
            if (!$(this).hasClass('disabled')) {
                $('.account-option').removeClass('highlighted');
                $(this).addClass('highlighted');
            }
        });

        // 更新选中账户的高亮状态
        function updateSelectedHighlight() {
            const selectedValue = $hiddenInput.val();
            $('.account-option').removeClass('selected');
            if (selectedValue) {
                $(`.account-option[data-value="${selectedValue}"]`).addClass('selected');
            }
        }
    }

    // 账户相关数据处理函数





    // 立即验证账户并加载数据（使用默认地区）
    function loadAccountDataImmediately(accountId) {
        // 使用默认地区 us-east-1 立即验证账户并加载数据
        const defaultRegion = 'us-east-1';
        console.log(`🔄 立即验证账户 ${accountId} 并加载在默认地区 ${defaultRegion} 的数据...`);

        // 分别调用各个方法，避免回调函数作用域问题
        awsDataManager.getSubnets(accountId, defaultRegion, function(subnets) {
            if (subnets !== null) {
                updateSubnetOptions(subnets);
                console.log(`✅ 账户 ${accountId} 验证成功，子网数据已更新`);
            } else {
                console.log(`❌ 账户 ${accountId} 验证失败`);
            }
        });

        awsDataManager.getSecurityGroups(accountId, defaultRegion, function(securityGroups) {
            if (securityGroups !== null) {
                updateSecurityGroupOptions(securityGroups);
                console.log(`✅ 账户 ${accountId} 安全组数据已更新`);
            }
        });

        awsDataManager.getKeyPairs(accountId, defaultRegion, function(keyPairs) {
            if (keyPairs !== null) {
                updateKeyPairOptions(keyPairs);
                console.log(`✅ 账户 ${accountId} 密钥对数据已更新`);
            }
        });
    }




});

// 初始化开机脚本功能
function initUserDataFeature() {
    // 启用/禁用开机脚本
    $('#enable_user_data').change(function() {
        if ($(this).is(':checked')) {
            $('#userDataSection').slideDown();
            loadUserDataTemplates();
        } else {
            $('#userDataSection').slideUp();
            $('#user_data').val('');
            updateScriptSize();
        }
    });

    // 模板选择
    $('#user_data_template').change(function() {
        const templateId = $(this).val();
        if (templateId) {
            const selectedOption = $(this).find('option:selected');
            const scriptContent = selectedOption.data('script');
            if (scriptContent) {
                $('#user_data').val(scriptContent);
                updateScriptSize();
            }
        }
    });

    // 点击下拉框时触发模板加载
    $('#user_data_template').on('focus click', function() {
        if (!templatesLoaded && !templatesLoading) {
            loadUserDataTemplates();
        }
    });

    // 脚本内容变化时更新大小显示
    $('#user_data').on('input', function() {
        updateScriptSize();
    });

    // 更新脚本大小显示
    function updateScriptSize() {
        const content = $('#user_data').val();
        const size = new Blob([content]).size;
        const maxSize = 16384; // 16KB
        const percentage = (size / maxSize) * 100;

        $('#script_size').text(size);
        $('#script_progress').css('width', Math.min(percentage, 100) + '%');

        // 根据大小改变进度条颜色
        const progressBar = $('#script_progress');
        progressBar.removeClass('bg-success bg-warning bg-danger');
        if (percentage < 70) {
            progressBar.addClass('bg-success');
        } else if (percentage < 90) {
            progressBar.addClass('bg-warning');
        } else {
            progressBar.addClass('bg-danger');
        }

        // 如果超过限制，显示警告
        if (size > maxSize) {
            if (!$('#size_warning').length) {
                $('#user_data').after('<div id="size_warning" class="text-danger mt-1"><small>脚本大小超过16KB限制，请减少内容</small></div>');
            }
        } else {
            $('#size_warning').remove();
        }
    }

    // 模板加载状态管理
    let templatesLoaded = false;
    let templatesLoading = false;

    // 加载开机脚本模板
    function loadUserDataTemplates() {
        // 如果已经加载过或正在加载，直接返回
        if (templatesLoaded || templatesLoading) {
            return;
        }

        templatesLoading = true;
        const templateSelect = $('#user_data_template');

        // 设置加载状态
        templateSelect.empty().append('<option value="">请选择模板...</option>');
        templateSelect.append('<option disabled>模板加载中......</option>');

        // 获取当前选择的操作系统类型
        const selectedOs = $('#selected_os').val();
        let osType = 'all';

        if (selectedOs) {
            if (selectedOs.toLowerCase().includes('windows')) {
                osType = 'windows';
            } else {
                osType = 'linux';
            }
        }

        $.get('{{ route("aws-ec2.user-data-templates") }}', { os_type: osType })
            .done(function(response) {
                if (response.success && response.data) {
                    // 清空并重新填充
                    templateSelect.empty().append('<option value="">请选择模板...</option>');

                    // 按分类分组
                    const categories = {};
                    response.data.forEach(template => {
                        if (!categories[template.category]) {
                            categories[template.category] = [];
                        }
                        categories[template.category].push(template);
                    });

                    // 添加分组选项
                    Object.keys(categories).forEach(category => {
                        const optgroup = $('<optgroup></optgroup>');
                        optgroup.attr('label', getCategoryName(category));

                        categories[category].forEach(template => {
                            const option = $('<option></option>');
                            option.attr('value', template.id);
                            option.attr('data-script', template.script_content);
                            option.attr('title', template.description);
                            option.text(template.name);
                            optgroup.append(option);
                        });
                        templateSelect.append(optgroup);
                    });

                    templatesLoaded = true;
                }
            })
            .fail(function() {
                console.error('加载开机脚本模板失败');
                // 失败时显示错误信息
                templateSelect.empty().append('<option value="">请选择模板...</option>');
                templateSelect.append('<option disabled>模板加载失败，请重试</option>');
            })
            .always(function() {
                templatesLoading = false;
            });
    }

    // 获取分类中文名称
    function getCategoryName(category) {
        const categoryNames = {
            'development': '开发环境',
            'web': 'Web服务',
            'security': '安全配置',
            'basic': '基础配置',
            'custom': '自定义'
        };
        return categoryNames[category] || category;
    }
}

</script>
@endpush