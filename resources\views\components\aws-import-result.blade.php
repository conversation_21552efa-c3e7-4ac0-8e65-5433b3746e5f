@props(['totalLines', 'successCount', 'failedCount', 'failedAccounts'])

<div class="import-result-container">
    <div class="row g-3">
        <div class="col-md-4">
            <div class="result-card total">
                <div class="result-icon">
                    <i class="bi bi-list-ul"></i>
                </div>
                <div class="result-content">
                    <div class="result-label">总行数</div>
                    <div class="result-value">{{ $totalLines }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="result-card success">
                <div class="result-icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="result-content">
                    <div class="result-label">添加成功</div>
                    <div class="result-value">{{ $successCount }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="result-card failed">
                <div class="result-icon">
                    <i class="bi bi-x-circle"></i>
                </div>
                <div class="result-content">
                    <div class="result-label">添加失败</div>
                    <div class="result-value">{{ $failedCount }}</div>
                </div>
            </div>
        </div>
    </div>

    @if($failedCount > 0 && !empty($failedAccounts))
        <div class="failed-accounts mt-4">
            <h6 class="text-danger mb-3">
                <i class="bi bi-exclamation-triangle me-1"></i>
                以下账户添加失败：
            </h6>
            <div class="failed-list">
                @foreach($failedAccounts as $account)
                    <div class="failed-item">
                        <i class="bi bi-x-circle me-2"></i>
                        {{ $account['email'] }}
                        <small class="text-muted ms-2">{{ $account['reason'] }}</small>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>

<style>
.import-result-container {
    padding: 1.5rem;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.result-card {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: #f8f9fa;
    transition: transform 0.2s;
}

.result-card:hover {
    transform: translateY(-2px);
}

.result-card.total {
    background: rgba(90, 102, 241, 0.1);
}

.result-card.total .result-icon {
    color: #5a66f1;
}

.result-card.success {
    background: rgba(10, 179, 156, 0.1);
}

.result-card.success .result-icon {
    color: #0ab39c;
}

.result-card.failed {
    background: rgba(240, 101, 72, 0.1);
}

.result-card.failed .result-icon {
    color: #f06548;
}

.result-icon {
    font-size: 2rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
}

.result-content {
    flex: 1;
}

.result-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.result-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
}

.failed-accounts {
    background: rgba(240, 101, 72, 0.05);
    border-radius: 0.5rem;
    padding: 1.25rem;
}

.failed-list {
    max-height: 200px;
    overflow-y: auto;
}

.failed-item {
    padding: 0.75rem;
    background: #fff;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #495057;
    border: 1px solid rgba(240, 101, 72, 0.2);
}

.failed-item:last-child {
    margin-bottom: 0;
}

.failed-item i {
    color: #f06548;
}
</style> 