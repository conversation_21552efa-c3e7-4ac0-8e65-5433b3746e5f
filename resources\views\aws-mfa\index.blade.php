@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: none;
}

.btn-soft-primary:hover {
    background-color: #4361ee;
    color: #fff;
}

/* 重置按钮样式 */
.btn-reset {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-reset i {
    font-size: 0.875rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

.table > :not(caption) > * > * {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
    text-align: center;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 所有列居中对齐 */
.table th, .table td {
    text-align: center;
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    font-size: 0.9rem;
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    font-size: 0.9rem;
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    font-size: 0.9rem;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 添加截断文本的样式 */
.truncate-text {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
}

.email-truncate {
    max-width: 250px;
}

.password-truncate {
    max-width: 100px;
}

.key-truncate {
    max-width: 120px;
}

.secret-truncate {
    max-width: 180px;
}

.mfa-truncate {
    max-width: 150px;
}
</style>
@endpush

@section('content')
<div class="container-fluid fade-in">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">AWS MFA登录</h4>
                            <p class="text-muted mb-0 mt-1">AWS登录MFA认证码获取</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <!-- 移除一键启用按钮 -->
                    </div>
                </div>

                <div class="card-body">
                    <!-- 使用筛选组件 -->
                    <x-filter-panel :status-options="App\Models\AwsAccount::getStatusList()" />

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </div>
                                    </th>
                                    <th>账户邮箱</th>
                                    <!--<th>邮箱密码</th>-->
                                    <th>AWS密码</th>
                                    <th>访问密钥</th>
                                    <th>秘密访问密钥</th>
                                    <th>MFA信息</th>
                                    <th>状态</th>
                                    <th>验证码</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($accounts as $account)
                                <tr data-account-id="{{ $account->id }}">
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input account-select" value="{{ $account->id }}">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="truncate-text email-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->account_name }}">
                                            {{ $account->account_name }}
                                        </span>
                                    </td>
                                    <!--td>
                                        <span class="truncate-text password-truncate" title="{{ $account->email_password }}">
                                            {{ $account->email_password }}
                                        </span>
                                    </td>-->
                                    <td>
                                        <span class="truncate-text password-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->aws_password }}">
                                            {{ $account->aws_password }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text key-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->access_key }}">
                                            {{ $account->access_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text secret-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->secret_key }}">
                                            {{ $account->secret_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text key-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->aws_mfa_key }}">
                                            {{ $account->aws_mfa_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $account->status === 1 ? 'badge-soft-success' : ($account->status === 2 ? 'badge-soft-danger' : 'badge-soft-warning') }}">
                                            {{ $account->status_text }}
                                        </span>
                                    </td>
                                    <td class="mfa-code-cell text-center"></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary get-code-btn" data-account-id="{{ $account->id }}">获取验证码</button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="mt-4 d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                总共 {{ $accounts->total() }} 条记录
                            </div>
                            <div>
                                {{ $accounts->withQueryString()->links('components.pagination') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    function showCopyToast(text) {
        let toast = document.createElement('div');
        toast.textContent = text;
        toast.style.position = 'fixed';
        toast.style.zIndex = 999999;
        toast.style.background = '#222';
        toast.style.color = '#fff';
        toast.style.padding = '10px 30px';
        toast.style.borderRadius = '8px';
        toast.style.fontSize = '20px';
        toast.style.left = '50vw';
        toast.style.top = '30vh';
        toast.style.opacity = 1;
        toast.style.pointerEvents = 'none';
        document.body.appendChild(toast);
        setTimeout(() => { toast.style.opacity = 0; }, 1800);
        setTimeout(() => { toast.remove(); }, 2200);
    }
    document.querySelector('tbody').addEventListener('click', function(e) {
        if (e.target.classList.contains('copy-mfa-code') || e.target.classList.contains('copy-cell')) {
            const code = e.target.textContent.trim();
            navigator.clipboard.writeText(code).then(function() {
                showCopyToast('复制成功');
            });
        }
    });
    document.querySelectorAll('.get-code-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const accountId = this.dataset.accountId;
            const row = this.closest('tr');
            const codeCell = row.querySelector('.mfa-code-cell');
            codeCell.textContent = '...';
            fetch('/user/aws-mfa/get-code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ account_id: accountId })
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    codeCell.innerHTML = `<span class="copy-mfa-code" style="cursor:pointer;user-select:all;">${data.code}</span>`;
                } else {
                    codeCell.textContent = '获取失败';
                    alert(data.message || '获取验证码失败');
                }
            })
            .catch(() => {
                codeCell.textContent = '获取失败';
                alert('请求失败');
            });
        });
    });
});
</script>
@endpush 