<?php

namespace App\Http\Controllers;

use App\Models\AwsAccount;
use App\Models\UserDataTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Aws\Ec2\Ec2Client;
use Aws\Exception\AwsException;

class AwsEc2Controller extends Controller
{
    /**
     * 创建EC2客户端（统一SSL配置和代理配置）
     */
    private function createEc2Client($region, $accessKey, $secretKey)
    {
        $user = auth()->user();

        $sdkConfig = [
            'version' => 'latest',
            'region' => $region,
            'credentials' => [
                'key' => $accessKey,
                'secret' => $secretKey,
            ],
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10
            ]
        ];

        // 根据配置决定是否禁用SSL验证
        if (config('aws.disable_ssl_verification', false)) {
            $sdkConfig['http']['verify'] = false;
        }

        // 应用用户代理配置
        $this->applyProxyConfig($sdkConfig, $user);

        return new Ec2Client($sdkConfig);
    }

    /**
     * 应用用户代理配置到AWS SDK配置
     */
    private function applyProxyConfig(&$config, $user = null)
    {
        if (!$user) {
            \Log::info("EC2 AWS SDK请求模式: 本地模式 (无用户认证)");
            return;
        }

        // 如果用户选择了代理模式，必须检查代理状态
        if ($user->proxy_mode === 'proxy') {
            // 检查代理状态是否异常
            if ($user->proxy_status !== 'active') {
                \Log::error("EC2代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'proxy_status' => $user->proxy_status,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('当前代理模式IP异常，请切换其他模式进行操作');
            }

            // 检查代理配置是否完整
            if (!$user->proxy_host || !$user->proxy_port) {
                \Log::error("EC2代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('代理配置不完整，请重新配置代理或切换到本地模式');
            }

            // 构建代理URL
            $proxyUrl = $this->buildProxyUrl(
                $user->proxy_type,
                $user->proxy_host,
                $user->proxy_port,
                $user->proxy_username,
                $user->proxy_password
            );

            // 应用代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("EC2 AWS SDK请求模式: 代理模式", [
                'user_id' => $user->id,
                'proxy_type' => $user->proxy_type,
                'proxy_host' => $user->proxy_host,
                'proxy_port' => $user->proxy_port,
                'proxy_username' => $user->proxy_username,
                'proxy_status' => $user->proxy_status,
                'actual_proxy_ip' => $user->proxy_host, // 实际使用的代理IP
                'proxy_url_format' => $user->proxy_type . '://' . $user->proxy_host . ':' . $user->proxy_port
            ]);
        } elseif ($user->proxy_mode === 'free_proxy') {
            // 免费代理模式
            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');

            if ($freeProxyStatus !== 'active') {
                \Log::error("EC2免费代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'free_proxy_status' => $freeProxyStatus
                ]);

                throw new \Exception('当前免费代理模式IP异常，请切换其他模式进行操作');
            }

            // 获取免费代理配置
            $freeProxyType = \App\Models\Setting::get('free_proxy_type', 'http');
            $freeProxyHost = \App\Models\Setting::get('free_proxy_host', '');
            $freeProxyPort = \App\Models\Setting::get('free_proxy_port', '');
            $freeProxyUsername = \App\Models\Setting::get('free_proxy_username', '');
            $freeProxyPassword = \App\Models\Setting::get('free_proxy_password', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                \Log::error("EC2免费代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id
                    // 敏感信息已移除：不记录具体的配置信息
                ]);

                throw new \Exception('免费代理配置不完整，请联系管理员');
            }

            // 构建免费代理URL
            $proxyUrl = $this->buildProxyUrl(
                $freeProxyType,
                $freeProxyHost,
                $freeProxyPort,
                $freeProxyUsername,
                $freeProxyPassword
            );

            // 应用免费代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("EC2 AWS SDK请求模式: 免费代理模式", [
                'user_id' => $user->id,
                'free_proxy_status' => $freeProxyStatus
                // 敏感信息已移除：不记录IP、端口、用户名等
            ]);
        } else {
            \Log::info("EC2 AWS SDK请求模式: 本地模式", [
                'user_id' => $user->id,
                'proxy_mode' => $user->proxy_mode,
                'proxy_status' => $user->proxy_status ?? 'inactive'
            ]);
        }
    }

    /**
     * 构建代理URL
     */
    private function buildProxyUrl($type, $host, $port, $username = null, $password = null)
    {
        $proxyUrl = $type . '://';

        if ($username && $password) {
            $proxyUrl .= urlencode($username) . ':' . urlencode($password) . '@';
        }

        $proxyUrl .= $host . ':' . $port;

        return $proxyUrl;
    }

    /**
     * 检查错误是否为代理相关错误
     */
    private function isProxyRelatedError($errorMessage)
    {
        $proxyErrorIndicators = [
            '407', 'Proxy Authentication Required',
            '502', 'Bad Gateway',
            '503', 'Service Unavailable',
            '504', 'Gateway Timeout',
            'CONNECT tunnel failed',
            'Proxy CONNECT aborted',
            'cURL error 7', 'cURL error 28', 'cURL error 35',
            'cURL error 52', 'cURL error 56',
            'Empty reply from server',
            'Connection refused',
            'Operation timed out'
        ];

        foreach ($proxyErrorIndicators as $indicator) {
            if (strpos($errorMessage, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }
    public function index()
    {
        $user = Auth::user();
        // 显示所有账户，前端根据状态控制可选性
        $accounts = AwsAccount::where('user_id', $user->id)
            ->orderBy('status', 'asc') // 可用账户排在前面
            ->orderBy('created_at', 'desc')
            ->get();

        return view('aws-ec2.index', compact('accounts'));
    }

    public function create()
    {
        $user = Auth::user();
        // 显示所有账户，按添加时间排序，最新添加的在前面
        $accounts = AwsAccount::where('user_id', $user->id)
            ->orderBy('created_at', 'desc') // 最新添加的账户排在前面
            ->get();

        return view('aws-ec2.create', compact('accounts'));
    }

    public function batchCreate()
    {
        $user = Auth::user();
        // 显示所有账户，前端根据状态控制可选性
        $accounts = AwsAccount::where('user_id', $user->id)
            ->orderBy('status', 'asc') // 可用账户排在前面
            ->orderBy('created_at', 'desc')
            ->get();

        return view('aws-ec2.batch-create', compact('accounts'));
    }

    public function batchStore(Request $request)
    {
        $request->validate([
            'batch_account_ids' => 'required|string',
            'region' => 'required|string',
            'selected_os' => 'required|string',
            'ami_id' => 'nullable|string',
            'instance_name' => 'required|string',
            'instance_type' => 'required|string',
            'instance_count' => 'required|integer|min:1|max:20',
            'volume_size' => 'required|integer|min:8|max:16384',
            'volume_type' => 'required|string',
            'subnet_id' => 'nullable|string',
            'security_group_ids' => 'nullable|array',
            'key_name' => 'nullable|string',
            'password' => 'nullable|string',
        ]);

        // 自定义验证子网ID
        if ($request->subnet_id && $request->subnet_id !== 'default' && !preg_match('/^subnet-[a-f0-9]{8,17}$/', $request->subnet_id)) {
            return response()->json([
                'success' => false,
                'message' => '子网ID格式无效'
            ], 422);
        }

        // 自定义验证安全组ID
        if ($request->security_group_ids && !empty($request->security_group_ids)) {
            foreach ($request->security_group_ids as $sgId) {
                if ($sgId !== 'default' && !preg_match('/^sg-[a-f0-9]{8,17}$/', $sgId)) {
                    return response()->json([
                        'success' => false,
                        'message' => '安全组ID格式无效'
                    ], 422);
                }
            }
        }

        $user = Auth::user();
        $accountIds = explode(',', $request->batch_account_ids);
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($accountIds as $accountId) {
            $account = AwsAccount::where('id', $accountId)
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                $results[] = [
                    'success' => false,
                    'account_id' => $accountId,
                    'account_name' => "账户ID: $accountId",
                    'message' => '账户不存在或无权限'
                ];
                $failureCount++;
                continue;
            }

            try {
                // 为批量创建克隆请求，并为每个账户使用默认子网
                $accountRequest = clone $request;

                // 对于批量创建，如果指定了子网ID，先检查是否在当前账户的区域有效
                if ($request->subnet_id && $request->subnet_id !== 'default') {
                    try {
                        $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);
                        // 尝试验证子网是否存在
                        $ec2Client->describeSubnets(['SubnetIds' => [$request->subnet_id]]);
                        // 如果验证成功，使用指定的子网
                    } catch (\Exception $e) {
                        // 如果子网不存在或无效，使用默认子网
                        Log::warning("账户 {$account->account_name} 的子网 {$request->subnet_id} 无效，使用默认子网", [
                            'account_id' => $accountId,
                            'error' => $e->getMessage()
                        ]);
                        $accountRequest->merge(['subnet_id' => null]);
                    }
                } else {
                    // 如果没有指定子网或指定为default，使用默认子网
                    $accountRequest->merge(['subnet_id' => null]);
                }

                // 对于批量创建，验证安全组是否在当前账户中有效
                if ($request->security_group_ids && !empty($request->security_group_ids)) {
                    try {
                        if (!isset($ec2Client)) {
                            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);
                        }
                        // 尝试验证安全组是否存在
                        $ec2Client->describeSecurityGroups(['GroupIds' => $request->security_group_ids]);
                        // 如果验证成功，使用指定的安全组
                    } catch (\Exception $e) {
                        // 如果安全组不存在或无效，使用默认安全组
                        Log::warning("账户 {$account->account_name} 的安全组无效，使用默认安全组", [
                            'account_id' => $accountId,
                            'security_groups' => $request->security_group_ids,
                            'error' => $e->getMessage()
                        ]);
                        $accountRequest->merge(['security_group_ids' => null]);
                    }
                } else {
                    // 如果没有指定安全组，使用默认安全组
                    $accountRequest->merge(['security_group_ids' => null]);
                }

                // 创建EC2实例
                $instanceResult = $this->createSingleInstance($account, $accountRequest);

                if ($instanceResult['success']) {
                    $results[] = [
                        'success' => true,
                        'account_id' => $accountId,
                        'account_name' => $account->account_name,
                        'instance_ids' => $instanceResult['instance_ids'] ?? [],
                        'message' => '创建成功'
                    ];
                    $successCount++;
                } else {
                    $results[] = [
                        'success' => false,
                        'account_id' => $accountId,
                        'account_name' => $account->account_name,
                        'message' => $instanceResult['error'] ?? '创建失败'
                    ];
                    $failureCount++;
                }
            } catch (\Exception $e) {
                $results[] = [
                    'success' => false,
                    'account_id' => $accountId,
                    'account_name' => $account->account_name,
                    'message' => $e->getMessage()
                ];
                $failureCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => '批量创建完成',
            'results' => $results,
            'summary' => [
                'total' => count($accountIds),
                'success' => $successCount,
                'failure' => $failureCount
            ]
        ]);
    }

    // 注意：store方法已被移除，请使用createInstance方法



    public function getInstances(Request $request)
    {
        $request->validate([
            'account_ids' => 'required|array',
            'account_ids.*' => 'exists:aws_accounts,id',
            'region' => 'required|string'
        ]);

        $user = Auth::user();
        $accountIds = $request->account_ids;
        $region = $request->region;
        $allInstances = [];

        foreach ($accountIds as $accountId) {
            $account = AwsAccount::where('id', $accountId)
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                continue;
            }

            try {
                $ec2Client = $this->createEc2Client($region, $account->access_key, $account->secret_key);

                $result = $ec2Client->describeInstances();
                
                foreach ($result['Reservations'] as $reservation) {
                    foreach ($reservation['Instances'] as $instance) {
                        $instanceData = [
                            'account_id' => $account->id,
                            'account_name' => $account->account_name,
                            'instance_id' => $instance['InstanceId'],
                            'instance_type' => $instance['InstanceType'],
                            'state' => $instance['State']['Name'],
                            'public_ip' => $instance['PublicIpAddress'] ?? 'N/A',
                            'private_ip' => $instance['PrivateIpAddress'] ?? 'N/A',
                            'launch_time' => $instance['LaunchTime']->format('Y-m-d H:i:s'),
                            'availability_zone' => $instance['Placement']['AvailabilityZone'],
                            'security_groups' => collect($instance['SecurityGroups'])->pluck('GroupName')->implode(', '),
                            'tags' => $this->formatTags($instance['Tags'] ?? []),
                            'region' => $region
                        ];
                        $allInstances[] = $instanceData;
                    }
                }
            } catch (AwsException $e) {
                // 记录错误但继续处理其他账户
                continue;
            } catch (\Exception $e) {
                \Log::error('General Instances Error: ' . $e->getMessage(), [
                    'account_id' => $accountId,
                    'region' => $region
                ]);
                // 检查是否是代理异常错误
                if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                       strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                    return response()->json([
                        'success' => false,
                        'error_type' => 'proxy_error',
                        'message' => $e->getMessage()
                    ], 400);
                }
                // 记录错误但继续处理其他账户
                continue;
            }
        }

        return response()->json([
            'success' => true,
            'instances' => $allInstances
        ]);
    }

    public function createInstance(Request $request)
    {
        // 根据AWS官方文档验证参数
        $request->validate([
            'account_id' => 'required|exists:aws_accounts,id',
            'region' => 'required|string|regex:/^[a-z0-9-]+$/',
            'ami_id' => 'required|string',
            'instance_type' => 'required|string|regex:/^[a-z0-9]+\.[a-z0-9]+$/',
            'key_name' => 'nullable|string|max:255',
            'security_group_ids' => 'nullable|array',
            'security_group_ids.*' => 'string',
            'subnet_id' => 'nullable|string',
            'instance_count' => 'required|integer|min:1|max:20',
            'instance_name' => 'required|string|max:255',
            'os_type' => 'nullable|string',
            'password' => 'nullable|string|min:8|max:255',
            'user_data' => 'nullable|string|max:16384', // AWS限制16KB
            'associate_public_ip' => 'nullable|boolean',
            'ebs_optimized' => 'nullable|boolean',
            'monitoring_enabled' => 'nullable|boolean',
            'disable_api_termination' => 'nullable|boolean'
        ]);

        // 自定义验证子网ID
        if ($request->subnet_id && $request->subnet_id !== 'default' && !preg_match('/^subnet-[a-f0-9]{8,17}$/', $request->subnet_id)) {
            return response()->json([
                'success' => false,
                'message' => '子网ID格式无效'
            ], 422);
        }

        // 自定义验证AMI ID
        if ($request->ami_id && !preg_match('/^(ami-[a-f0-9]{8,17}|ami-generic-.+)$/', $request->ami_id)) {
            return response()->json([
                'success' => false,
                'message' => 'AMI ID格式无效'
            ], 422);
        }

        // 自定义验证安全组ID
        if ($request->security_group_ids && !empty($request->security_group_ids)) {
            foreach ($request->security_group_ids as $sgId) {
                if ($sgId !== 'default' && !preg_match('/^sg-[a-f0-9]{8,17}$/', $sgId)) {
                    return response()->json([
                        'success' => false,
                        'message' => '安全组ID格式无效'
                    ], 422);
                }
            }
        }

        $user = Auth::user();
        $account = AwsAccount::where('id', $request->account_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$account) {
            return response()->json(['success' => false, 'message' => '账户不存在']);
        }

        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            // 直接使用前端传递的AMI ID（前端负责AMI映射）
            $amiId = $request->ami_id;

            if (!$amiId) {
                return response()->json([
                    'success' => false,
                    'message' => "创建失败: 缺少AMI ID",
                    'error_code' => 'MISSING_AMI_ID',
                    'error_type' => 'client'
                ], 400);
            }

            Log::info("✅ 使用前端传递的AMI ID: 地区={$request->region}, AMI={$amiId}");

            // 根据AWS官方文档构建RunInstances参数
            $runInstancesParams = [
                'ImageId' => $amiId,
                'InstanceType' => $request->instance_type,
                'MinCount' => $request->instance_count,
                'MaxCount' => $request->instance_count,
            ];

            // KeyName - 密钥对名称
            if ($request->key_name) {
                $runInstancesParams['KeyName'] = $request->key_name;
            }

            // EbsOptimized - EBS优化
            if ($request->boolean('ebs_optimized', false)) {
                $runInstancesParams['EbsOptimized'] = true;
            }

            // Monitoring - 详细监控
            if ($request->boolean('monitoring_enabled', false)) {
                $runInstancesParams['Monitoring'] = ['Enabled' => true];
            }

            // DisableApiTermination - 终止保护
            if ($request->boolean('disable_api_termination', false)) {
                $runInstancesParams['DisableApiTermination'] = true;
            }

            // UserData - 用户数据脚本
            if ($request->password) {
                $osType = $request->os_type ?? 'Linux';
                if (stripos($osType, 'Windows') !== false) {
                    // Windows实例密码设置
                    $userData = base64_encode("<powershell>\n# 设置管理员密码\nnet user Administrator \"{$request->password}\"\n# 启用管理员账户\nnet user Administrator /active:yes\n# 启用远程桌面\nSet-ItemProperty -Path 'HKLM:\\System\\CurrentControlSet\\Control\\Terminal Server' -name 'fDenyTSConnections' -Value 0\nEnable-NetFirewallRule -DisplayGroup 'Remote Desktop'\n</powershell>");
                } else {
                    // Linux实例密码设置
                    $userData = base64_encode("#!/bin/bash\n# 设置root密码\necho 'root:{$request->password}' | chpasswd\n# 启用SSH密码登录\nsed -i 's/PasswordAuthentication no/PasswordAuthentication yes/g' /etc/ssh/sshd_config\nsed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/g' /etc/ssh/sshd_config\n# 允许root SSH登录\nsed -i 's/#PermitRootLogin yes/PermitRootLogin yes/g' /etc/ssh/sshd_config\nsed -i 's/PermitRootLogin no/PermitRootLogin yes/g' /etc/ssh/sshd_config\n# 重启SSH服务\nsystemctl restart sshd || service ssh restart");
                }
                $runInstancesParams['UserData'] = $userData;
            } elseif ($request->user_data) {
                $runInstancesParams['UserData'] = base64_encode($request->user_data);
            }

            // TagSpecifications - 标签规范
            $runInstancesParams['TagSpecifications'] = [
                [
                    'ResourceType' => 'instance',
                    'Tags' => [
                        [
                            'Key' => 'Name',
                            'Value' => $request->instance_name
                        ]
                    ]
                ]
            ];

            // 执行RunInstances API调用
            Log::info("🚀 执行RunInstances API调用", ['params' => $runInstancesParams]);
            $result = $ec2Client->runInstances($runInstancesParams);
            
            $instanceIds = collect($result['Instances'])->pluck('InstanceId')->toArray();

            // 更新账户的EC2开通状态为已开通
            $account->update(['ec2_status' => AwsAccount::EC2_STATUS_ENABLED]);

            return response()->json([
                'success' => true,
                'message' => '实例创建成功',
                'instance_ids' => $instanceIds
            ]);

        } catch (AwsException $e) {
            Log::error("❌ EC2实例创建失败", [
                'error_message' => $e->getAwsErrorMessage(),
                'error_code' => $e->getAwsErrorCode(),
                'error_type' => $e->getAwsErrorType(),
                'request_params' => $runInstancesParams
            ]);

            return response()->json([
                'success' => false,
                'message' => '创建失败: ' . $e->getAwsErrorMessage(),
                'error_code' => $e->getAwsErrorCode(),
                'error_type' => $e->getAwsErrorType()
            ], 500);
        } catch (\Exception $e) {
            Log::error("❌ EC2实例创建异常", [
                'error_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json([
                'success' => false,
                'message' => '创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 注意：AMI映射逻辑已移至前端处理
     * 后端直接使用前端传递的AMI ID，减少代码复杂度
     * 前端负责根据区域和系统选择正确的官方AMI ID
     */

    /**
     * 使用传统方法根据操作系统类型查找最新AMI
     */
    private function findLatestAmiByOsType($ec2Client, $osType)
    {
        try {
            $filters = [];
            $owners = [];

            switch ($osType) {
                case 'Ubuntu':
                    $owners = ['099720109477']; // Canonical
                    $filters = [
                        ['Name' => 'name', 'Values' => ['ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                        ['Name' => 'virtualization-type', 'Values' => ['hvm']],
                        ['Name' => 'root-device-type', 'Values' => ['ebs']]
                    ];
                    break;
                case 'Amazon Linux':
                case 'Amazon Linux 2':
                    $owners = ['amazon'];
                    $filters = [
                        ['Name' => 'name', 'Values' => ['amzn2-ami-hvm-*-x86_64-gp2']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                        ['Name' => 'virtualization-type', 'Values' => ['hvm']],
                        ['Name' => 'root-device-type', 'Values' => ['ebs']]
                    ];
                    break;
                case 'CentOS':
                    $owners = ['125523088429']; // CentOS Official
                    $filters = [
                        ['Name' => 'name', 'Values' => ['CentOS*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                        ['Name' => 'virtualization-type', 'Values' => ['hvm']]
                    ];
                    break;
                case 'Red Hat':
                case 'Red Hat Enterprise Linux':
                    $owners = ['309956199498']; // Red Hat
                    $filters = [
                        ['Name' => 'name', 'Values' => ['RHEL-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                        ['Name' => 'virtualization-type', 'Values' => ['hvm']]
                    ];
                    break;
                case 'Windows':
                case 'Windows Server':
                case 'Windows Server 2019':
                case 'Windows Server 2022':
                    $owners = ['amazon'];
                    $filters = [
                        ['Name' => 'name', 'Values' => ['Windows_Server-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                        ['Name' => 'virtualization-type', 'Values' => ['hvm']],
                        ['Name' => 'platform', 'Values' => ['windows']]
                    ];
                    break;
                default:
                    return null;
            }

            $images = $ec2Client->describeImages([
                'Owners' => $owners,
                'Filters' => $filters
            ]);

            if (!empty($images['Images'])) {
                // 按创建日期排序，选择最新的
                usort($images['Images'], function($a, $b) {
                    return strtotime($b['CreationDate']) - strtotime($a['CreationDate']);
                });

                return $images['Images'][0]['ImageId'];
            }

            return null;
        } catch (AwsException $e) {
            Log::error("查找AMI失败: " . $e->getMessage());
            return null;
        }
    }

    public function startInstances(Request $request)
    {
        return $this->manageInstanceState($request, 'start');
    }

    public function stopInstances(Request $request)
    {
        return $this->manageInstanceState($request, 'stop');
    }

    public function rebootInstances(Request $request)
    {
        return $this->manageInstanceState($request, 'reboot');
    }

    public function terminateInstances(Request $request)
    {
        return $this->manageInstanceState($request, 'terminate');
    }

    private function manageInstanceState(Request $request, string $action)
    {
        $request->validate([
            'instances' => 'required|array',
            'instances.*.account_id' => 'required|exists:aws_accounts,id',
            'instances.*.instance_id' => 'required|string',
            'instances.*.region' => 'required|string'
        ]);

        $user = Auth::user();
        $results = [];

        foreach ($request->instances as $instanceData) {
            $account = AwsAccount::where('id', $instanceData['account_id'])
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => '账户不存在'
                ];
                continue;
            }

            try {
                $ec2Client = $this->createEc2Client($instanceData['region'], $account->access_key, $account->secret_key);

                $params = ['InstanceIds' => [$instanceData['instance_id']]];

                switch ($action) {
                    case 'start':
                        $ec2Client->startInstances($params);
                        break;
                    case 'stop':
                        $ec2Client->stopInstances($params);
                        break;
                    case 'reboot':
                        $ec2Client->rebootInstances($params);
                        break;
                    case 'terminate':
                        $ec2Client->terminateInstances($params);
                        break;
                }

                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => true,
                    'message' => '操作成功'
                ];

            } catch (AwsException $e) {
                $errorMessage = $e->getAwsErrorMessage();

                // 特殊处理账户阻止错误
                if (strpos($errorMessage, 'account is currently blocked') !== false) {
                    $errorMessage = 'AWS账户被阻止，请联系AWS支持进行账户验证。详情：' . $errorMessage;
                }

                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => $errorMessage
                ];
            }
        }

        $successCount = collect($results)->where('success', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount > 0,
            'message' => "操作完成: {$successCount}/{$totalCount} 个实例成功",
            'results' => $results
        ]);
    }

    private function formatTags(array $tags): string
    {
        return collect($tags)->map(function ($tag) {
            return $tag['Key'] . ': ' . $tag['Value'];
        })->implode(', ');
    }

    public function getRegions()
    {
        // 返回完整的AWS区域列表（与官方一致）
        $regions = [
            ['code' => 'us-east-1', 'name' => '美国东部 (弗吉尼亚北部)'],
            ['code' => 'us-east-2', 'name' => '美国东部 (俄亥俄)'],
            ['code' => 'us-west-1', 'name' => '美国西部 (加利福尼亚北部)'],
            ['code' => 'us-west-2', 'name' => '美国西部 (俄勒冈)'],
            ['code' => 'af-south-1', 'name' => '非洲 (开普敦)'],
            ['code' => 'ap-east-1', 'name' => '亚太地区 (香港)'],
            ['code' => 'ap-south-1', 'name' => '亚太地区 (孟买)'],
            ['code' => 'ap-south-2', 'name' => '亚太地区 (海得拉巴)'],
            ['code' => 'ap-northeast-1', 'name' => '亚太地区 (东京)'],
            ['code' => 'ap-northeast-2', 'name' => '亚太地区 (首尔)'],
            ['code' => 'ap-northeast-3', 'name' => '亚太地区 (大阪)'],
            ['code' => 'ap-southeast-1', 'name' => '亚太地区 (新加坡)'],
            ['code' => 'ap-southeast-2', 'name' => '亚太地区 (悉尼)'],
            ['code' => 'ap-southeast-3', 'name' => '亚太地区 (雅加达)'],
            ['code' => 'ap-southeast-4', 'name' => '亚太地区 (墨尔本)'],
            ['code' => 'ca-central-1', 'name' => '加拿大 (中部)'],
            ['code' => 'ca-west-1', 'name' => '加拿大 (卡尔加里)'],
            ['code' => 'eu-central-1', 'name' => '欧洲 (法兰克福)'],
            ['code' => 'eu-central-2', 'name' => '欧洲 (苏黎世)'],
            ['code' => 'eu-north-1', 'name' => '欧洲 (斯德哥尔摩)'],
            ['code' => 'eu-south-1', 'name' => '欧洲 (米兰)'],
            ['code' => 'eu-south-2', 'name' => '欧洲 (西班牙)'],
            ['code' => 'eu-west-1', 'name' => '欧洲 (爱尔兰)'],
            ['code' => 'eu-west-2', 'name' => '欧洲 (伦敦)'],
            ['code' => 'eu-west-3', 'name' => '欧洲 (巴黎)'],
            ['code' => 'il-central-1', 'name' => '以色列 (特拉维夫)'],
            ['code' => 'me-central-1', 'name' => '中东 (阿联酋)'],
            ['code' => 'me-south-1', 'name' => '中东 (巴林)'],
            ['code' => 'sa-east-1', 'name' => '南美洲 (圣保罗)'],
        ];

        return response()->json($regions);
    }

    public function getInstanceTypes()
    {
        // 使用文件缓存（1小时）
        $cacheFile = storage_path('app/cache/instance_types.json');
        $cacheTime = 3600; // 1小时

        // 检查缓存文件是否存在且未过期
        if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheTime) {
            $instanceTypes = json_decode(file_get_contents($cacheFile), true);
            if ($instanceTypes) {
                return response()->json($instanceTypes);
            }
        }

        // 生成新数据
        $instanceTypes = $this->getInstanceTypesData();

        // 保存到缓存文件
        $cacheDir = dirname($cacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        file_put_contents($cacheFile, json_encode($instanceTypes));

        return response()->json($instanceTypes);
    }

    private function getInstanceTypesData()
    {
        // AWS官方完整实例类型列表（2024年最新）
        $instanceTypes = [
            // 通用型 - T系列 (突发性能实例)
            ['name' => 't2.nano', 'specs' => '1 vCPU, 0.5 GB RAM - 突发性能'],
            ['name' => 't2.micro', 'specs' => '1 vCPU, 1 GB RAM - 突发性能'],
            ['name' => 't2.small', 'specs' => '1 vCPU, 2 GB RAM - 突发性能'],
            ['name' => 't2.medium', 'specs' => '2 vCPU, 4 GB RAM - 突发性能'],
            ['name' => 't2.large', 'specs' => '2 vCPU, 8 GB RAM - 突发性能'],
            ['name' => 't2.xlarge', 'specs' => '4 vCPU, 16 GB RAM - 突发性能'],
            ['name' => 't2.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - 突发性能'],

            ['name' => 't3.nano', 'specs' => '2 vCPU, 0.5 GB RAM - 突发性能'],
            ['name' => 't3.micro', 'specs' => '2 vCPU, 1 GB RAM - 突发性能'],
            ['name' => 't3.small', 'specs' => '2 vCPU, 2 GB RAM - 突发性能'],
            ['name' => 't3.medium', 'specs' => '2 vCPU, 4 GB RAM - 突发性能'],
            ['name' => 't3.large', 'specs' => '2 vCPU, 8 GB RAM - 突发性能'],
            ['name' => 't3.xlarge', 'specs' => '4 vCPU, 16 GB RAM - 突发性能'],
            ['name' => 't3.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - 突发性能'],

            ['name' => 't3a.nano', 'specs' => '2 vCPU, 0.5 GB RAM - AMD处理器'],
            ['name' => 't3a.micro', 'specs' => '2 vCPU, 1 GB RAM - AMD处理器'],
            ['name' => 't3a.small', 'specs' => '2 vCPU, 2 GB RAM - AMD处理器'],
            ['name' => 't3a.medium', 'specs' => '2 vCPU, 4 GB RAM - AMD处理器'],
            ['name' => 't3a.large', 'specs' => '2 vCPU, 8 GB RAM - AMD处理器'],
            ['name' => 't3a.xlarge', 'specs' => '4 vCPU, 16 GB RAM - AMD处理器'],
            ['name' => 't3a.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - AMD处理器'],

            ['name' => 't4g.nano', 'specs' => '2 vCPU, 0.5 GB RAM - ARM Graviton2'],
            ['name' => 't4g.micro', 'specs' => '2 vCPU, 1 GB RAM - ARM Graviton2'],
            ['name' => 't4g.small', 'specs' => '2 vCPU, 2 GB RAM - ARM Graviton2'],
            ['name' => 't4g.medium', 'specs' => '2 vCPU, 4 GB RAM - ARM Graviton2'],
            ['name' => 't4g.large', 'specs' => '2 vCPU, 8 GB RAM - ARM Graviton2'],
            ['name' => 't4g.xlarge', 'specs' => '4 vCPU, 16 GB RAM - ARM Graviton2'],
            ['name' => 't4g.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - ARM Graviton2'],

            // 通用型 - M系列 (平衡型)
            ['name' => 'm5.large', 'specs' => '2 vCPU, 8 GB RAM - 平衡型'],
            ['name' => 'm5.xlarge', 'specs' => '4 vCPU, 16 GB RAM - 平衡型'],
            ['name' => 'm5.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - 平衡型'],
            ['name' => 'm5.4xlarge', 'specs' => '16 vCPU, 64 GB RAM - 平衡型'],
            ['name' => 'm5.8xlarge', 'specs' => '32 vCPU, 128 GB RAM - 平衡型'],
            ['name' => 'm5.12xlarge', 'specs' => '48 vCPU, 192 GB RAM - 平衡型'],
            ['name' => 'm5.16xlarge', 'specs' => '64 vCPU, 256 GB RAM - 平衡型'],
            ['name' => 'm5.24xlarge', 'specs' => '96 vCPU, 384 GB RAM - 平衡型'],

            ['name' => 'm5a.large', 'specs' => '2 vCPU, 8 GB RAM - AMD处理器'],
            ['name' => 'm5a.xlarge', 'specs' => '4 vCPU, 16 GB RAM - AMD处理器'],
            ['name' => 'm5a.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - AMD处理器'],
            ['name' => 'm5a.4xlarge', 'specs' => '16 vCPU, 64 GB RAM - AMD处理器'],
            ['name' => 'm5a.8xlarge', 'specs' => '32 vCPU, 128 GB RAM - AMD处理器'],
            ['name' => 'm5a.12xlarge', 'specs' => '48 vCPU, 192 GB RAM - AMD处理器'],
            ['name' => 'm5a.16xlarge', 'specs' => '64 vCPU, 256 GB RAM - AMD处理器'],
            ['name' => 'm5a.24xlarge', 'specs' => '96 vCPU, 384 GB RAM - AMD处理器'],

            ['name' => 'm6i.large', 'specs' => '2 vCPU, 8 GB RAM - 第3代Intel'],
            ['name' => 'm6i.xlarge', 'specs' => '4 vCPU, 16 GB RAM - 第3代Intel'],
            ['name' => 'm6i.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - 第3代Intel'],
            ['name' => 'm6i.4xlarge', 'specs' => '16 vCPU, 64 GB RAM - 第3代Intel'],
            ['name' => 'm6i.8xlarge', 'specs' => '32 vCPU, 128 GB RAM - 第3代Intel'],
            ['name' => 'm6i.12xlarge', 'specs' => '48 vCPU, 192 GB RAM - 第3代Intel'],
            ['name' => 'm6i.16xlarge', 'specs' => '64 vCPU, 256 GB RAM - 第3代Intel'],
            ['name' => 'm6i.24xlarge', 'specs' => '96 vCPU, 384 GB RAM - 第3代Intel'],
            ['name' => 'm6i.32xlarge', 'specs' => '128 vCPU, 512 GB RAM - 第3代Intel'],

            ['name' => 'm6a.large', 'specs' => '2 vCPU, 8 GB RAM - 第3代AMD'],
            ['name' => 'm6a.xlarge', 'specs' => '4 vCPU, 16 GB RAM - 第3代AMD'],
            ['name' => 'm6a.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - 第3代AMD'],
            ['name' => 'm6a.4xlarge', 'specs' => '16 vCPU, 64 GB RAM - 第3代AMD'],
            ['name' => 'm6a.8xlarge', 'specs' => '32 vCPU, 128 GB RAM - 第3代AMD'],
            ['name' => 'm6a.12xlarge', 'specs' => '48 vCPU, 192 GB RAM - 第3代AMD'],
            ['name' => 'm6a.16xlarge', 'specs' => '64 vCPU, 256 GB RAM - 第3代AMD'],
            ['name' => 'm6a.24xlarge', 'specs' => '96 vCPU, 384 GB RAM - 第3代AMD'],
            ['name' => 'm6a.32xlarge', 'specs' => '128 vCPU, 512 GB RAM - 第3代AMD'],
            ['name' => 'm6a.48xlarge', 'specs' => '192 vCPU, 768 GB RAM - 第3代AMD'],

            ['name' => 'm6g.medium', 'specs' => '1 vCPU, 4 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.large', 'specs' => '2 vCPU, 8 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.xlarge', 'specs' => '4 vCPU, 16 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.2xlarge', 'specs' => '8 vCPU, 32 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.4xlarge', 'specs' => '16 vCPU, 64 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.8xlarge', 'specs' => '32 vCPU, 128 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.12xlarge', 'specs' => '48 vCPU, 192 GB RAM - ARM Graviton2'],
            ['name' => 'm6g.16xlarge', 'specs' => '64 vCPU, 256 GB RAM - ARM Graviton2'],

            // 计算优化型 - C系列
            ['name' => 'c5.large', 'specs' => '2 vCPU, 4 GB RAM - 计算优化'],
            ['name' => 'c5.xlarge', 'specs' => '4 vCPU, 8 GB RAM - 计算优化'],
            ['name' => 'c5.2xlarge', 'specs' => '8 vCPU, 16 GB RAM - 计算优化'],
            ['name' => 'c5.4xlarge', 'specs' => '16 vCPU, 32 GB RAM - 计算优化'],
            ['name' => 'c5.9xlarge', 'specs' => '36 vCPU, 72 GB RAM - 计算优化'],
            ['name' => 'c5.12xlarge', 'specs' => '48 vCPU, 96 GB RAM - 计算优化'],
            ['name' => 'c5.18xlarge', 'specs' => '72 vCPU, 144 GB RAM - 计算优化'],
            ['name' => 'c5.24xlarge', 'specs' => '96 vCPU, 192 GB RAM - 计算优化'],

            ['name' => 'c5a.large', 'specs' => '2 vCPU, 4 GB RAM - AMD计算优化'],
            ['name' => 'c5a.xlarge', 'specs' => '4 vCPU, 8 GB RAM - AMD计算优化'],
            ['name' => 'c5a.2xlarge', 'specs' => '8 vCPU, 16 GB RAM - AMD计算优化'],
            ['name' => 'c5a.4xlarge', 'specs' => '16 vCPU, 32 GB RAM - AMD计算优化'],
            ['name' => 'c5a.8xlarge', 'specs' => '32 vCPU, 64 GB RAM - AMD计算优化'],
            ['name' => 'c5a.12xlarge', 'specs' => '48 vCPU, 96 GB RAM - AMD计算优化'],
            ['name' => 'c5a.16xlarge', 'specs' => '64 vCPU, 128 GB RAM - AMD计算优化'],
            ['name' => 'c5a.24xlarge', 'specs' => '96 vCPU, 192 GB RAM - AMD计算优化'],

            ['name' => 'c6i.large', 'specs' => '2 vCPU, 4 GB RAM - 第3代Intel'],
            ['name' => 'c6i.xlarge', 'specs' => '4 vCPU, 8 GB RAM - 第3代Intel'],
            ['name' => 'c6i.2xlarge', 'specs' => '8 vCPU, 16 GB RAM - 第3代Intel'],
            ['name' => 'c6i.4xlarge', 'specs' => '16 vCPU, 32 GB RAM - 第3代Intel'],
            ['name' => 'c6i.8xlarge', 'specs' => '32 vCPU, 64 GB RAM - 第3代Intel'],
            ['name' => 'c6i.12xlarge', 'specs' => '48 vCPU, 96 GB RAM - 第3代Intel'],
            ['name' => 'c6i.16xlarge', 'specs' => '64 vCPU, 128 GB RAM - 第3代Intel'],
            ['name' => 'c6i.24xlarge', 'specs' => '96 vCPU, 192 GB RAM - 第3代Intel'],
            ['name' => 'c6i.32xlarge', 'specs' => '128 vCPU, 256 GB RAM - 第3代Intel'],

            ['name' => 'c6g.medium', 'specs' => '1 vCPU, 2 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.large', 'specs' => '2 vCPU, 4 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.xlarge', 'specs' => '4 vCPU, 8 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.2xlarge', 'specs' => '8 vCPU, 16 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.4xlarge', 'specs' => '16 vCPU, 32 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.8xlarge', 'specs' => '32 vCPU, 64 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.12xlarge', 'specs' => '48 vCPU, 96 GB RAM - ARM Graviton2'],
            ['name' => 'c6g.16xlarge', 'specs' => '64 vCPU, 128 GB RAM - ARM Graviton2'],

            // 内存优化型 - R系列
            ['name' => 'r5.large', 'specs' => '2 vCPU, 16 GB RAM - 内存优化'],
            ['name' => 'r5.xlarge', 'specs' => '4 vCPU, 32 GB RAM - 内存优化'],
            ['name' => 'r5.2xlarge', 'specs' => '8 vCPU, 64 GB RAM - 内存优化'],
            ['name' => 'r5.4xlarge', 'specs' => '16 vCPU, 128 GB RAM - 内存优化'],
            ['name' => 'r5.8xlarge', 'specs' => '32 vCPU, 256 GB RAM - 内存优化'],
            ['name' => 'r5.12xlarge', 'specs' => '48 vCPU, 384 GB RAM - 内存优化'],
            ['name' => 'r5.16xlarge', 'specs' => '64 vCPU, 512 GB RAM - 内存优化'],
            ['name' => 'r5.24xlarge', 'specs' => '96 vCPU, 768 GB RAM - 内存优化'],

            ['name' => 'r5a.large', 'specs' => '2 vCPU, 16 GB RAM - AMD内存优化'],
            ['name' => 'r5a.xlarge', 'specs' => '4 vCPU, 32 GB RAM - AMD内存优化'],
            ['name' => 'r5a.2xlarge', 'specs' => '8 vCPU, 64 GB RAM - AMD内存优化'],
            ['name' => 'r5a.4xlarge', 'specs' => '16 vCPU, 128 GB RAM - AMD内存优化'],
            ['name' => 'r5a.8xlarge', 'specs' => '32 vCPU, 256 GB RAM - AMD内存优化'],
            ['name' => 'r5a.12xlarge', 'specs' => '48 vCPU, 384 GB RAM - AMD内存优化'],
            ['name' => 'r5a.16xlarge', 'specs' => '64 vCPU, 512 GB RAM - AMD内存优化'],
            ['name' => 'r5a.24xlarge', 'specs' => '96 vCPU, 768 GB RAM - AMD内存优化'],

            ['name' => 'r6i.large', 'specs' => '2 vCPU, 16 GB RAM - 第3代Intel'],
            ['name' => 'r6i.xlarge', 'specs' => '4 vCPU, 32 GB RAM - 第3代Intel'],
            ['name' => 'r6i.2xlarge', 'specs' => '8 vCPU, 64 GB RAM - 第3代Intel'],
            ['name' => 'r6i.4xlarge', 'specs' => '16 vCPU, 128 GB RAM - 第3代Intel'],
            ['name' => 'r6i.8xlarge', 'specs' => '32 vCPU, 256 GB RAM - 第3代Intel'],
            ['name' => 'r6i.12xlarge', 'specs' => '48 vCPU, 384 GB RAM - 第3代Intel'],
            ['name' => 'r6i.16xlarge', 'specs' => '64 vCPU, 512 GB RAM - 第3代Intel'],
            ['name' => 'r6i.24xlarge', 'specs' => '96 vCPU, 768 GB RAM - 第3代Intel'],
            ['name' => 'r6i.32xlarge', 'specs' => '128 vCPU, 1024 GB RAM - 第3代Intel'],

            ['name' => 'r6g.medium', 'specs' => '1 vCPU, 8 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.large', 'specs' => '2 vCPU, 16 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.xlarge', 'specs' => '4 vCPU, 32 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.2xlarge', 'specs' => '8 vCPU, 64 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.4xlarge', 'specs' => '16 vCPU, 128 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.8xlarge', 'specs' => '32 vCPU, 256 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.12xlarge', 'specs' => '48 vCPU, 384 GB RAM - ARM Graviton2'],
            ['name' => 'r6g.16xlarge', 'specs' => '64 vCPU, 512 GB RAM - ARM Graviton2'],

            // 存储优化型 - I系列 (高I/O性能)
            ['name' => 'i3.large', 'specs' => '2 vCPU, 15.25 GB RAM, 475 GB NVMe SSD'],
            ['name' => 'i3.xlarge', 'specs' => '4 vCPU, 30.5 GB RAM, 950 GB NVMe SSD'],
            ['name' => 'i3.2xlarge', 'specs' => '8 vCPU, 61 GB RAM, 1,900 GB NVMe SSD'],
            ['name' => 'i3.4xlarge', 'specs' => '16 vCPU, 122 GB RAM, 3,800 GB NVMe SSD'],
            ['name' => 'i3.8xlarge', 'specs' => '32 vCPU, 244 GB RAM, 7,600 GB NVMe SSD'],
            ['name' => 'i3.16xlarge', 'specs' => '64 vCPU, 488 GB RAM, 15,200 GB NVMe SSD'],

            ['name' => 'i4i.large', 'specs' => '2 vCPU, 16 GB RAM, 468 GB NVMe SSD'],
            ['name' => 'i4i.xlarge', 'specs' => '4 vCPU, 32 GB RAM, 937 GB NVMe SSD'],
            ['name' => 'i4i.2xlarge', 'specs' => '8 vCPU, 64 GB RAM, 1,875 GB NVMe SSD'],
            ['name' => 'i4i.4xlarge', 'specs' => '16 vCPU, 128 GB RAM, 3,750 GB NVMe SSD'],
            ['name' => 'i4i.8xlarge', 'specs' => '32 vCPU, 256 GB RAM, 7,500 GB NVMe SSD'],
            ['name' => 'i4i.16xlarge', 'specs' => '64 vCPU, 512 GB RAM, 15,000 GB NVMe SSD'],
            ['name' => 'i4i.32xlarge', 'specs' => '128 vCPU, 1024 GB RAM, 30,000 GB NVMe SSD'],

            // 存储优化型 - D系列 (密集型HDD存储)
            ['name' => 'd3.xlarge', 'specs' => '4 vCPU, 32 GB RAM, 3 x 1,980 GB HDD'],
            ['name' => 'd3.2xlarge', 'specs' => '8 vCPU, 64 GB RAM, 6 x 1,980 GB HDD'],
            ['name' => 'd3.4xlarge', 'specs' => '16 vCPU, 128 GB RAM, 12 x 1,980 GB HDD'],
            ['name' => 'd3.8xlarge', 'specs' => '32 vCPU, 256 GB RAM, 24 x 1,980 GB HDD'],

            ['name' => 'd3en.large', 'specs' => '2 vCPU, 8 GB RAM, 2 x 14,000 GB HDD'],
            ['name' => 'd3en.xlarge', 'specs' => '4 vCPU, 16 GB RAM, 4 x 14,000 GB HDD'],
            ['name' => 'd3en.2xlarge', 'specs' => '8 vCPU, 32 GB RAM, 8 x 14,000 GB HDD'],
            ['name' => 'd3en.4xlarge', 'specs' => '16 vCPU, 64 GB RAM, 16 x 14,000 GB HDD'],
            ['name' => 'd3en.6xlarge', 'specs' => '24 vCPU, 96 GB RAM, 24 x 14,000 GB HDD'],
            ['name' => 'd3en.8xlarge', 'specs' => '32 vCPU, 128 GB RAM, 32 x 14,000 GB HDD'],
            ['name' => 'd3en.12xlarge', 'specs' => '48 vCPU, 192 GB RAM, 48 x 14,000 GB HDD'],

            // 加速计算型 - P系列 (GPU机器学习)
            ['name' => 'p3.2xlarge', 'specs' => '8 vCPU, 61 GB RAM, 1 x NVIDIA V100 GPU'],
            ['name' => 'p3.8xlarge', 'specs' => '32 vCPU, 244 GB RAM, 4 x NVIDIA V100 GPU'],
            ['name' => 'p3.16xlarge', 'specs' => '64 vCPU, 488 GB RAM, 8 x NVIDIA V100 GPU'],

            ['name' => 'p4d.24xlarge', 'specs' => '96 vCPU, 1152 GB RAM, 8 x NVIDIA A100 GPU'],

            // 加速计算型 - G系列 (图形工作站)
            ['name' => 'g4dn.xlarge', 'specs' => '4 vCPU, 16 GB RAM, 1 x NVIDIA T4 GPU'],
            ['name' => 'g4dn.2xlarge', 'specs' => '8 vCPU, 32 GB RAM, 1 x NVIDIA T4 GPU'],
            ['name' => 'g4dn.4xlarge', 'specs' => '16 vCPU, 64 GB RAM, 1 x NVIDIA T4 GPU'],
            ['name' => 'g4dn.8xlarge', 'specs' => '32 vCPU, 128 GB RAM, 1 x NVIDIA T4 GPU'],
            ['name' => 'g4dn.12xlarge', 'specs' => '48 vCPU, 192 GB RAM, 4 x NVIDIA T4 GPU'],
            ['name' => 'g4dn.16xlarge', 'specs' => '64 vCPU, 256 GB RAM, 1 x NVIDIA T4 GPU'],

            ['name' => 'g5.xlarge', 'specs' => '4 vCPU, 16 GB RAM, 1 x NVIDIA A10G GPU'],
            ['name' => 'g5.2xlarge', 'specs' => '8 vCPU, 32 GB RAM, 1 x NVIDIA A10G GPU'],
            ['name' => 'g5.4xlarge', 'specs' => '16 vCPU, 64 GB RAM, 1 x NVIDIA A10G GPU'],
            ['name' => 'g5.8xlarge', 'specs' => '32 vCPU, 128 GB RAM, 1 x NVIDIA A10G GPU'],
            ['name' => 'g5.12xlarge', 'specs' => '48 vCPU, 192 GB RAM, 4 x NVIDIA A10G GPU'],
            ['name' => 'g5.16xlarge', 'specs' => '64 vCPU, 256 GB RAM, 1 x NVIDIA A10G GPU'],
            ['name' => 'g5.24xlarge', 'specs' => '96 vCPU, 384 GB RAM, 4 x NVIDIA A10G GPU'],
            ['name' => 'g5.48xlarge', 'specs' => '192 vCPU, 768 GB RAM, 8 x NVIDIA A10G GPU'],

            // 高性能计算 - Hpc系列
            ['name' => 'hpc6a.48xlarge', 'specs' => '96 vCPU, 384 GB RAM - 高性能计算'],

            // 高内存型 - X系列
            ['name' => 'x1.16xlarge', 'specs' => '64 vCPU, 976 GB RAM - 高内存'],
            ['name' => 'x1.32xlarge', 'specs' => '128 vCPU, 1,952 GB RAM - 高内存'],

            ['name' => 'x1e.xlarge', 'specs' => '4 vCPU, 122 GB RAM - 高内存'],
            ['name' => 'x1e.2xlarge', 'specs' => '8 vCPU, 244 GB RAM - 高内存'],
            ['name' => 'x1e.4xlarge', 'specs' => '16 vCPU, 488 GB RAM - 高内存'],
            ['name' => 'x1e.8xlarge', 'specs' => '32 vCPU, 976 GB RAM - 高内存'],
            ['name' => 'x1e.16xlarge', 'specs' => '64 vCPU, 1,952 GB RAM - 高内存'],
            ['name' => 'x1e.32xlarge', 'specs' => '128 vCPU, 3,904 GB RAM - 高内存'],

            ['name' => 'x2gd.medium', 'specs' => '1 vCPU, 16 GB RAM - ARM高内存'],
            ['name' => 'x2gd.large', 'specs' => '2 vCPU, 32 GB RAM - ARM高内存'],
            ['name' => 'x2gd.xlarge', 'specs' => '4 vCPU, 64 GB RAM - ARM高内存'],
            ['name' => 'x2gd.2xlarge', 'specs' => '8 vCPU, 128 GB RAM - ARM高内存'],
            ['name' => 'x2gd.4xlarge', 'specs' => '16 vCPU, 256 GB RAM - ARM高内存'],
            ['name' => 'x2gd.8xlarge', 'specs' => '32 vCPU, 512 GB RAM - ARM高内存'],
            ['name' => 'x2gd.12xlarge', 'specs' => '48 vCPU, 768 GB RAM - ARM高内存'],
            ['name' => 'x2gd.16xlarge', 'specs' => '64 vCPU, 1024 GB RAM - ARM高内存'],

            // 网络优化型
            ['name' => 'a1.medium', 'specs' => '1 vCPU, 2 GB RAM - ARM Graviton'],
            ['name' => 'a1.large', 'specs' => '2 vCPU, 4 GB RAM - ARM Graviton'],
            ['name' => 'a1.xlarge', 'specs' => '4 vCPU, 8 GB RAM - ARM Graviton'],
            ['name' => 'a1.2xlarge', 'specs' => '8 vCPU, 16 GB RAM - ARM Graviton'],
            ['name' => 'a1.4xlarge', 'specs' => '16 vCPU, 32 GB RAM - ARM Graviton'],
        ];

        return $instanceTypes;
    }

    public function getAmis(Request $request)
    {
        $request->validate([
            'region' => 'required|string',
            'os_type' => 'required|string',
            'account_id' => 'required|exists:aws_accounts,id'
        ]);

        $user = Auth::user();
        $account = AwsAccount::where('id', $request->account_id)
            ->where('user_id', $user->id)
            ->where('status', 1)
            ->first();

        if (!$account) {
            return response()->json(['error' => '账户不存在或未启用'], 404);
        }

        // 尝试从AWS API获取真实AMI数据，如果失败则使用预定义数据
        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            // 获取AWS官方AMI
            $realAmis = $this->getOfficialAmis($ec2Client, $request->os_type);
            if (!empty($realAmis)) {
                return response()->json($realAmis);
            }
        } catch (AwsException $e) {
            // 如果AWS API调用失败，继续使用预定义数据
            \Log::warning('AWS AMI API调用失败: ' . $e->getMessage());
        }

        // 根据操作系统类型返回对应的AMI列表（预定义数据）
        $amis = [];

        // 根据AWS官方文档的操作系统分类和命名
        switch ($request->os_type) {
            case 'Amazon Linux 2023':
                $amis = [
                    ['id' => 'ami-al2023-kernel-6.1-x86_64', 'name' => 'Amazon Linux 2023 AMI (Kernel 6.1)', 'description' => 'Amazon Linux 2023 with Kernel 6.1, x86_64'],
                    ['id' => 'ami-al2023-kernel-6.1-arm64', 'name' => 'Amazon Linux 2023 AMI (Kernel 6.1, ARM64)', 'description' => 'Amazon Linux 2023 with Kernel 6.1, ARM64'],
                    ['id' => 'ami-al2023-minimal-kernel-6.1-x86_64', 'name' => 'Amazon Linux 2023 AMI Minimal (Kernel 6.1)', 'description' => 'Amazon Linux 2023 Minimal with Kernel 6.1, x86_64'],
                ];
                break;
            case 'Amazon Linux 2':
                $amis = [
                    ['id' => 'ami-amzn2-hvm-x86_64-gp2', 'name' => 'Amazon Linux 2 AMI (HVM) - Kernel 5.10', 'description' => 'Amazon Linux 2 with Kernel 5.10, x86_64, GP2'],
                    ['id' => 'ami-amzn2-hvm-arm64-gp2', 'name' => 'Amazon Linux 2 AMI (HVM, ARM64) - Kernel 5.10', 'description' => 'Amazon Linux 2 with Kernel 5.10, ARM64, GP2'],
                    ['id' => 'ami-amzn2-minimal-hvm-x86_64-ebs', 'name' => 'Amazon Linux 2 AMI Minimal (HVM)', 'description' => 'Amazon Linux 2 Minimal, x86_64, EBS'],
                ];
                break;
            case 'Ubuntu Server':
                $amis = [
                    ['id' => 'ami-ubuntu-24.04-lts-x86_64', 'name' => 'Ubuntu Server 24.04 LTS', 'description' => 'Ubuntu Server 24.04 LTS, x86_64'],
                    ['id' => 'ami-ubuntu-22.04-lts-x86_64', 'name' => 'Ubuntu Server 22.04 LTS', 'description' => 'Ubuntu Server 22.04 LTS, x86_64'],
                    ['id' => 'ami-ubuntu-20.04-lts-x86_64', 'name' => 'Ubuntu Server 20.04 LTS', 'description' => 'Ubuntu Server 20.04 LTS, x86_64'],
                    ['id' => 'ami-ubuntu-18.04-lts-x86_64', 'name' => 'Ubuntu Server 18.04 LTS', 'description' => 'Ubuntu Server 18.04 LTS, x86_64'],
                ];
                break;
            case 'Windows Server':
                $amis = [
                    ['id' => 'ami-windows-server-2022-english-full-base', 'name' => 'Microsoft Windows Server 2022 Base', 'description' => 'Windows Server 2022 English Full Base'],
                    ['id' => 'ami-windows-server-2019-english-full-base', 'name' => 'Microsoft Windows Server 2019 Base', 'description' => 'Windows Server 2019 English Full Base'],
                    ['id' => 'ami-windows-server-2016-english-full-base', 'name' => 'Microsoft Windows Server 2016 Base', 'description' => 'Windows Server 2016 English Full Base'],
                ];
                break;
            case 'Red Hat Enterprise Linux':
                $amis = [
                    ['id' => 'ami-rhel-9-x86_64-hvm', 'name' => 'Red Hat Enterprise Linux 9 (HVM)', 'description' => 'RHEL 9, x86_64, HVM'],
                    ['id' => 'ami-rhel-8-x86_64-hvm', 'name' => 'Red Hat Enterprise Linux 8 (HVM)', 'description' => 'RHEL 8, x86_64, HVM'],
                    ['id' => 'ami-rhel-7-x86_64-hvm', 'name' => 'Red Hat Enterprise Linux 7 (HVM)', 'description' => 'RHEL 7, x86_64, HVM'],
                ];
                break;
            // case 'SUSE Linux Enterprise Server':
            //     $amis = [
            //         ['id' => 'ami-sles-15-sp5-x86_64-hvm', 'name' => 'SUSE Linux Enterprise Server 15 SP5', 'description' => 'SLES 15 SP5, x86_64, HVM'],
            //         ['id' => 'ami-sles-12-sp5-x86_64-hvm', 'name' => 'SUSE Linux Enterprise Server 12 SP5', 'description' => 'SLES 12 SP5, x86_64, HVM'],
            //     ];
            //     break;
            // macOS 在很多区域不可用，暂时注释
            // case 'macOS':
            //     $amis = [
            //         ['id' => 'ami-macos-sonoma-x86_64', 'name' => 'macOS Sonoma', 'description' => 'macOS Sonoma, x86_64'],
            //         ['id' => 'ami-macos-ventura-x86_64', 'name' => 'macOS Ventura', 'description' => 'macOS Ventura, x86_64'],
            //         ['id' => 'ami-macos-monterey-x86_64', 'name' => 'macOS Monterey', 'description' => 'macOS Monterey, x86_64'],
            //     ];
            //     break;
            default:
                $amis = [];
        }

        return response()->json($amis);
    }

    /**
     * 获取AWS官方AMI
     */
    private function getOfficialAmis($ec2Client, $osType)
    {
        try {
            $filters = [];
            $owners = ['amazon']; // AWS官方AMI

            // 根据操作系统类型设置过滤器
            switch ($osType) {
                case 'Amazon Linux 2023':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['al2023-ami-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                    ];
                    break;
                case 'Amazon Linux 2':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['amzn2-ami-hvm-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                    ];
                    break;
                case 'Ubuntu Server':
                    $owners = ['099720109477']; // Canonical的AWS账户ID
                    $filters = [
                        ['Name' => 'name', 'Values' => ['ubuntu/images/hvm-ssd/ubuntu-*-server-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                    ];
                    break;
                case 'Windows Server':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['Windows_Server-*-English-Full-Base-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'platform', 'Values' => ['windows']],
                    ];
                    break;
                case 'Red Hat Enterprise Linux':
                    $owners = ['309956199498']; // Red Hat的AWS账户ID
                    $filters = [
                        ['Name' => 'name', 'Values' => ['RHEL-*']],
                        ['Name' => 'state', 'Values' => ['available']],
                        ['Name' => 'architecture', 'Values' => ['x86_64']],
                    ];
                    break;
                // case 'SUSE Linux Enterprise Server':
                //     $owners = ['013907871322']; // SUSE的AWS账户ID
                //     $filters = [
                //         ['Name' => 'name', 'Values' => ['suse-sles-*']],
                //         ['Name' => 'state', 'Values' => ['available']],
                //         ['Name' => 'architecture', 'Values' => ['x86_64']],
                //     ];
                //     break;
                default:
                    return [];
            }

            $result = $ec2Client->describeImages([
                'Owners' => $owners,
                'Filters' => $filters,
                'MaxResults' => 10
            ]);

            $amis = [];
            foreach ($result['Images'] as $image) {
                $amis[] = [
                    'id' => $image['ImageId'],
                    'name' => $image['Name'],
                    'description' => $image['Description'] ?? '官方AMI镜像',
                    'creation_date' => $image['CreationDate'] ?? '',
                    'architecture' => $image['Architecture'] ?? 'x86_64',
                    'virtualization_type' => $image['VirtualizationType'] ?? 'hvm'
                ];
            }

            // 按创建日期排序，最新的在前
            usort($amis, function($a, $b) {
                return strcmp($b['creation_date'], $a['creation_date']);
            });

            return array_slice($amis, 0, 5); // 返回最新的5个AMI

        } catch (AwsException $e) {
            \Log::error('获取官方AMI失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取支持的操作系统列表（基于AWS官方文档）
     */
    public function getSupportedOperatingSystems()
    {
        $operatingSystems = [
            [
                'id' => 'Amazon Linux 2023',
                'name' => 'Amazon Linux 2023',
                'description' => '最新的AWS优化Linux发行版',
                'icon' => 'https://a0.awsstatic.com/libra-css/images/logos/aws_logo_smile_179x109.png',
                'category' => 'Linux',
                'official' => true
            ],
            [
                'id' => 'Amazon Linux 2',
                'name' => 'Amazon Linux 2',
                'description' => '长期支持的AWS Linux发行版',
                'icon' => 'https://a0.awsstatic.com/libra-css/images/logos/aws_logo_smile_179x109.png',
                'category' => 'Linux',
                'official' => true
            ],
            [
                'id' => 'Ubuntu Server',
                'name' => 'Ubuntu Server',
                'description' => '流行的开源Linux服务器发行版',
                'icon' => 'https://assets.ubuntu.com/v1/29985a98-ubuntu-logo32.png',
                'category' => 'Linux',
                'official' => true
            ],
            [
                'id' => 'Windows Server',
                'name' => 'Windows Server',
                'description' => 'Microsoft Windows服务器操作系统',
                'icon' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Windows_logo_-_2012.svg/512px-Windows_logo_-_2012.svg.png',
                'category' => 'Windows',
                'official' => true
            ],
            [
                'id' => 'Red Hat Enterprise Linux',
                'name' => 'Red Hat Enterprise Linux',
                'description' => '企业级Linux解决方案',
                'icon' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d8/Red_Hat_logo.svg/512px-Red_Hat_logo.svg.png',
                'category' => 'Linux',
                'official' => true
            ]
            // [
            //     'id' => 'SUSE Linux Enterprise Server',
            //     'name' => 'SUSE Linux Enterprise Server',
            //     'description' => '企业级SUSE Linux发行版',
            //     'icon' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d2/Suse_Logo.svg/512px-Suse_Logo.svg.png',
            //     'category' => 'Linux',
            //     'official' => true
            // ]
            // macOS 在很多区域不可用，暂时注释
            // [
            //     'id' => 'macOS',
            //     'name' => 'macOS',
            //     'description' => 'Apple macOS操作系统',
            //     'icon' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/22/MacOS_logo_%282017%29.svg/512px-MacOS_logo_%282017%29.svg.png',
            //     'category' => 'macOS',
            //     'official' => true
            // ]
        ];

        return response()->json($operatingSystems);
    }

    public function getKeyPairs(Request $request)
    {
        $request->validate([
            'account_id' => 'required|exists:aws_accounts,id',
            'region' => 'required|string'
        ]);

        $user = Auth::user();
        $account = AwsAccount::where('id', $request->account_id)
            ->where('user_id', $user->id)
            ->whereIn('status', [0, 1]) // 只允许正常状态和未测状态的账户
            ->first();

        if (!$account) {
            return response()->json(['error' => '账户不存在或状态异常'], 404);
        }

        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            $result = $ec2Client->describeKeyPairs();
            $keyPairs = [];

            foreach ($result['KeyPairs'] as $keyPair) {
                $keyPairs[$keyPair['KeyName']] = $keyPair['KeyName'];
            }

            return response()->json(['success' => true, 'data' => $keyPairs]);

        } catch (AwsException $e) {
            \Log::error('AWS Key Pairs API Error: ' . $e->getMessage(), [
                'account_id' => $request->account_id,
                'region' => $request->region,
                'aws_error_code' => $e->getAwsErrorCode(),
                'aws_error_message' => $e->getAwsErrorMessage()
            ]);
            return response()->json(['success' => false, 'message' => $e->getAwsErrorMessage()], 500);
        } catch (\Exception $e) {
            \Log::error('General Key Pairs Error: ' . $e->getMessage(), [
                'account_id' => $request->account_id,
                'region' => $request->region
            ]);
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json(['success' => false, 'message' => '获取密钥对失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取开机脚本模板
     */
    public function getUserDataTemplates(Request $request)
    {
        $request->validate([
            'os_type' => 'nullable|string|in:linux,windows,all'
        ]);

        $query = UserDataTemplate::active();

        // 如果指定了操作系统类型，进行过滤（但不包括'all'）
        if ($request->has('os_type') && $request->os_type && $request->os_type !== 'all') {
            $query->forOs($request->os_type);
        }

        $templates = $query->orderBy('category')
                          ->orderBy('name')
                          ->get(['id', 'name', 'description', 'script_content', 'os_type', 'category']);

        return response()->json(['success' => true, 'data' => $templates]);
    }

    public function getSubnets(Request $request)
    {
        $request->validate([
            'account_id' => 'required|exists:aws_accounts,id',
            'region' => 'required|string'
        ]);

        $user = Auth::user();
        $account = AwsAccount::where('id', $request->account_id)
            ->where('user_id', $user->id)
            ->whereIn('status', [0, 1])  // 只允许正常状态和未测状态的账户
            ->first();

        if (!$account) {
            return response()->json(['success' => false, 'message' => '账户不存在或状态异常'], 404);
        }

        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            $result = $ec2Client->describeSubnets();
            $subnets = [];

            foreach ($result['Subnets'] as $subnet) {
                $name = '';
                if (isset($subnet['Tags'])) {
                    foreach ($subnet['Tags'] as $tag) {
                        if ($tag['Key'] === 'Name') {
                            $name = ' (' . $tag['Value'] . ')';
                            break;
                        }
                    }
                }
                $subnets[$subnet['SubnetId']] = $subnet['SubnetId'] . $name . ' - ' . $subnet['AvailabilityZone'];
            }

            return response()->json(['success' => true, 'data' => $subnets]);

        } catch (AwsException $e) {
            return response()->json(['success' => false, 'message' => $e->getAwsErrorMessage()], 500);
        } catch (\Exception $e) {
            \Log::error('General Subnets Error: ' . $e->getMessage(), [
                'account_id' => $request->account_id,
                'region' => $request->region
            ]);
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json(['success' => false, 'message' => '获取子网失败: ' . $e->getMessage()], 500);
        }
    }

    public function getSecurityGroups(Request $request)
    {
        $request->validate([
            'account_id' => 'required|exists:aws_accounts,id',
            'region' => 'required|string'
        ]);

        $user = Auth::user();
        $account = AwsAccount::where('id', $request->account_id)
            ->where('user_id', $user->id)
            ->whereIn('status', [0, 1]) // 只允许正常状态和未测状态的账户
            ->first();

        if (!$account) {
            return response()->json(['error' => '账户不存在或状态异常'], 404);
        }

        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            $result = $ec2Client->describeSecurityGroups();
            $securityGroups = [];

            foreach ($result['SecurityGroups'] as $sg) {
                $securityGroups[] = [
                    'GroupId' => $sg['GroupId'],
                    'GroupName' => $sg['GroupName'],
                    'Description' => $sg['Description'] ?? ''
                ];
            }

            return response()->json(['success' => true, 'data' => $securityGroups]);

        } catch (AwsException $e) {
            \Log::error('AWS Security Groups API Error: ' . $e->getMessage(), [
                'account_id' => $request->account_id,
                'region' => $request->region,
                'aws_error_code' => $e->getAwsErrorCode(),
                'aws_error_message' => $e->getAwsErrorMessage()
            ]);
            return response()->json(['success' => false, 'message' => $e->getAwsErrorMessage()], 500);
        } catch (\Exception $e) {
            \Log::error('General Security Groups Error: ' . $e->getMessage(), [
                'account_id' => $request->account_id,
                'region' => $request->region
            ]);
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json(['error' => '获取安全组失败: ' . $e->getMessage()], 500);
        }
    }

    public function manageTags(Request $request)
    {
        $request->validate([
            'instances' => 'required|array',
            'instances.*.account_id' => 'required|exists:aws_accounts,id',
            'instances.*.instance_id' => 'required|string',
            'instances.*.region' => 'required|string',
            'action' => 'required|in:add,remove',
            'tags' => 'required|array'
        ]);

        $user = Auth::user();
        $results = [];

        foreach ($request->instances as $instanceData) {
            $account = AwsAccount::where('id', $instanceData['account_id'])
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => '账户不存在'
                ];
                continue;
            }

            try {
                $ec2Client = $this->createEc2Client($instanceData['region'], $account->access_key, $account->secret_key);

                $tags = collect($request->tags)->map(function ($value, $key) {
                    return ['Key' => $key, 'Value' => $value];
                })->values()->toArray();

                if ($request->action === 'add') {
                    $ec2Client->createTags([
                        'Resources' => [$instanceData['instance_id']],
                        'Tags' => $tags
                    ]);
                } else {
                    $ec2Client->deleteTags([
                        'Resources' => [$instanceData['instance_id']],
                        'Tags' => $tags
                    ]);
                }

                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => true,
                    'message' => '标签操作成功'
                ];

            } catch (AwsException $e) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => $e->getAwsErrorMessage()
                ];
            }
        }

        $successCount = collect($results)->where('success', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount > 0,
            'message' => "标签操作完成: {$successCount}/{$totalCount} 个实例成功",
            'results' => $results
        ]);
    }

    public function createSnapshot(Request $request)
    {
        $request->validate([
            'instances' => 'required|array',
            'instances.*.account_id' => 'required|exists:aws_accounts,id',
            'instances.*.instance_id' => 'required|string',
            'instances.*.region' => 'required|string',
            'description' => 'nullable|string'
        ]);

        $user = Auth::user();
        $results = [];

        foreach ($request->instances as $instanceData) {
            $account = AwsAccount::where('id', $instanceData['account_id'])
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => '账户不存在'
                ];
                continue;
            }

            try {
                $ec2Client = $this->createEc2Client($instanceData['region'], $account->access_key, $account->secret_key);

                // 获取实例的EBS卷
                $instanceResult = $ec2Client->describeInstances([
                    'InstanceIds' => [$instanceData['instance_id']]
                ]);

                $snapshots = [];
                foreach ($instanceResult['Reservations'] as $reservation) {
                    foreach ($reservation['Instances'] as $instance) {
                        foreach ($instance['BlockDeviceMappings'] as $mapping) {
                            if (isset($mapping['Ebs']['VolumeId'])) {
                                $volumeId = $mapping['Ebs']['VolumeId'];
                                $description = $request->description ?: "Snapshot of {$instanceData['instance_id']} - {$mapping['DeviceName']}";

                                $snapshotResult = $ec2Client->createSnapshot([
                                    'VolumeId' => $volumeId,
                                    'Description' => $description
                                ]);

                                $snapshots[] = $snapshotResult['SnapshotId'];
                            }
                        }
                    }
                }

                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => true,
                    'message' => '快照创建成功',
                    'snapshots' => $snapshots
                ];

            } catch (AwsException $e) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => $e->getAwsErrorMessage()
                ];
            }
        }

        $successCount = collect($results)->where('success', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount > 0,
            'message' => "快照创建完成: {$successCount}/{$totalCount} 个实例成功",
            'results' => $results
        ]);
    }

    public function manageSecurityGroups(Request $request)
    {
        $request->validate([
            'instances' => 'required|array',
            'instances.*.account_id' => 'required|exists:aws_accounts,id',
            'instances.*.instance_id' => 'required|string',
            'instances.*.region' => 'required|string',
            'security_group_ids' => 'required|array'
        ]);

        $user = Auth::user();
        $results = [];

        foreach ($request->instances as $instanceData) {
            $account = AwsAccount::where('id', $instanceData['account_id'])
                ->where('user_id', $user->id)
                ->first();

            if (!$account) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => '账户不存在'
                ];
                continue;
            }

            try {
                $ec2Client = $this->createEc2Client($instanceData['region'], $account->access_key, $account->secret_key);

                $ec2Client->modifyInstanceAttribute([
                    'InstanceId' => $instanceData['instance_id'],
                    'Groups' => $request->security_group_ids
                ]);

                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => true,
                    'message' => '安全组修改成功'
                ];

            } catch (AwsException $e) {
                $results[] = [
                    'instance_id' => $instanceData['instance_id'],
                    'success' => false,
                    'message' => $e->getAwsErrorMessage()
                ];
            }
        }

        $successCount = collect($results)->where('success', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => $successCount > 0,
            'message' => "安全组修改完成: {$successCount}/{$totalCount} 个实例成功",
            'results' => $results
        ]);
    }

    /**
     * 批量创建EC2实例
     */
    public function batchCreateInstances(Request $request)
    {
        // 处理来自前端的JSON格式account_ids
        if ($request->has('account_ids') && is_string($request->account_ids)) {
            $accountIds = json_decode($request->account_ids, true);
            $request->merge(['account_ids' => $accountIds]);
        }

        $request->validate([
            'account_ids' => 'required|array|min:1',
            'region' => 'required|string',
            'instance_type' => 'required|string',
            'ami_id' => 'required|string',
            'instance_count' => 'required|integer|min:1|max:20',
            'security_group_ids' => 'nullable|array',
            'subnet_id' => 'nullable|string',
            'key_name' => 'nullable|string',
            'instance_name' => 'required|string',
            'password' => 'nullable|string|min:8',
            'user_data' => 'nullable|string',
            'associate_public_ip' => 'nullable|boolean',
            'ebs_optimized' => 'nullable|boolean',
            'monitoring_enabled' => 'nullable|boolean',
            'disable_api_termination' => 'nullable|boolean',
            'os_type' => 'nullable|string'
        ]);

        // 自定义验证子网ID
        if ($request->subnet_id && $request->subnet_id !== 'default' && !preg_match('/^subnet-[a-f0-9]{8,17}$/', $request->subnet_id)) {
            return response()->json([
                'success' => false,
                'message' => '子网ID格式无效'
            ], 422);
        }

        $user = Auth::user();
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($request->account_ids as $accountId) {
            try {
                // 验证账户归属
                $account = AwsAccount::where('id', $accountId)
                    ->where('user_id', $user->id)
                    ->first();

                if (!$account) {
                    $results[] = [
                        'account_id' => $accountId,
                        'account_name' => 'Unknown',
                        'success' => false,
                        'message' => '账户不存在或无权限访问'
                    ];
                    $failureCount++;
                    continue;
                }

                // AWS SDK配置
                $sdkConfig = [
                    'region' => $request->region,
                    'version' => 'latest',
                    'credentials' => [
                        'key' => $account->access_key,
                        'secret' => $account->secret_key,
                    ],
                ];

                // 条件性禁用SSL验证
                if (config('aws.disable_ssl_verification', false)) {
                    $sdkConfig['http']['verify'] = false;
                }

                // 应用用户代理配置
                $this->applyProxyConfig($sdkConfig, auth()->user());

                $ec2Client = new Ec2Client($sdkConfig);

                // 准备实例创建参数
                $runInstancesParams = [
                    'ImageId' => $request->ami_id,
                    'MinCount' => $request->instance_count,
                    'MaxCount' => $request->instance_count,
                    'InstanceType' => $request->instance_type,
                ];

                // 添加可选参数
                if ($request->key_name) {
                    $runInstancesParams['KeyName'] = $request->key_name;
                }

                // 处理子网配置：如果是'default'或空值，则不指定子网（使用默认VPC的默认子网）
                if ($request->subnet_id && $request->subnet_id !== 'default') {
                    $runInstancesParams['SubnetId'] = $request->subnet_id;
                }

                // 处理安全组配置：如果没有指定、为空或为'default'，则不设置（使用默认VPC的默认安全组）
                if ($request->security_group_ids && !empty($request->security_group_ids)) {
                    // 过滤掉 'default' 值
                    $securityGroups = array_filter($request->security_group_ids, function($sg) {
                        return $sg !== 'default';
                    });

                    // 如果过滤后还有安全组，则使用；否则不设置（使用默认）
                    if (!empty($securityGroups)) {
                        $runInstancesParams['SecurityGroupIds'] = array_values($securityGroups);
                    }
                }

                // 网络接口配置
                if ($request->has('associate_public_ip')) {
                    $runInstancesParams['NetworkInterfaces'] = [
                        [
                            'DeviceIndex' => 0,
                            'AssociatePublicIpAddress' => $request->boolean('associate_public_ip'),
                            'DeleteOnTermination' => true,
                        ]
                    ];

                    // 如果使用NetworkInterfaces，需要在这里指定子网和安全组
                    if ($request->subnet_id && $request->subnet_id !== 'default') {
                        $runInstancesParams['NetworkInterfaces'][0]['SubnetId'] = $request->subnet_id;
                    }
                    if ($request->security_group_ids && !empty($request->security_group_ids)) {
                        // 过滤掉 'default' 值
                        $securityGroups = array_filter($request->security_group_ids, function($sg) {
                            return $sg !== 'default';
                        });

                        // 如果过滤后还有安全组，则使用；否则不设置（使用默认）
                        if (!empty($securityGroups)) {
                            $runInstancesParams['NetworkInterfaces'][0]['Groups'] = array_values($securityGroups);
                        }
                    }

                    // 移除顶级的SubnetId和SecurityGroupIds
                    unset($runInstancesParams['SubnetId']);
                    unset($runInstancesParams['SecurityGroupIds']);
                }

                // EBS优化
                if ($request->has('ebs_optimized')) {
                    $runInstancesParams['EbsOptimized'] = $request->boolean('ebs_optimized');
                }

                // 监控
                if ($request->has('monitoring_enabled')) {
                    $runInstancesParams['Monitoring'] = [
                        'Enabled' => $request->boolean('monitoring_enabled')
                    ];
                }

                // 终止保护
                if ($request->has('disable_api_termination')) {
                    $runInstancesParams['DisableApiTermination'] = $request->boolean('disable_api_termination');
                }

                // 用户数据脚本
                if ($request->user_data) {
                    $runInstancesParams['UserData'] = base64_encode($request->user_data);
                } elseif ($request->password) {
                    // 如果没有自定义用户数据但有密码，使用密码设置脚本
                    $userData = "#!/bin/bash\necho 'root:{$request->password}' | chpasswd";
                    $runInstancesParams['UserData'] = base64_encode($userData);
                }

                // 标签规范
                $runInstancesParams['TagSpecifications'] = [
                    [
                        'ResourceType' => 'instance',
                        'Tags' => [
                            [
                                'Key' => 'Name',
                                'Value' => $request->instance_name
                            ]
                        ]
                    ]
                ];

                // 创建实例
                $result = $ec2Client->runInstances($runInstancesParams);

                $instanceIds = collect($result['Instances'])->pluck('InstanceId')->toArray();

                // 更新账户的EC2开通状态为已开通
                $account->update(['ec2_status' => AwsAccount::EC2_STATUS_ENABLED]);

                $results[] = [
                    'account_id' => $accountId,
                    'account_name' => $account->account_name,
                    'success' => true,
                    'message' => "成功创建 {$request->instance_count} 个实例",
                    'instance_ids' => $instanceIds
                ];
                $successCount++;

                Log::info("批量创建实例成功", [
                    'account_id' => $accountId,
                    'region' => $request->region,
                    'instance_ids' => $instanceIds
                ]);

            } catch (\Exception $e) {
                Log::error("批量创建实例失败 - 账户ID: {$accountId}", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $results[] = [
                    'account_id' => $accountId,
                    'account_name' => $account->account_name ?? 'Unknown',
                    'success' => false,
                    'message' => '创建失败: ' . $e->getMessage()
                ];
                $failureCount++;
            }
        }

        return response()->json([
            'success' => $successCount > 0,
            'message' => "批量创建完成：成功 {$successCount} 个账户，失败 {$failureCount} 个账户",
            'results' => $results,
            'summary' => [
                'total' => count($request->account_ids),
                'success' => $successCount,
                'failure' => $failureCount
            ]
        ]);
    }

    // 在batchCreateInstances方法后添加异常处理
    private function handleBatchCreateException(\Exception $e)
    {
        // 检查是否是代理异常错误
        if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                               strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
            return response()->json([
                'success' => false,
                'error_type' => 'proxy_error',
                'message' => $e->getMessage()
            ], 400);
        }

        return response()->json([
            'success' => false,
            'message' => '批量创建失败: ' . $e->getMessage()
        ], 500);
    }

    /**
     * 获取账户统计数据
     */
    public function getAccountStats()
    {
        try {
            $userId = auth()->id();

            // 使用文件缓存（5分钟）
            $cacheFile = storage_path("app/cache/account_stats_{$userId}.json");
            $cacheTime = 300; // 5分钟

            // 检查缓存文件是否存在且未过期
            if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheTime) {
                $stats = json_decode(file_get_contents($cacheFile), true);
                if ($stats) {
                    return response()->json($stats);
                }
            }

            // 生成新数据
            $stats = $this->calculateAccountStats($userId);

            // 保存到缓存文件
            $cacheDir = dirname($cacheFile);
            if (!is_dir($cacheDir)) {
                mkdir($cacheDir, 0755, true);
            }
            file_put_contents($cacheFile, json_encode($stats));

            return response()->json($stats);
        } catch (\Exception $e) {
            // 如果查询失败，返回默认值
            return response()->json([
                'today' => 0,
                'yesterday' => 0,
                'day_before' => 0,
                'error' => 'Unable to fetch stats: ' . $e->getMessage()
            ]);
        }
    }

    private function calculateAccountStats($userId)
    {
        try {
            $today = now()->startOfDay();
            $yesterday = now()->subDay()->startOfDay();
            $dayBefore = now()->subDays(2)->startOfDay();

            $stats = [
                'today' => AwsAccount::where('user_id', $userId)
                    ->whereDate('created_at', $today)
                    ->count(),
                'yesterday' => AwsAccount::where('user_id', $userId)
                    ->whereDate('created_at', $yesterday)
                    ->count(),
                'day_before' => AwsAccount::where('user_id', $userId)
                    ->whereDate('created_at', $dayBefore)
                    ->count(),
            ];

            return $stats;
        } catch (\Exception $e) {
            return [
                'today' => 0,
                'yesterday' => 0,
                'day_before' => 0,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 智能选择账户
     */
    public function smartSelectAccounts(Request $request)
    {
        $request->validate([
            'time_range' => 'required|in:today,yesterday,day_before',
            'count' => 'required',
        ]);

        $userId = auth()->id();
        $timeRange = $request->time_range;
        $count = $request->count;

        // 确定日期范围
        $date = match($timeRange) {
            'today' => now()->startOfDay(),
            'yesterday' => now()->subDay()->startOfDay(),
            'day_before' => now()->subDays(2)->startOfDay(),
        };

        // 查询账户
        $query = AwsAccount::where('user_id', $userId)
            ->whereDate('created_at', $date)
            ->orderBy('created_at', 'asc');

        // 应用数量限制
        if ($count !== 'all') {
            $query->limit((int)$count);
        }

        $accounts = $query->get(['id', 'account_name', 'created_at', 'status', 'ec2_status']);

        return response()->json([
            'success' => true,
            'accounts' => $accounts,
            'message' => "成功选择 {$accounts->count()} 个账户"
        ]);
    }

    /**
     * 存储批量选择到Session
     */
    public function storeBatchSelection(Request $request)
    {
        $request->validate([
            'accounts' => 'required|array',
        ]);

        // 验证账户归属
        $accountIds = collect($request->accounts)->pluck('id');
        $validAccounts = AwsAccount::where('user_id', auth()->id())
            ->whereIn('id', $accountIds)
            ->get();

        if ($validAccounts->count() !== $accountIds->count()) {
            return response()->json([
                'success' => false,
                'message' => '部分账户无效或无权限访问'
            ]);
        }

        // 存储到Session
        session(['batch_selected_accounts' => $request->accounts]);

        return response()->json([
            'success' => true,
            'message' => '选择已保存'
        ]);
    }

    /**
     * 清除批量选择
     */
    public function clearBatchSelection()
    {
        session()->forget('batch_selected_accounts');

        return response()->json([
            'success' => true,
            'message' => '选择已清除'
        ]);
    }

    /**
     * 为单个账户创建实例（用于批量创建）
     */
    private function createSingleInstance($account, $request)
    {
        try {
            $ec2Client = $this->createEc2Client($request->region, $account->access_key, $account->secret_key);

            // 优先使用前端传递的AMI ID，如果是通用AMI ID则通过SSM参数获取官方AMI
            $amiId = $request->ami_id;

            // 直接使用前端传递的AMI ID（前端负责AMI映射）
            $amiId = $request->ami_id;

            if (!$amiId) {
                return [
                    'success' => false,
                    'error' => "缺少AMI ID"
                ];
            }

            Log::info("✅ 批量创建使用固定AMI: 地区={$request->region}, 系统={$request->selected_os}, AMI={$amiId}");

            // 准备实例参数
            $instanceParams = [
                'ImageId' => $amiId,
                'MinCount' => (int)$request->instance_count,
                'MaxCount' => (int)$request->instance_count,
                'InstanceType' => $request->instance_type,
                'BlockDeviceMappings' => [
                    [
                        'DeviceName' => '/dev/xvda',
                        'Ebs' => [
                            'VolumeSize' => (int)$request->volume_size,
                            'VolumeType' => $request->volume_type,
                            'DeleteOnTermination' => true
                        ]
                    ]
                ],
                'TagSpecifications' => [
                    [
                        'ResourceType' => 'instance',
                        'Tags' => [
                            [
                                'Key' => 'Name',
                                'Value' => $request->instance_name
                            ]
                        ]
                    ]
                ]
            ];

            // 添加可选参数
            if ($request->key_name) {
                $instanceParams['KeyName'] = $request->key_name;
            }

            // 处理子网配置：如果是'default'或空值，则不指定子网（使用默认VPC的默认子网）
            if ($request->subnet_id && $request->subnet_id !== 'default') {
                // 验证子网是否存在
                try {
                    $ec2Client->describeSubnets(['SubnetIds' => [$request->subnet_id]]);
                    $instanceParams['SubnetId'] = $request->subnet_id;
                    Log::info("✅ 子网验证成功: {$request->subnet_id}");
                } catch (\Exception $e) {
                    Log::error("❌ 子网验证失败: {$request->subnet_id}, 错误: " . $e->getMessage());
                    throw new \Exception("子网配置错误: 子网 {$request->subnet_id} 在地区 {$request->region} 不存在或无权限访问");
                }
            }

            // 处理安全组配置：如果没有指定、为空或为'default'，则不设置（使用默认VPC的默认安全组）
            if ($request->security_group_ids && !empty($request->security_group_ids)) {
                $securityGroups = is_array($request->security_group_ids)
                    ? $request->security_group_ids
                    : [$request->security_group_ids];

                // 过滤掉 'default' 值
                $securityGroups = array_filter($securityGroups, function($sg) {
                    return $sg !== 'default';
                });

                // 如果过滤后还有安全组，则验证并使用
                if (!empty($securityGroups)) {
                    // 验证安全组是否存在
                    try {
                        $ec2Client->describeSecurityGroups(['GroupIds' => $securityGroups]);
                        $instanceParams['SecurityGroupIds'] = array_values($securityGroups);
                        Log::info("✅ 安全组验证成功: " . implode(', ', $securityGroups));
                    } catch (\Exception $e) {
                        Log::error("❌ 安全组验证失败: " . implode(', ', $securityGroups) . ", 错误: " . $e->getMessage());
                        throw new \Exception("安全组配置错误: 安全组 " . implode(', ', $securityGroups) . " 在地区 {$request->region} 不存在或无权限访问");
                    }
                }
            }

            // 添加用户数据（用于设置密码）
            if ($request->password) {
                $userData = $this->generateUserData($request->selected_os, $request->password);
                if ($userData) {
                    $instanceParams['UserData'] = base64_encode($userData);
                }
            }

            // 创建实例
            $result = $ec2Client->runInstances($instanceParams);

            $instances = [];
            $instanceIds = [];
            foreach ($result['Instances'] as $instance) {
                $instanceId = $instance['InstanceId'];
                $instanceIds[] = $instanceId;
                $instances[] = [
                    'instance_id' => $instanceId,
                    'state' => $instance['State']['Name'],
                    'instance_type' => $instance['InstanceType'],
                    'launch_time' => $instance['LaunchTime']->format('Y-m-d H:i:s')
                ];
            }

            // 更新账户的EC2开通状态为已开通
            $account->update(['ec2_status' => AwsAccount::EC2_STATUS_ENABLED]);

            return [
                'success' => true,
                'instances' => $instances,
                'instance_ids' => $instanceIds
            ];

        } catch (\Exception $e) {
            Log::error("创建实例失败 - 账户: {$account->account_name}, 错误: " . $e->getMessage());

            // 只有在代理模式下才检查代理相关错误
            $user = auth()->user();
            $errorMessage = ($user && $user->proxy_mode === 'proxy' && $this->isProxyRelatedError($e->getMessage()))
                ? "代理IP配置错误，无法连接AWS服务，请检查代理设置"
                : $e->getMessage();

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        }
    }

    /**
     * 根据操作系统获取AMI ID
     */
    private function getAmiForOS($osType, $region, $ec2Client)
    {
        try {
            $filters = [];

            switch ($osType) {
                case 'Amazon Linux':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['amzn2-ami-hvm-*-x86_64-gp2']],
                        ['Name' => 'owner-alias', 'Values' => ['amazon']],
                        ['Name' => 'state', 'Values' => ['available']]
                    ];
                    break;
                case 'Ubuntu':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['ubuntu/images/hvm-ssd/ubuntu-*-amd64-server-*']],
                        ['Name' => 'owner-id', 'Values' => ['099720109477']], // Canonical
                        ['Name' => 'state', 'Values' => ['available']]
                    ];
                    break;
                case 'Windows Server':
                    $filters = [
                        ['Name' => 'name', 'Values' => ['Windows_Server-*-English-Full-Base-*']],
                        ['Name' => 'owner-alias', 'Values' => ['amazon']],
                        ['Name' => 'state', 'Values' => ['available']]
                    ];
                    break;
                default:
                    return null;
            }

            $result = $ec2Client->describeImages([
                'Filters' => $filters,
                'MaxResults' => 5
            ]);

            if (!empty($result['Images'])) {
                // 按创建日期排序，选择最新的
                usort($result['Images'], function($a, $b) {
                    return strtotime($b['CreationDate']) - strtotime($a['CreationDate']);
                });
                return $result['Images'][0]['ImageId'];
            }

            return null;
        } catch (\Exception $e) {
            Log::error("获取AMI失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成用户数据脚本
     */
    private function generateUserData($osType, $password)
    {
        switch ($osType) {
            case 'Windows Server':
                return "<powershell>\nnet user Administrator \"$password\"\n</powershell>";
            case 'Ubuntu':
            case 'Amazon Linux':
                return "#!/bin/bash\necho 'ec2-user:$password' | chpasswd\n";
            default:
                return null;
        }
    }
}
