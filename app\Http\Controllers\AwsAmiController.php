<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AwsAmiController extends Controller
{
    /**
     * 获取所有AWS AMI数据（优先使用硬编码数据，避免频繁API调用）
     *
     * @return JsonResponse
     */
    public function getAllAmiData(): JsonResponse
    {
        try {
            // 使用缓存提高响应速度（缓存1小时）
            $amiData = Cache::remember('aws_ami_data', 3600, function () {
                // 优先使用硬编码数据（基于AMI.txt文件）
                Log::info('使用硬编码AMI数据（推荐方式）');
                return $this->getHardcodedAmiData();
            });

            return response()->json([
                'success' => true,
                'data' => $amiData,
                'message' => '获取AMI数据成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取AMI数据失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '获取AMI数据失败: ' . $e->getMessage(),
                'data' => $this->getFallbackAmiData()
            ], 500);
        }
    }

    /**
     * 获取硬编码的AMI数据（固化数据，不依赖外部文件）
     *
     * @return array
     */
    private function getHardcodedAmiData(): array
    {
        $regions = $this->getFixedRegionsData();

        $regionSystemSupport = [];
        foreach ($regions as $regionCode => $regionData) {
            $regionSystemSupport[$regionCode] = array_keys($regionData['systems']);
        }

        return [
            'regions' => $regions,
            'region_system_support' => $regionSystemSupport,
            'system_icons' => $this->getSystemIcons(),
            'last_updated' => now()->toISOString(),
            'data_source' => 'fixed_hardcoded'
        ];
    }

    /**
     * 获取固化的地区数据（从分区域配置文件加载）
     *
     * @return array
     */
    private function getFixedRegionsData(): array
    {
        try {
            // 合并所有地区配置文件
            $allRegions = [];

            // 加载各个地区的配置文件
            $regionConfigs = [
                'aws_ami_americas',        // 美洲地区
                'aws_ami_asia_pacific_1',  // 亚太地区1
                'aws_ami_asia_pacific_2',  // 亚太地区2
                'aws_ami_europe',          // 欧洲地区
                'aws_ami_middle_east',     // 中东地区
                'aws_ami_africa'           // 非洲地区
            ];

            foreach ($regionConfigs as $configName) {
                try {
                    $regionData = config($configName, []);
                    if (!empty($regionData) && is_array($regionData)) {
                        $allRegions = array_merge($allRegions, $regionData);
                        Log::info("成功加载配置文件: {$configName}, 地区数: " . count($regionData));
                    } else {
                        Log::warning("配置文件为空或格式错误: {$configName}");
                    }
                } catch (\Exception $e) {
                    Log::error("加载配置文件失败: {$configName}, 错误: " . $e->getMessage());
                }
            }

            // 如果分区域文件都不存在，则使用原始配置文件
            if (empty($allRegions)) {
                Log::warning("所有分区域配置文件都为空，尝试使用原始配置文件");
                $allRegions = config('aws_ami_data', []);

                // 如果原始配置文件也为空，使用硬编码的备用数据
                if (empty($allRegions)) {
                    Log::warning("原始配置文件也为空，使用硬编码备用数据");
                    $allRegions = $this->getHardcodedBackupData();
                }
            }

            Log::info("最终加载的地区数量: " . count($allRegions));
            return $allRegions;

        } catch (\Exception $e) {
            Log::error("获取地区数据失败: " . $e->getMessage());
            return $this->getHardcodedBackupData();
        }
    }

    /**
     * 获取硬编码的备用数据
     *
     * @return array
     */
    private function getHardcodedBackupData(): array
    {
        return [
            'us-east-1' => [
                'name' => '美国东部（弗吉尼亚北部）',
                'code' => 'us-east-1',
                'systems' => [
                    'Amazon Linux' => [
                        'name' => 'Amazon Linux',
                        'versions' => [
                            ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2023'],
                            ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a634ae95e11c6f91', 'display_name' => 'Amazon Linux 2']
                        ]
                    ],
                    'Ubuntu' => [
                        'name' => 'Ubuntu',
                        'versions' => [
                            ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 22.04 LTS'],
                            ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0a634ae95e11c6f91', 'display_name' => 'Ubuntu 20.04 LTS']
                        ]
                    ],
                    'Windows Server' => [
                        'name' => 'Windows Server',
                        'versions' => [
                            ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                            ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                        ]
                    ]
                ]
            ],
            'us-east-2' => [
                'name' => '美国东部（俄亥俄）',
                'code' => 'us-east-2',
                'systems' => [
                    'Amazon Linux' => [
                        'name' => 'Amazon Linux',
                        'versions' => [
                            ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Amazon Linux 2023'],
                            ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Amazon Linux 2']
                        ]
                    ],
                    'Ubuntu' => [
                        'name' => 'Ubuntu',
                        'versions' => [
                            ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Ubuntu 22.04 LTS'],
                            ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Ubuntu 20.04 LTS']
                        ]
                    ],
                    'Windows Server' => [
                        'name' => 'Windows Server',
                        'versions' => [
                            ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                            ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                        ]
                    ]
                ]
            ],
            'us-west-2' => [
                'name' => '美国西部（俄勒冈）',
                'code' => 'us-west-2',
                'systems' => [
                    'Amazon Linux' => [
                        'name' => 'Amazon Linux',
                        'versions' => [
                            ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Amazon Linux 2023'],
                            ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0892d3c7ee96c0bf7', 'display_name' => 'Amazon Linux 2']
                        ]
                    ],
                    'Ubuntu' => [
                        'name' => 'Ubuntu',
                        'versions' => [
                            ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 22.04 LTS'],
                            ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0892d3c7ee96c0bf7', 'display_name' => 'Ubuntu 20.04 LTS']
                        ]
                    ],
                    'Windows Server' => [
                        'name' => 'Windows Server',
                        'versions' => [
                            ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                            ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取系统模板数据
     *
     * @return array
     */
    private function getSystemTemplate(): array
    {
        return [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => '', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => '', 'display_name' => 'Amazon Linux 2']
                ],
                'default_ami' => ''
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => '', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => '', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => '', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => '', 'display_name' => 'Ubuntu 18.04 LTS']
                ],
                'default_ami' => ''
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => '', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => '', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => '', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => '', 'display_name' => 'Windows Server 2016']
                ],
                'default_ami' => ''
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => '', 'display_name' => 'Red Hat Enterprise Linux 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => '', 'display_name' => 'Red Hat Enterprise Linux 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => '', 'display_name' => 'Red Hat Enterprise Linux 7.9']
                ],
                'default_ami' => ''
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => '', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => '', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => '', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ],
                'default_ami' => ''
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => '', 'display_name' => 'Debian 12 (Bookworm)'],
                    ['name' => 'Debian 11', 'ami_id' => '', 'display_name' => 'Debian 11 (Bullseye)']
                ],
                'default_ami' => ''
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => '', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => '', 'display_name' => 'CentOS Stream 8']
                ],
                'default_ami' => ''
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => '', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => '', 'display_name' => 'Oracle Linux 8']
                ],
                'default_ami' => ''
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => '', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => '', 'display_name' => 'Rocky Linux 8']
                ],
                'default_ami' => ''
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => '', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => '', 'display_name' => 'AlmaLinux 8']
                ],
                'default_ami' => ''
            ]
        ];
    }

    /**
     * 生成地区系统数据
     *
     * @param string $amiPrefix
     * @return array
     */
    private function generateRegionSystems(string $amiPrefix): array
    {
        $template = $this->getSystemTemplate();
        $systems = [];
        $counter = 0;

        foreach ($template as $osName => $osData) {
            $systems[$osName] = [
                'name' => $osData['name'],
                'versions' => [],
                'default_ami' => $amiPrefix . sprintf('%02x', $counter)
            ];

            foreach ($osData['versions'] as $version) {
                $systems[$osName]['versions'][] = [
                    'name' => $version['name'],
                    'ami_id' => $amiPrefix . sprintf('%02x', $counter),
                    'display_name' => $version['display_name']
                ];
                $counter++;
            }
        }

        return $systems;
    }

    /**
     * 获取所有地区的完整数据（24个地区）
     *
     * @return array
     */
    private function getAllRegionsData(): array
    {
        $regions = [
            // 美国地区
            ['code' => 'us-east-1', 'name' => '美国东部（弗吉尼亚北部）', 'prefix' => 'ami-0a'],
            ['code' => 'us-east-2', 'name' => '美国东部（俄亥俄）', 'prefix' => 'ami-0b'],
            ['code' => 'us-west-1', 'name' => '美国西部（加利福尼亚北部）', 'prefix' => 'ami-0c'],
            ['code' => 'us-west-2', 'name' => '美国西部（俄勒冈）', 'prefix' => 'ami-0d'],
            
            // 非洲地区
            ['code' => 'af-south-1', 'name' => '非洲（开普敦）', 'prefix' => 'ami-0e'],
            
            // 亚太地区
            ['code' => 'ap-east-1', 'name' => '亚太地区（香港）', 'prefix' => 'ami-0f'],
            ['code' => 'ap-south-1', 'name' => '亚太地区（孟买）', 'prefix' => 'ami-0g'],
            ['code' => 'ap-northeast-1', 'name' => '亚太地区（东京）', 'prefix' => 'ami-0h'],
            ['code' => 'ap-northeast-2', 'name' => '亚太地区（首尔）', 'prefix' => 'ami-0i'],
            ['code' => 'ap-northeast-3', 'name' => '亚太地区（大阪）', 'prefix' => 'ami-0j'],
            ['code' => 'ap-southeast-1', 'name' => '亚太地区（新加坡）', 'prefix' => 'ami-0k'],
            ['code' => 'ap-southeast-2', 'name' => '亚太地区（悉尼）', 'prefix' => 'ami-0l'],
            ['code' => 'ap-southeast-3', 'name' => '亚太地区（雅加达）', 'prefix' => 'ami-0m'],
            ['code' => 'ap-south-2', 'name' => '亚太地区（海德拉巴）', 'prefix' => 'ami-0n'],
            ['code' => 'ap-southeast-4', 'name' => '亚太地区（墨尔本）', 'prefix' => 'ami-0o'],
            
            // 加拿大地区
            ['code' => 'ca-central-1', 'name' => '加拿大（中部）', 'prefix' => 'ami-0p'],
            
            // 欧洲地区
            ['code' => 'eu-central-1', 'name' => '欧洲（法兰克福）', 'prefix' => 'ami-0q'],
            ['code' => 'eu-west-1', 'name' => '欧洲（爱尔兰）', 'prefix' => 'ami-0r'],
            ['code' => 'eu-west-2', 'name' => '欧洲（伦敦）', 'prefix' => 'ami-0s'],
            ['code' => 'eu-west-3', 'name' => '欧洲（巴黎）', 'prefix' => 'ami-0t'],
            ['code' => 'eu-north-1', 'name' => '欧洲（斯德哥尔摩）', 'prefix' => 'ami-0u'],
            
            // 中东地区
            ['code' => 'me-south-1', 'name' => '中东（巴林）', 'prefix' => 'ami-0v'],
            ['code' => 'me-central-1', 'name' => '中东（阿联酋）', 'prefix' => 'ami-0w'],
            
            // 南美洲地区
            ['code' => 'sa-east-1', 'name' => '南美洲（圣保罗）', 'prefix' => 'ami-0x']
        ];

        $result = [];
        foreach ($regions as $region) {
            $result[$region['code']] = [
                'name' => $region['name'],
                'code' => $region['code'],
                'systems' => $this->generateRegionSystems($region['prefix'])
            ];
        }

        return $result;
    }

    /**
     * 获取系统图标映射（本地化图标）
     *
     * @return array
     */
    private function getSystemIcons(): array
    {
        return [
            'Amazon Linux' => [
                'type' => 'local',
                'path' => '/images/os-icons/amazon-linux.png',
                'fallback' => 'fab fa-aws'
            ],
            'Ubuntu' => [
                'type' => 'local',
                'path' => '/images/os-icons/ubuntu.png',
                'fallback' => 'fab fa-ubuntu'
            ],
            'Windows Server' => [
                'type' => 'local',
                'path' => '/images/os-icons/windows.png',
                'fallback' => 'fab fa-windows'
            ],
            'Red Hat' => [
                'type' => 'local',
                'path' => '/images/os-icons/redhat.png',
                'fallback' => 'fab fa-redhat'
            ],
            'SUSE Linux' => [
                'type' => 'local',
                'path' => '/images/os-icons/suse.png',
                'fallback' => 'fab fa-suse'
            ],
            'Debian' => [
                'type' => 'local',
                'path' => '/images/os-icons/debian.png',
                'fallback' => 'fab fa-debian'
            ],
            'CentOS' => [
                'type' => 'local',
                'path' => '/images/os-icons/centos.png',
                'fallback' => 'fab fa-centos'
            ],
            'Oracle Linux' => [
                'type' => 'local',
                'path' => '/images/os-icons/oracle.png',
                'fallback' => 'fas fa-database'
            ],
            'Rocky Linux' => [
                'type' => 'local',
                'path' => '/images/os-icons/rocky.png',
                'fallback' => 'fas fa-mountain'
            ],
            'AlmaLinux' => [
                'type' => 'local',
                'path' => '/images/os-icons/almalinux.png',
                'fallback' => 'fab fa-linux'
            ]
        ];
    }

    /**
     * 格式化真实AMI数据为前端需要的格式
     *
     * @param array $realData
     * @return array
     */
    private function formatRealAmiData(array $realData): array
    {
        $formattedData = [
            'regions' => [],
            'region_system_support' => [],
            'system_icons' => $this->getSystemIcons(),
            'last_updated' => now()->toISOString(),
            'data_source' => 'real_api'
        ];

        foreach ($realData as $regionCode => $regionInfo) {
            $formattedData['regions'][$regionCode] = [
                'name' => $regionInfo['name'],
                'code' => $regionCode,
                'systems' => $regionInfo['systems']
            ];

            // 构建地区系统支持映射
            $formattedData['region_system_support'][$regionCode] = array_keys($regionInfo['systems']);
        }

        return $formattedData;
    }

    /**
     * 获取备用AMI数据（当解析失败时使用）
     *
     * @return array
     */
    private function getFallbackAmiData(): array
    {
        return [
            'regions' => [
                'us-east-1' => [
                    'name' => '美国东部（弗吉尼亚北部）',
                    'code' => 'us-east-1',
                    'systems' => [
                        'Amazon Linux' => [
                            'name' => 'Amazon Linux',
                            'versions' => [
                                ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2023']
                            ],
                            'default_ami' => 'ami-080e1f13689e07408'
                        ]
                    ]
                ]
            ],
            'region_system_support' => [
                'us-east-1' => ['Amazon Linux']
            ],
            'system_icons' => $this->getSystemIcons(),
            'last_updated' => now()->toISOString(),
            'data_source' => 'fallback'
        ];
    }

    /**
     * 清除AMI数据缓存
     *
     * @return JsonResponse
     */
    public function clearCache(): JsonResponse
    {
        try {
            Cache::forget('aws_ami_data');
            Cache::forget('aws_ami_real_data');

            return response()->json([
                'success' => true,
                'message' => 'AMI数据缓存已清除'
            ]);
        } catch (\Exception $e) {
            Log::error('清除AMI缓存失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '清除缓存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 手动刷新缓存（重新加载固化数据）
     *
     * @return JsonResponse
     */
    public function refreshData(): JsonResponse
    {
        try {
            // 清除现有缓存，强制重新加载固化数据
            Cache::forget('aws_ami_data');

            return response()->json([
                'success' => true,
                'message' => '数据已刷新，下次请求将重新加载'
            ]);
        } catch (\Exception $e) {
            Log::error('刷新数据失败: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '刷新失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
