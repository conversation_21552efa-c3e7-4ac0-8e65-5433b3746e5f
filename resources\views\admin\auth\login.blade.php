<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ \App\Models\Setting::get('login_title', config('app.name')) }} - 管理员登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #5156be;
            --primary-light: rgba(81,86,190,.1);
            --text-color: #495057;
        }
        
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-light) 0%, #fff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
        }
        
        .login-card {
            width: 100%;
            max-width: 400px;
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,.05);
            padding: 2rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header .logo {
            width: 64px;
            height: 64px;
            background: var(--primary-light);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .login-header .logo i {
            font-size: 2rem;
            color: var(--primary-color);
        }
        
        .login-header h1 {
            font-size: 1.5rem;
            color: var(--text-color);
            margin: 0;
            font-weight: 600;
        }
        
        .login-header p {
            color: #74788d;
            margin: 0.5rem 0 0;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-floating > .form-control {
            padding: 1rem 0.75rem;
            height: calc(3.5rem + 2px);
            line-height: 1.25;
        }
        
        .form-floating > label {
            padding: 1rem 0.75rem;
        }
        
        .form-check {
            padding-left: 1.75rem;
            margin-bottom: 1rem;
        }
        
        .form-check-input {
            width: 1.1em;
            height: 1.1em;
            margin-left: -1.75rem;
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-login {
            padding: 0.75rem 1rem;
            font-weight: 500;
            width: 100%;
            background: var(--primary-color);
            border: none;
            border-radius: 0.5rem;
        }
        
        .btn-login:hover {
            background: #4347a5;
        }
        
        .invalid-feedback {
            font-size: 0.875em;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .alert-danger {
            background-color: rgba(240,101,72,.1);
            color: #f06548;
        }
        
        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 5;
            cursor: pointer;
            color: #74788d;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            padding: 0;
        }
        
        .password-toggle:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <div class="logo">
                <i class="bi bi-cloud"></i>
            </div>
            <h1>{{ \App\Models\Setting::get('login_title', config('app.name')) }}</h1>
            <p>管理员登录</p>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-circle me-2"></i>
                {{ $errors->first() }}
            </div>
        @endif

        <form method="POST" action="{{ route('admin.login') }}">
            @csrf
            <div class="form-floating">
                <input type="text" 
                       class="form-control @error('username') is-invalid @enderror" 
                       id="username" 
                       name="username" 
                       placeholder="用户名"
                       value="{{ old('username', $remembered_username ?? '') }}"
                       required 
                       autofocus>
                <label for="username">用户名</label>
                @error('username')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-floating">
                <input type="password" 
                       class="form-control @error('password') is-invalid @enderror" 
                       id="password" 
                       name="password" 
                       placeholder="密码"
                       required>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="bi bi-eye-slash" id="togglePassword"></i>
                </button>
                <label for="password">密码</label>
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                </div>

            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                <label class="form-check-label" for="remember">记住用户名</label>
                </div>

            <button type="submit" class="btn btn-primary btn-login">
                <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </button>
            </form>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const usernameInput = document.getElementById('username');
        const rememberCheckbox = document.getElementById('remember');
        
        // 从localStorage读取状态
        const remembered = localStorage.getItem('remember_username') === 'true';
        rememberCheckbox.checked = remembered;
        
        // 如果之前选择了记住用户名，则填充用户名
        if (remembered) {
            const savedUsername = localStorage.getItem('saved_username');
            if (savedUsername) {
                usernameInput.value = savedUsername;
            }
        }
        
        // 监听复选框变化
        rememberCheckbox.addEventListener('change', function() {
            if (this.checked) {
                localStorage.setItem('remember_username', 'true');
                // 立即保存当前输入框中的用户名
                if (usernameInput.value) {
                    localStorage.setItem('saved_username', usernameInput.value);
                }
            } else {
                localStorage.removeItem('remember_username');
                localStorage.removeItem('saved_username');
            }
        });
        
        // 监听表单提交
        document.querySelector('form').addEventListener('submit', function() {
            if (rememberCheckbox.checked && usernameInput.value) {
                localStorage.setItem('saved_username', usernameInput.value);
            }
        });
        
        // 监听用户名输入变化
        usernameInput.addEventListener('input', function() {
            if (rememberCheckbox.checked) {
                localStorage.setItem('saved_username', this.value);
            }
        });
    });

    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('togglePassword');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.replace('bi-eye-slash', 'bi-eye');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.replace('bi-eye', 'bi-eye-slash');
        }
    }
    </script>
</body>
</html> 