<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Carbon\Carbon;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'expires_at',
        'last_login_at',
        'proxy_mode',
        'proxy_type',
        'proxy_host',
        'proxy_port',
        'proxy_username',
        'proxy_password',
        'proxy_status',
        'proxy_last_test',
        'proxy_error_message',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'expires_at' => 'datetime',
        'last_login_at' => 'datetime',
        'proxy_last_test' => 'datetime',
    ];

    public function awsAccounts()
    {
        return $this->hasMany(AwsAccount::class);
    }

    public function activationCode()
    {
        return $this->belongsTo(ActivationCode::class, 'activation_code', 'code');
    }

    /**
     * 获取精确的剩余天数（用于计算）
     */
    public function getRemainingDaysAttribute()
    {
        if (!$this->expires_at) {
            return 0;
        }

        $now = Carbon::now();
        $expiresAt = Carbon::parse($this->expires_at);

        if ($now->gt($expiresAt)) {
            return 0;
        }

        return $now->floatDiffInDays($expiresAt);
    }

    /**
     * 获取格式化的剩余时间显示
     */
    public function getFormattedRemainingTimeAttribute()
    {
        $remainingDays = $this->remaining_days;
        
        if ($remainingDays <= 0) {
            return '已过期';
        }
        
        if ($remainingDays >= 1) {
            // 如果剩余天数大于等于1
            if ($remainingDays >= 1.5) {
                return ceil($remainingDays) . '天';
            } else {
                return floor($remainingDays) . '天';
            }
        } else {
            // 如果剩余不足1天，转换为小时显示
            $remainingHours = $remainingDays * 24;
            return ceil($remainingHours) . '小时';
        }
    }

    public function getExpirationStatusAttribute()
    {
        if (!$this->expires_at) {
            return null;
        }

        return $this->expires_at->lt(Carbon::now()) ? '已过期' : $this->formatted_remaining_time;
    }

    public function isExpired()
    {
        return $this->expires_at && Carbon::parse($this->expires_at)->isPast();
    }
}
