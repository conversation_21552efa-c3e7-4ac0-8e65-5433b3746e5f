# 操作系统图标下载说明

本目录包含了以下操作系统的图标：
- amazon-linux.png - Amazon Linux 图标
- ubuntu.png - Ubuntu 图标  
- windows.png - Windows Server 图标

## 需要下载的图标：
- redhat.png - Red Hat 图标
- suse.png - SUSE Linux 图标
- debian.png - Debian 图标
- centos.png - CentOS 图标
- oracle.png - Oracle Linux 图标
- rocky.png - Rocky Linux 图标
- almalinux.png - AlmaLinux 图标

## 下载方法：

### 方法一：使用HTML下载页面
1. 打开 `download_icons.html` 文件（在浏览器中打开）
2. 点击每个图标下方的"下载SVG"链接，下载SVG格式的图标
3. 使用图像编辑软件（如GIMP、Photoshop或在线工具）将SVG转换为PNG格式
4. 调整图像大小为64x64像素
5. 确保背景是透明的
6. 将处理后的PNG图像保存为对应的文件名（如redhat.png、suse.png等）

### 方法二：使用在线图标库
1. 访问以下网站获取官方图标：
   - Red Hat: https://www.redhat.com/en/about/brand/standards/logo
   - SUSE: https://brand.suse.com/suse-logo
   - Debian: https://www.debian.org/logos/
   - CentOS: https://wiki.centos.org/ArtWork/Brand/Logo
   - Oracle Linux: https://www.oracle.com/legal/logos.html
   - Rocky Linux: https://github.com/rocky-linux/branding
   - AlmaLinux: https://almalinux.org/p/the-almalinux-os-trademark-usage-policy/
2. 下载官方图标
3. 使用图像编辑软件处理为所需格式

## 图标要求：
- 格式：PNG
- 尺寸：64x64 像素
- 背景：透明
- 风格：官方标准图标

## 图标来源：
所有图标均来自各操作系统的官方网站或维基共享资源，确保使用权限合规。

## 文件说明：
- README.md - 本说明文件
- download_icons.html - HTML下载页面，包含所有需要的图标链接
- download_os_icons.sh - Linux/Mac用户可用的下载脚本（需要安装curl和ImageMagick） 