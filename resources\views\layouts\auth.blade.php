@extends('layouts.app')

@push('styles')
<!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        /* 自定义样式 */
    .min-h-screen {
        min-height: calc(100vh - 56px); /* 减去导航栏的高度 */
        padding-top: 1rem;
    }
    
    .bg-gray-50 {
        background-color: #F9FAFB;
    }
    
    .shadow-lg {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    
    .focus\:ring-blue-500:focus {
        --tw-ring-opacity: 1;
        --tw-ring-color: rgba(59, 130, 246, var(--tw-ring-opacity));
        box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    }
    
    .focus\:border-blue-500:focus {
        --tw-border-opacity: 1;
        border-color: rgba(59, 130, 246, var(--tw-border-opacity));
    }
    
    .hover\:bg-blue-700:hover {
        --tw-bg-opacity: 1;
        background-color: rgba(29, 78, 216, var(--tw-bg-opacity));
    }
    
    .bg-blue-600 {
        --tw-bg-opacity: 1;
        background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
    }
    
    /* 文本样式 */
    .text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    
    .text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }
    
    .font-medium {
        font-weight: 500;
    }
    
    .font-bold {
        font-weight: 700;
    }
    
    .text-gray-600 {
        color: #4B5563;
    }
    
    .text-gray-700 {
        color: #374151;
    }
    
    /* 间距和布局 */
    .space-y-6 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
    }
    
    .px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }
    
    .mb-8 {
        margin-bottom: 2rem;
    }
    
    .mb-2 {
        margin-bottom: 0.5rem;
    }
    
    .mt-1 {
        margin-top: 0.25rem;
    }
    
    /* 表单样式 */
    .block {
        display: block;
    }
    
    .w-full {
        width: 100%;
    }
    
    .border {
        border-width: 1px;
    }
    
    .border-gray-300 {
        border-color: #D1D5DB;
    }
    
    .rounded-md {
        border-radius: 0.375rem;
    }
    
    .rounded-lg {
        border-radius: 0.5rem;
    }
    
    /* 错误提示样式 */
    .text-red-600 {
        --tw-text-opacity: 1;
        color: rgba(220, 38, 38, var(--tw-text-opacity));
    }
    
    .bg-red-100 {
        --tw-bg-opacity: 1;
        background-color: rgba(254, 226, 226, var(--tw-bg-opacity));
    }
    
    .border-red-400 {
        --tw-border-opacity: 1;
        border-color: rgba(248, 113, 113, var(--tw-border-opacity));
    }
    
    .border-red-500 {
        --tw-border-opacity: 1;
        border-color: rgba(239, 68, 68, var(--tw-border-opacity));
    }
    
    /* 成功提示样式 */
    .bg-green-100 {
        --tw-bg-opacity: 1;
        background-color: rgba(220, 252, 231, var(--tw-bg-opacity));
    }
    
    .border-green-400 {
        --tw-border-opacity: 1;
        border-color: rgba(74, 222, 128, var(--tw-border-opacity));
    }
    
    .text-green-700 {
        --tw-text-opacity: 1;
        color: rgba(21, 128, 61, var(--tw-text-opacity));
    }
    
    /* Flex 布局 */
    .flex {
        display: flex;
    }
    
    .items-center {
        align-items: center;
    }
    
    .justify-center {
        justify-content: center;
    }
    
    .justify-between {
        justify-content: space-between;
    }
    
    /* 链接样式 */
    .text-blue-600 {
        --tw-text-opacity: 1;
        color: rgba(37, 99, 235, var(--tw-text-opacity));
    }
    
    .hover\:text-blue-500:hover {
        --tw-text-opacity: 1;
        color: rgba(59, 130, 246, var(--tw-text-opacity));
    }
    
    /* 按钮样式 */
    .focus\:outline-none:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
    }
    
    .focus\:ring-2:focus {
        --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
        --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
        box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    }
    
    .focus\:ring-offset-2:focus {
        --tw-ring-offset-width: 2px;
    }
    
    /* 输入框聚焦效果 */
    input:focus {
        outline: none !important;
        border-color: #3B82F6 !important;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
    }
</style>
@endpush

@push('scripts')
<script>
    // 确保导航栏正确初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化 Bootstrap 导航栏
        var navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            navbarToggler.addEventListener('click', function() {
                var target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    target.classList.toggle('show');
                }
            });
        }
    });
</script>
@endpush

@section('content')
    @yield('content')
@endsection 