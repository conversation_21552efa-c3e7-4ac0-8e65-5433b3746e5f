<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            // 为账户统计查询添加复合索引
            $table->index(['user_id', 'created_at'], 'idx_aws_accounts_user_created');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->dropIndex('idx_aws_accounts_user_created');
        });
    }
};
