@extends('layouts.guest')

@section('content')
<h1>{{ \App\Models\Setting::get('login_title', config('app.name')) }}</h1>
<p>会员续费</p>

@if(session('success'))
    <div class="alert alert-success mb-4">
        {{ session('success') }}
    </div>
@endif

@if($errors->any())
    <div class="alert alert-danger mb-4">
        <ul class="list-unstyled mb-0">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<form method="POST" action="{{ route('renewal.renew') }}" class="space-y-4">
    @csrf
    
    <div class="mb-3">
        <label for="username" class="form-label">用户名</label>
        <input id="username" type="text" name="username" value="{{ old('username') }}" 
            class="form-control @error('username') is-invalid @enderror" required autofocus>
        @error('username')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="activation_code" class="form-label">激活码</label>
        <input id="activation_code" type="text" name="activation_code" value="{{ old('activation_code') }}" 
            class="form-control @error('activation_code') is-invalid @enderror" required>
        @error('activation_code')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3 d-flex justify-content-between align-items-center">
        <div class="form-check">
        </div>
        <div>
            <a href="{{ route('login') }}" class="text-decoration-none">续费成功？立即登录</a>
        </div>
    </div>

    <button type="submit" class="btn btn-primary w-100">
        立即续费
    </button>
</form>
@endsection 