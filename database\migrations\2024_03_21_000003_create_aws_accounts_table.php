<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('aws_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('account_name');
            $table->string('email_password');
            $table->string('aws_password');
            $table->string('access_key');
            $table->string('secret_key');
            $table->tinyInteger('status')->default(0);
            $table->text('quota')->nullable();
            $table->timestamp('last_check_at')->nullable();
            $table->text('remarks')->nullable();
            $table->timestamps();

            // 添加索引
            $table->index('status');
            $table->index('last_check_at');
            $table->index('created_at');
            $table->unique(['user_id', 'account_name']);
            $table->unique(['user_id', 'access_key']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('aws_accounts');
    }
}; 