<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AwsAccount;
use Aws\Sdk;
use Aws\Iam\IamClient;
use Aws\Exception\AwsException;
use Exception;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\ActivityLog;
use App\Services\AwsService;

class AwsIamController extends Controller
{
    /**
     * 应用用户代理配置到AWS SDK配置
     */
    private function applyProxyConfig(&$config, $user = null)
    {
        if (!$user) {
            \Log::info("IAM AWS SDK请求模式: 本地模式 (无用户认证)");
            return;
        }

        // 如果用户选择了代理模式，必须检查代理状态
        if ($user->proxy_mode === 'proxy') {
            // 检查代理状态是否异常
            if ($user->proxy_status !== 'active') {
                \Log::error("IAM代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'proxy_status' => $user->proxy_status,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('当前代理模式IP异常，请切换其他模式进行操作');
            }

            // 检查代理配置是否完整
            if (!$user->proxy_host || !$user->proxy_port) {
                \Log::error("IAM代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('代理配置不完整，请重新配置代理或切换到本地模式');
            }

            // 构建代理URL
            $proxyUrl = $this->buildProxyUrl(
                $user->proxy_type,
                $user->proxy_host,
                $user->proxy_port,
                $user->proxy_username,
                $user->proxy_password
            );

            // 应用代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("IAM AWS SDK请求模式: 代理模式", [
                'user_id' => $user->id,
                'proxy_type' => $user->proxy_type,
                'proxy_host' => $user->proxy_host,
                'proxy_port' => $user->proxy_port,
                'proxy_username' => $user->proxy_username,
                'proxy_status' => $user->proxy_status,
                'actual_proxy_ip' => $user->proxy_host, // 实际使用的代理IP
                'proxy_url_format' => $user->proxy_type . '://' . $user->proxy_host . ':' . $user->proxy_port
            ]);
        } elseif ($user->proxy_mode === 'free_proxy') {
            // 免费代理模式
            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');

            if ($freeProxyStatus !== 'active') {
                \Log::error("IAM免费代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'free_proxy_status' => $freeProxyStatus
                ]);

                throw new \Exception('当前免费代理模式IP异常，请切换其他模式进行操作');
            }

            // 获取免费代理配置
            $freeProxyType = \App\Models\Setting::get('free_proxy_type', 'http');
            $freeProxyHost = \App\Models\Setting::get('free_proxy_host', '');
            $freeProxyPort = \App\Models\Setting::get('free_proxy_port', '');
            $freeProxyUsername = \App\Models\Setting::get('free_proxy_username', '');
            $freeProxyPassword = \App\Models\Setting::get('free_proxy_password', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                \Log::error("IAM免费代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id
                    // 敏感信息已移除：不记录具体的配置信息
                ]);

                throw new \Exception('免费代理配置不完整，请联系管理员');
            }

            // 构建免费代理URL
            $proxyUrl = $this->buildProxyUrl(
                $freeProxyType,
                $freeProxyHost,
                $freeProxyPort,
                $freeProxyUsername,
                $freeProxyPassword
            );

            // 应用免费代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("IAM AWS SDK请求模式: 免费代理模式", [
                'user_id' => $user->id,
                'free_proxy_status' => $freeProxyStatus
                // 敏感信息已移除：不记录IP、端口、用户名等
            ]);
        } else {
            \Log::info("IAM AWS SDK请求模式: 本地模式", [
                'user_id' => $user->id,
                'proxy_mode' => $user->proxy_mode,
                'proxy_status' => $user->proxy_status ?? 'inactive'
            ]);
        }
    }

    /**
     * 构建代理URL
     */
    private function buildProxyUrl($type, $host, $port, $username = null, $password = null)
    {
        $proxyUrl = $type . '://';

        if ($username && $password) {
            $proxyUrl .= urlencode($username) . ':' . urlencode($password) . '@';
        }

        $proxyUrl .= $host . ':' . $port;

        return $proxyUrl;
    }

    /**
     * 检查错误是否为代理相关错误
     */
    private function isProxyRelatedError($errorMessage)
    {
        $proxyErrorIndicators = [
            '407', 'Proxy Authentication Required',
            '502', 'Bad Gateway',
            '503', 'Service Unavailable',
            '504', 'Gateway Timeout',
            'CONNECT tunnel failed',
            'Proxy CONNECT aborted',
            'cURL error 7', 'cURL error 28', 'cURL error 35',
            'cURL error 52', 'cURL error 56',
            'Empty reply from server',
            'Connection refused',
            'Operation timed out'
        ];

        foreach ($proxyErrorIndicators as $indicator) {
            if (strpos($errorMessage, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }
    /**
     * 显示 IAM 账户开通页面
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100, 200, 500, 1000]) ? $perPage : 10;
        
        $query = AwsAccount::query();
        
        // 只显示当前用户的账户
        $query->where('user_id', auth()->id());

        // 搜索条件
        if ($request->filled('search')) {
            $query->where('account_name', 'like', '%' . $request->search . '%');
        }

        // 时间范围
        if ($request->filled('time_range')) {
            switch ($request->time_range) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', Carbon::yesterday());
                    break;
                case 'custom':
                    if ($request->filled('start_date') && $request->filled('end_date')) {
                        $query->whereBetween('created_at', [
                            Carbon::parse($request->start_date)->startOfDay(),
                            Carbon::parse($request->end_date)->endOfDay()
                        ]);
                    }
                    break;
            }
        }

        // 状态筛选
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', (int)$request->status);
        }

        // 添加默认降序排序
        $query->orderBy('id', 'desc');

        $accounts = $query->paginate($perPage);
        
        // 保持筛选条件
        $accounts->appends($request->only(['search', 'time_range', 'start_date', 'end_date', 'status', 'per_page']));

        return view('aws-iam.index', compact('accounts', 'perPage'));
    }

    /**
     * 创建 IAM 用户
     */
    public function create(Request $request)
    {
        try {
            // 验证请求数据
            $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'required|exists:aws_accounts,id',
                'username' => 'required|string|min:4|regex:/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/',
                'password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/'
            ], [
                'username.regex' => '用户名必须至少4位，且包含英文和数字',
                'password.regex' => '密码必须至少8位，且包含大小写字母和数字'
            ]);

            $ids = $request->input('ids', []);
            $username = $request->input('username');
            $password = $request->input('password');
            $currentTime = now();
            
            $successCount = 0;
            $failedCount = 0;
            $results = [];

            // 创建AWS Service实例
            $awsService = app(\App\Services\AwsService::class);

            foreach ($ids as $id) {
                $account = AwsAccount::find($id);
                $result = ['id' => $id, 'status' => 'failed'];

                if (!$account) {
                    Log::warning('账户不存在', ['account_id' => $id]);
                    $failedCount++;
                    $results[] = $result;
                    continue;
                }

                try {
                    // 1. 首先检查账户状态是否为2或3
                    if (in_array($account->status, [2, 3])) {
                        Log::info("账户状态异常(status={$account->status})，跳过IAM创建");
                        $failedCount++;
                        $account->update([
                            'status' => $account->status,  // 保持原有状态
                            'last_check_at' => $currentTime,
                            'remarks' => '账户状态异常，无法创建IAM'
                        ]);
                        $result['status'] = 'failed';
                        $result['account_status'] = $account->status;
                        $result['id'] = $account->id;  // 添加账户ID
                        $results[] = $result;
                        continue;
                    }

                    // 2. 如果账户状态为0或1，先进行测号
                    if (in_array($account->status, [0, 1])) {
                        $checkResult = $awsService->checkAccount($account);
                        Log::info("账户测试结果: " . json_encode($checkResult, JSON_UNESCAPED_UNICODE));
                        
                        // 更新账户状态和相关信息
                        $updateData = [
                            'status' => $checkResult['status'],  // 更新为测试返回的状态
                            'last_check_at' => $currentTime
                        ];
                        
                        // 如果有错误信息，保存到备注
                        if (isset($checkResult['error'])) {
                            $updateData['remarks'] = $checkResult['error'];
                        }
                        
                        $account->update($updateData);
                        
                        // 3. 如果测号结果不是正常(status=1)，则停止创建
                        if ($checkResult['status'] !== 1) {
                            Log::info("账户测试未通过，跳过IAM创建");
                            $failedCount++;
                            $result['status'] = 'failed';
                            $result['account_status'] = $checkResult['status'];
                            $result['id'] = $account->id;  // 添加账户ID
                            $results[] = $result;
                            continue;
                        }
                    }

                    // 4. 只有状态为1的账户才继续创建IAM
                    Log::info("开始创建IAM用户");

                    // 创建AWS SDK实例
                    $sdkConfig = [
                        'version' => 'latest',
                        'region'  => 'us-east-1',
                        'credentials' => [
                            'key'    => $account->access_key,
                            'secret' => $account->secret_key
                        ],
                        'http' => [
                            'connect_timeout' => 5,
                            'timeout' => 10
                        ]
                    ];

                    // 根据配置决定是否禁用SSL验证
                    if (config('aws.disable_ssl_verification', false)) {
                        $sdkConfig['http']['verify'] = false;
                    }

                    // 应用用户代理配置
                    $this->applyProxyConfig($sdkConfig, auth()->user());

                    $sdk = new Sdk($sdkConfig);
                    $iam = $sdk->createIam();
                    
                    // 激活IAM用户的账单访问权限
                    try {
                        $iam->putUserPolicy([
                            'UserName' => $username,
                            'PolicyName' => 'EnableBillingAccess',
                            'PolicyDocument' => json_encode([
                                'Version' => '2012-10-17',
                                'Statement' => [
                                    [
                                        'Effect' => 'Allow',
                                        'Action' => [
                                            'aws-portal:ViewBilling',
                                            'aws-portal:ModifyBilling',
                                            'aws-portal:ViewAccount',
                                            'aws-portal:ModifyAccount'
                                        ],
                                        'Resource' => '*'
                                    ]
                                ]
                            ])
                        ]);
                    } catch (\Exception $e) {
                        Log::warning('激活账单访问权限失败，但将继续创建用户', [
                            'error' => $e->getMessage()
                        ]);
                    }
                    
                    // 创建IAM用户
                    $createResult = $iam->createUser([
                        'UserName' => $username,
                        'Tags' => [
                            [
                                'Key' => 'AccessType',
                                'Value' => 'IAMUser'
                            ]
                        ]
                    ]);

                    // 为用户创建登录配置文件，启用控制台访问
                    $iam->createLoginProfile([
                        'UserName' => $username,
                        'Password' => $password,
                        'PasswordResetRequired' => false
                    ]);

                    // 附加管理员权限策略
                    $iam->attachUserPolicy([
                        'UserName' => $username,
                        'PolicyArn' => 'arn:aws:iam::aws:policy/AdministratorAccess'
                    ]);

                    // 创建访问密钥
                    $accessKey = $iam->createAccessKey([
                        'UserName' => $username
                    ]);

                    $result['status'] = 'success';
                    $result['account_status'] = $account->status;
                    $result['id'] = $account->id;
                    $result['account_id'] = explode(':', $createResult['User']['Arn'])[4];
                    $result['iam_username'] = $username;
                    $result['iam_password'] = $password;
                    $result['access_key'] = $accessKey['AccessKey']['AccessKeyId'];
                    $result['secret_key'] = $accessKey['AccessKey']['SecretAccessKey'];
                    
                    // 保存IAM账户信息到数据库
                    $account->update([
                        'aws_account_id' => explode(':', $createResult['User']['Arn'])[4],
                        'iam_username' => $username,
                        'iam_password' => $password,
                        'iam_access_key' => $accessKey['AccessKey']['AccessKeyId'],
                        'iam_secret_key' => $accessKey['AccessKey']['SecretAccessKey'],
                        'iam_status' => 'success',
                        'iam_created_at' => now()
                    ]);
                    
                    $successCount++;
                } catch (\Exception $e) {
                    Log::error('IAM创建失败', [
                        'account_id' => $id,
                        'error_message' => $e->getMessage()
                    ]);
                    
                    // 检查是否是代理异常错误
                    if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                           strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                        // 直接抛出代理异常，让外层catch处理
                        throw $e;
                    }
                    
                    $failedCount++;
                    $result['status'] = 'failed';
                    $result['account_status'] = $account->status;
                    $result['id'] = $account->id;

                    // 只有在代理模式下才检查代理相关错误
                    $user = auth()->user();
                    // 检查是否是代理相关错误
                    if ($user && ($user->proxy_mode === 'proxy' || $user->proxy_mode === 'free_proxy') && $this->isProxyRelatedError($e->getMessage())) {
                        if ($user->proxy_mode === 'free_proxy') {
                            $result['error'] = "当前免费代理模式IP异常，请切换其他模式进行操作";
                        } else {
                            $result['error'] = "当前代理模式IP异常，请切换其他模式进行操作";
                        }
                    } else {
                        $result['error'] = $e->getMessage();
                    }
                }

                $results[] = $result;
            }

            return response()->json([
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'results' => $results,
                'message' => sprintf(
                    "处理完成！\n成功：%d\n失败：%d",
                    $successCount,
                    $failedCount
                )
            ]);
        } catch (\Exception $e) {
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json([
                'success' => false,
                'message' => 'IAM创建失败: ' . $e->getMessage()
            ], 500);

        } catch (\Exception $e) {
            Log::error('IAM创建流程失败', [
                'error_message' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function testAccount($account)
    {
        Log::info('==================== 开始测试账户 ====================', [
                        'account_id' => $account->id,
                        'account_name' => $account->account_name,
            'access_key' => $account->access_key,
            'secret_key_prefix' => substr($account->secret_key, 0, 10) . '...',
            'current_status' => $account->status,
            'test_time' => now()->toDateTimeString()
        ]);

        try {
            // 直接使用AwsService的checkAccount方法
            Log::info('调用AwsService.checkAccount方法');
            $awsService = app(AwsService::class);
            $result = $awsService->checkAccount($account);
            
            Log::info('AwsService.checkAccount返回结果', [
                'success' => $result['success'] ?? null,
                'status' => $result['status'] ?? null,
                'error' => $result['error'] ?? null,
                'raw_result' => $result
            ]);

            if (isset($result['status'])) {
                Log::info('账户状态说明', [
                    'status_code' => $result['status'],
                    'status_meaning' => [
                        0 => '未测试',
                        1 => '正常',
                        2 => '封禁',
                        3 => '无效'
                    ][$result['status']] ?? '未知状态'
                ]);
            }
            
            // 返回状态
            Log::info('==================== 测试账户完成 ====================');
            return ['status' => $result['status']];
        } catch (Exception $e) {
            Log::error('账户测试发生异常', [
                'exception_class' => get_class($e),
                'error_message' => $e->getMessage(),
                'error_code' => method_exists($e, 'getCode') ? $e->getCode() : null,
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            Log::info('==================== 测试账户异常结束 ====================');
            return ['status' => 3]; // 发生异常时视为无效账户
        }
    }

    /**
     * 掩码处理字符串
     * @param string $str 原始字符串
     * @param int $showLength 显示长度，默认为8 
     * @return string
     */
    private function maskString($str, $showLength = 8)
    {
        if (empty($str)) {
            return '';
        }
        
        $length = strlen($str);
        if ($length <= $showLength) {
            return $str;
        }
        
        return substr($str, 0, $showLength) . '...';
    }
} 