<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // 注册全局中间件 - 维护模式中间件必须在最前面
        $middleware->prepend(\App\Http\Middleware\CheckMaintenanceMode::class);
        // 暂时注释掉可能导致重定向循环的中间件
        // $middleware->append(\App\Http\Middleware\RedirectIfAdminAuthenticated::class);

        // 注册web中间件组
        $middleware->web(append: [
            \App\Http\Middleware\CheckUserExpiration::class,
        ]);

        // 注册命名中间件
        $middleware->alias([
            'maintenance' => \App\Http\Middleware\CheckMaintenanceMode::class,
            'aws.owner' => \App\Http\Middleware\VerifyAwsAccountOwnership::class,
            'admin.permission' => \App\Http\Middleware\VerifyAdminPermission::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
