@extends('admin.layouts.app')

@push('styles')
<style>
.form-label {
    color: #495057;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
.form-control {
    border-color: #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}
.form-control:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.15rem rgba(81,86,190,.25);
}
.input-group-text {
    border-color: #e9ecef;
    background-color: #f8f9fa;
    color: #74788d;
    border-radius: 0.5rem;
    font-size: 0.875rem;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.btn-soft-primary {
    color: #5156be;
    background-color: rgba(81,86,190,.1);
    border-color: transparent;
    transition: all 0.3s ease;
}
.btn-soft-primary:hover {
    color: #fff;
    background-color: #5156be;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(81,86,190,.2);
}
.btn-soft-secondary {
    color: #74788d;
    background-color: rgba(116,120,141,.1);
    border-color: transparent;
    transition: all 0.3s ease;
}
.btn-soft-secondary:hover {
    color: #fff;
    background-color: #74788d;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(116,120,141,.2);
}
.form-text {
    color: #74788d;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}
.invalid-feedback {
    color: #f06548;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
.card {
    transition: all 0.3s ease;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0,0,0,.12);
}
</style>
@endpush

@section('content')
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0 text-dark">生成激活码</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-secondary">首页</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.activation-codes.index') }}" class="text-secondary">激活码管理</a></li>
                <li class="breadcrumb-item active" aria-current="page">生成激活码</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.activation-codes.store') }}" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label for="count" class="form-label">生成数量</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-hash"></i>
                            </span>
                            <input type="number" class="form-control @error('count') is-invalid @enderror" id="count" name="count" min="1" max="100" value="{{ old('count', null) }}" placeholder="请输入生成数量" required>
                            @error('count')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            单次最多可生成 100 个激活码
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="expires_in" class="form-label">有效期（天）</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-calendar"></i>
                            </span>
                            <input type="number" class="form-control @error('expires_in') is-invalid @enderror" id="expires_in" name="expires_in" min="1" max="365" value="{{ old('expires_in', null) }}" placeholder="请输入有效期天数" required>
                            @error('expires_in')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            有效期最长为 365 天
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-soft-primary">
                            <i class="bi bi-plus-lg me-1"></i> 生成
                        </button>
                        <a href="{{ route('admin.activation-codes.index') }}" class="btn btn-soft-secondary">
                            <i class="bi bi-arrow-left me-1"></i> 返回
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title mb-4">生成说明</h5>
                <div class="alert alert-info bg-soft-info text-info mb-0">
                    <div class="d-flex">
                        <i class="bi bi-info-circle-fill fs-4 me-2"></i>
                        <div>
                            <h6 class="mb-2">注意事项：</h6>
                            <ul class="ps-3 mb-0">
                                <li>激活码生成后将立即生效</li>
                                <li>有效期从生成时间开始计算</li>
                                <li>生成的激活码可以在列表中查看和管理</li>
                                <li>未使用的激活码可以随时删除</li>
                                <li>已过期或已使用的激活码无法删除</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 