/**
 * AWS统一数据管理器
 * 避免重复请求，统一管理AMI数据、子网、安全组、密钥对数据
 */

class AWSUnifiedDataManager {
    constructor() {
        // 强制清除所有缓存 - 修复假AMI ID问题
        this.clearAllCache();
        this.cache = {
            amiData: null,
            subnets: {},      // 按账户ID缓存
            securityGroups: {}, // 按账户ID缓存
            keyPairs: {}      // 按账户ID缓存
        };
        this.loading = {
            amiData: false,
            subnets: {},
            securityGroups: {},
            keyPairs: {}
        };
        this.callbacks = {
            amiData: [],
            subnets: {},
            securityGroups: {},
            keyPairs: {}
        };
        // 记录已经提示过验证错误的账户，避免重复提示
        this.validationErrorShown = new Set();
    }

    // 清除所有缓存
    clearAllCache() {
        // 清除localStorage中的AMI缓存
        if (typeof(Storage) !== "undefined") {
            localStorage.removeItem('aws_ami_data');
            localStorage.removeItem('aws_ami_cache');
            sessionStorage.removeItem('aws_ami_data');
            sessionStorage.removeItem('aws_ami_cache');
        }
        console.log('🧹 已清除所有AMI缓存');
    }

    /**
     * 获取AMI数据（全局缓存，只请求一次）
     */
    getAmiData(callback) {
        // 强制清除AMI缓存以获取最新数据
        this.cache.amiData = null;

        // 如果已有缓存数据，直接返回
        if (this.cache.amiData) {
            callback(this.cache.amiData);
            return;
        }

        // 添加回调到队列
        this.callbacks.amiData.push(callback);

        // 如果正在加载，不重复请求
        if (this.loading.amiData) {
            return;
        }

        // 开始加载
        this.loading.amiData = true;
        console.log('🔄 强制重新加载AMI数据（清除缓存）...');

        $.ajax({
            url: '/user/aws-ec2/ami-data?_t=' + Date.now() + '&_v=20250711_fix',
            method: 'GET',
            dataType: 'json',
            cache: false,
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            success: (response) => {
                this.loading.amiData = false;
                
                if (response.success) {
                    this.cache.amiData = response.data;
                    console.log('✅ AMI数据加载成功:', response.data);
                    
                    // 执行所有回调
                    this.callbacks.amiData.forEach(cb => cb(response.data));
                    this.callbacks.amiData = [];
                } else {
                    console.error('❌ AMI数据加载失败:', response.message);
                    this.callbacks.amiData.forEach(cb => cb(null));
                    this.callbacks.amiData = [];
                }
            },
            error: (xhr, status, error) => {
                this.loading.amiData = false;
                console.error('❌ AMI数据请求失败:', error);
                this.callbacks.amiData.forEach(cb => cb(null));
                this.callbacks.amiData = [];
            }
        });
    }

    /**
     * 获取子网数据（按账户缓存）
     */
    getSubnets(accountId, region, callback) {
        const cacheKey = `${accountId}_${region}`;

        // 如果已有缓存数据，直接返回
        if (this.cache.subnets[cacheKey]) {
            callback(this.cache.subnets[cacheKey]);
            return;
        }

        // 初始化回调队列
        if (!this.callbacks.subnets[cacheKey]) {
            this.callbacks.subnets[cacheKey] = [];
        }
        this.callbacks.subnets[cacheKey].push(callback);

        // 如果正在加载，不重复请求
        if (this.loading.subnets[cacheKey]) {
            return;
        }

        // 开始加载
        this.loading.subnets[cacheKey] = true;
        console.log(`🔄 开始加载账户 ${accountId} 地区 ${region} 的子网数据...`);

        $.ajax({
            url: '/user/aws-ec2/subnets',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            dataType: 'json',
            success: (response) => {
                this.loading.subnets[cacheKey] = false;

                if (response.success) {
                    this.cache.subnets[cacheKey] = response.data;
                    console.log(`✅ 账户 ${accountId} 地区 ${region} 子网数据加载成功:`, response.data);

                    // 执行所有回调
                    this.callbacks.subnets[cacheKey].forEach(cb => cb(response.data));
                    this.callbacks.subnets[cacheKey] = [];
                } else {
                    console.error(`❌ 账户 ${accountId} 地区 ${region} 子网数据加载失败:`, response.message);

                    // 检查是否是账户验证错误
                    if ((response.message && (response.message.includes('AWS was not able to validate the provided access credentials') ||
                                             response.message.includes('账户不存在或状态异常'))) ||
                        (response.error && (response.error.includes('AWS was not able to validate the provided access credentials') ||
                                           response.error.includes('账户不存在或状态异常')))) {
                        this.handleAccountValidationError(accountId);
                    }

                    this.callbacks.subnets[cacheKey].forEach(cb => cb(null));
                    this.callbacks.subnets[cacheKey] = [];
                }
            },
            error: (xhr, status, error) => {
                this.loading.subnets[cacheKey] = false;
                console.error(`❌ 账户 ${accountId} 地区 ${region} 子网数据请求失败:`, error);

                // 检查是否是账户验证错误
                if (xhr.responseJSON &&
                    ((xhr.responseJSON.message && xhr.responseJSON.message.includes('AWS was not able to validate the provided access credentials')) ||
                     (xhr.responseJSON.error && xhr.responseJSON.error.includes('AWS was not able to validate the provided access credentials')))) {
                    this.handleAccountValidationError(accountId);
                }

                this.callbacks.subnets[cacheKey].forEach(cb => cb(null));
                this.callbacks.subnets[cacheKey] = [];
            }
        });
    }

    /**
     * 获取安全组数据（按账户缓存）
     */
    getSecurityGroups(accountId, region, callback) {
        const cacheKey = `${accountId}_${region}`;

        // 如果已有缓存数据，直接返回
        if (this.cache.securityGroups[cacheKey]) {
            callback(this.cache.securityGroups[cacheKey]);
            return;
        }

        // 初始化回调队列
        if (!this.callbacks.securityGroups[cacheKey]) {
            this.callbacks.securityGroups[cacheKey] = [];
        }
        this.callbacks.securityGroups[cacheKey].push(callback);

        // 如果正在加载，不重复请求
        if (this.loading.securityGroups[cacheKey]) {
            return;
        }

        // 开始加载
        this.loading.securityGroups[cacheKey] = true;
        console.log(`🔄 开始加载账户 ${accountId} 地区 ${region} 的安全组数据...`);

        $.ajax({
            url: '/user/aws-ec2/security-groups',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            dataType: 'json',
            success: (response) => {
                this.loading.securityGroups[cacheKey] = false;

                if (response.success) {
                    this.cache.securityGroups[cacheKey] = response.data;
                    console.log(`✅ 账户 ${accountId} 地区 ${region} 安全组数据加载成功:`, response.data);

                    // 执行所有回调
                    this.callbacks.securityGroups[cacheKey].forEach(cb => cb(response.data));
                    this.callbacks.securityGroups[cacheKey] = [];
                } else {
                    console.error(`❌ 账户 ${accountId} 地区 ${region} 安全组数据加载失败:`, response.message);
                    // 不在安全组接口检测账户验证错误，只记录错误
                    this.callbacks.securityGroups[cacheKey].forEach(cb => cb(null));
                    this.callbacks.securityGroups[cacheKey] = [];
                }
            },
            error: (xhr, status, error) => {
                this.loading.securityGroups[cacheKey] = false;
                console.error(`❌ 账户 ${accountId} 地区 ${region} 安全组数据请求失败:`, error);

                // 不在安全组接口检测账户验证错误，只记录错误
                this.callbacks.securityGroups[cacheKey].forEach(cb => cb(null));
                this.callbacks.securityGroups[cacheKey] = [];
            }
        });
    }

    /**
     * 获取密钥对数据（按账户缓存）
     */
    getKeyPairs(accountId, region, callback) {
        const cacheKey = `${accountId}_${region}`;

        // 如果已有缓存数据，直接返回
        if (this.cache.keyPairs[cacheKey]) {
            callback(this.cache.keyPairs[cacheKey]);
            return;
        }

        // 初始化回调队列
        if (!this.callbacks.keyPairs[cacheKey]) {
            this.callbacks.keyPairs[cacheKey] = [];
        }
        this.callbacks.keyPairs[cacheKey].push(callback);

        // 如果正在加载，不重复请求
        if (this.loading.keyPairs[cacheKey]) {
            return;
        }

        // 开始加载
        this.loading.keyPairs[cacheKey] = true;
        console.log(`🔄 开始加载账户 ${accountId} 地区 ${region} 的密钥对数据...`);

        $.ajax({
            url: '/user/aws-ec2/key-pairs',
            method: 'POST',
            data: { account_id: accountId, region: region },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            dataType: 'json',
            success: (response) => {
                this.loading.keyPairs[cacheKey] = false;

                if (response.success) {
                    this.cache.keyPairs[cacheKey] = response.data;
                    console.log(`✅ 账户 ${accountId} 地区 ${region} 密钥对数据加载成功:`, response.data);

                    // 执行所有回调
                    this.callbacks.keyPairs[cacheKey].forEach(cb => cb(response.data));
                    this.callbacks.keyPairs[cacheKey] = [];
                } else {
                    console.error(`❌ 账户 ${accountId} 地区 ${region} 密钥对数据加载失败:`, response.message);

                    // 检查是否是代理异常错误
                    if (this.isProxyError(response)) {
                        this.handleProxyError(response.message || response.error);
                        return;
                    }

                    // 不在密钥对接口检测账户验证错误，只记录错误
                    this.callbacks.keyPairs[cacheKey].forEach(cb => cb(null));
                    this.callbacks.keyPairs[cacheKey] = [];
                }
            },
            error: (xhr, status, error) => {
                this.loading.keyPairs[cacheKey] = false;
                console.error(`❌ 账户 ${accountId} 地区 ${region} 密钥对数据请求失败:`, error);

                // 检查是否是代理异常错误
                if (xhr.responseJSON && xhr.responseJSON.error_type === 'proxy_error') {
                    this.handleProxyError(xhr.responseJSON.message);
                    return;
                }

                // 不在密钥对接口检测账户验证错误，只记录错误
                this.callbacks.keyPairs[cacheKey].forEach(cb => cb(null));
                this.callbacks.keyPairs[cacheKey] = [];
            }
        });
    }

    /**
     * 当账户切换时，加载该账户的所有相关数据
     */
    loadAccountData(accountId, region, callbacks = {}) {
        console.log(`🔄 开始加载账户 ${accountId} 地区 ${region} 的所有数据...`);

        // 并行加载子网、安全组、密钥对
        if (callbacks.onSubnetsLoaded) {
            this.getSubnets(accountId, region, callbacks.onSubnetsLoaded);
        }

        if (callbacks.onSecurityGroupsLoaded) {
            this.getSecurityGroups(accountId, region, callbacks.onSecurityGroupsLoaded);
        }

        if (callbacks.onKeyPairsLoaded) {
            this.getKeyPairs(accountId, region, callbacks.onKeyPairsLoaded);
        }
    }

    /**
     * 清除指定账户的缓存（当账户信息更新时使用）
     */
    clearAccountCache(accountId, region = null) {
        if (region) {
            const cacheKey = `${accountId}_${region}`;
            delete this.cache.subnets[cacheKey];
            delete this.cache.securityGroups[cacheKey];
            delete this.cache.keyPairs[cacheKey];
            console.log(`🧹 已清除账户 ${accountId} 地区 ${region} 的缓存`);
        } else {
            // 清除该账户所有地区的缓存
            Object.keys(this.cache.subnets).forEach(key => {
                if (key.startsWith(accountId + '_')) {
                    delete this.cache.subnets[key];
                }
            });
            Object.keys(this.cache.securityGroups).forEach(key => {
                if (key.startsWith(accountId + '_')) {
                    delete this.cache.securityGroups[key];
                }
            });
            Object.keys(this.cache.keyPairs).forEach(key => {
                if (key.startsWith(accountId + '_')) {
                    delete this.cache.keyPairs[key];
                }
            });
            console.log(`🧹 已清除账户 ${accountId} 所有地区的缓存`);
        }
    }

    /**
     * 清除所有缓存
     */
    clearAllCache() {
        this.cache = {
            amiData: null,
            subnets: {},
            securityGroups: {},
            keyPairs: {}
        };
        console.log('🧹 已清除所有缓存');
    }

    /**
     * 清除特定账户的缓存
     */
    clearAccountCache(accountId) {
        let clearedCount = 0;

        // 清除子网缓存
        Object.keys(this.cache.subnets).forEach(key => {
            if (key.startsWith(accountId + '_')) {
                delete this.cache.subnets[key];
                clearedCount++;
            }
        });

        // 清除安全组缓存
        Object.keys(this.cache.securityGroups).forEach(key => {
            if (key.startsWith(accountId + '_')) {
                delete this.cache.securityGroups[key];
                clearedCount++;
            }
        });

        // 清除密钥对缓存
        Object.keys(this.cache.keyPairs).forEach(key => {
            if (key.startsWith(accountId + '_')) {
                delete this.cache.keyPairs[key];
                clearedCount++;
            }
        });

        console.log(`🧹 已清除账户 ${accountId} 的 ${clearedCount} 个缓存项`);
    }

    /**
     * 处理账户验证错误
     */
    handleAccountValidationError(accountId) {
        // 检查是否已经提示过这个账户的错误
        if (this.validationErrorShown.has(accountId)) {
            console.log(`⚠️ 账户 ${accountId} 验证错误已提示过，跳过重复提示`);
            return;
        }

        // 记录已提示
        this.validationErrorShown.add(accountId);

        console.error(`❌ 账户 ${accountId} 验证失败`);

        // 显示错误提示（只提示一次）
        if (typeof showErrorMessage === 'function') {
            showErrorMessage('该账户无效或异常，请重新检查账户');
        } else if (typeof toastr !== 'undefined') {
            toastr.error('该账户无效或异常，请重新检查账户');
        } else {
            alert('该账户无效或异常，请重新检查账户');
        }

        // 禁用创建实例按钮
        const $createButton = $('button[type="submit"], #createInstanceBtn, #create-instance-btn, .create-instance-btn');
        if ($createButton.length > 0) {
            $createButton.prop('disabled', true)
                         .addClass('disabled')
                         .css('background-color', '#6c757d')
                         .css('cursor', 'not-allowed');
            console.log('🔒 已禁用创建实例按钮');
        }

        // 标记特定账户为无效状态
        window.accountValidationFailed = true;
        window.invalidAccountId = accountId;

        // 清除该无效账户的所有缓存
        this.clearAccountCache(accountId);
        console.log(`🧹 账户验证失败：已清除账户 ${accountId} 的缓存`);
    }

    /**
     * 清除验证错误提示记录（在账户切换时调用）
     */
    clearValidationErrorHistory() {
        this.validationErrorShown.clear();
        console.log('🧹 已清除验证错误提示记录');
    }

    /**
     * 重置账户验证状态（在账户切换时调用）
     */
    resetAccountValidationState() {
        // 清除验证错误提示记录
        this.clearValidationErrorHistory();

        // 重置全局验证状态
        window.accountValidationFailed = false;
        window.invalidAccountId = null;

        console.log('🔄 已重置账户验证状态');
    }

    /**
     * 检查是否是代理异常错误
     */
    isProxyError(response) {
        if (!response) return false;
        // 优先检查error_type字段
        if (response.error_type === 'proxy_error') return true;
        // 兼容旧的字符串检查方式
        const message = response.message || response.error || '';
        return message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
               message.includes('当前代理模式IP异常，请切换其他模式进行操作');
    }

    /**
     * 处理代理异常错误
     */
    handleProxyError(message) {
        console.error('❌ 代理异常错误:', message);

        // 显示toast提示
        if (typeof showProxyToast === 'function') {
            showProxyToast('error', message);
        } else if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }
}

// 创建全局实例
window.awsDataManager = new AWSUnifiedDataManager();
