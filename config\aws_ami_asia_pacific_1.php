<?php

/**
 * AWS AMI数据配置 - 亚太地区1
 * 包含东亚、东南亚等地区
 */

return [
    'ap-northeast-1' => [
        'name' => '亚太地区（东京）',
        'code' => 'ap-northeast-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0df99b3a8349462cb', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0df99b3a8349462cb', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0df99b3a8349462cb', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0d52744d6551d851e', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0df99b3a8349462cb', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'ap-northeast-2' => [
        'name' => '亚太地区（首尔）',
        'code' => 'ap-northeast-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0e9bfdb247cc8de84', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0e9bfdb247cc8de84', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0a12345bcdef67890', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'ap-northeast-3' => [
        'name' => '亚太地区（大阪）',
        'code' => 'ap-northeast-3',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0b276ad8d5b8abb29', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-01234abcdef567890', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-01234abcdef567890', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-southeast-1' => [
        'name' => '亚太地区（新加坡）',
        'code' => 'ap-southeast-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0c802847a7dd848c0', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0df7a207adb9748c7', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0234abcdef5678901', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-southeast-2' => [
        'name' => '亚太地区（悉尼）',
        'code' => 'ap-southeast-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0567f647e75c7bc05', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-034abcdef56789012', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-034abcdef56789012', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ap-southeast-3' => [
        'name' => '亚太地区（雅加达）',
        'code' => 'ap-southeast-3',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-04abcdef567890123', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-04abcdef567890123', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-04abcdef567890123', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ]
];
