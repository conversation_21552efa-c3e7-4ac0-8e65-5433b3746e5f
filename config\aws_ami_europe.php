<?php

/**
 * AWS AMI数据配置 - 欧洲地区
 * 包含西欧、北欧、南欧等地区
 */

return [
    'eu-west-1' => [
        'name' => '欧洲（爱尔兰）',
        'code' => 'eu-west-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'eu-west-2' => [
        'name' => '欧洲（伦敦）',
        'code' => 'eu-west-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'eu-central-1' => [
        'name' => '欧洲（法兰克福）',
        'code' => 'eu-central-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0e067cc8a2b58de59', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0d527b8c289b4af7f', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'eu-central-2' => [
        'name' => '欧洲（苏黎世）',
        'code' => 'eu-central-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0a8e758f5e873d1c1', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0dad20bd1b9c8c004', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ]
        ]
    ],
    'eu-west-3' => [
        'name' => '欧洲（巴黎）',
        'code' => 'eu-west-3',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0b0dcb5067f052a63', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0194c3e07668a7e36', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0b0dcb5067f052a63', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0194c3e07668a7e36', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                ]
            ]
        ]
    ],
    'eu-north-1' => [
        'name' => '欧洲（斯德哥尔摩）',
        'code' => 'eu-north-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0989fb15ce71ba39e', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0c947472aff72870c', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0989fb15ce71ba39e', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0c947472aff72870c', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                ]
            ]
        ]
    ],
    'eu-south-1' => [
        'name' => '欧洲（米兰）',
        'code' => 'eu-south-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0f3164307ee5d695a', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-08d70e59c07c61a3a', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0f3164307ee5d695a', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-08d70e59c07c61a3a', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ]
        ]
    ],
    'eu-south-2' => [
        'name' => '欧洲（西班牙）',
        'code' => 'eu-south-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ]
        ]
    ]
];
