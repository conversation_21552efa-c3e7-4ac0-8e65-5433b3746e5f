<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->string('iam_username')->nullable();
            $table->string('iam_password')->nullable();
            $table->string('iam_access_key')->nullable();
            $table->string('iam_secret_key')->nullable();
            $table->enum('iam_status', ['success', 'failed'])->nullable();
            $table->timestamp('iam_created_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->dropColumn([
                'iam_username',
                'iam_password',
                'iam_access_key',
                'iam_secret_key',
                'iam_status',
                'iam_created_at'
            ]);
        });
    }
}; 