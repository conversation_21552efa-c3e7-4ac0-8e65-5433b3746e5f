<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AmiDataTest extends TestCase
{
    /**
     * 测试AMI数据API接口
     */
    public function test_ami_data_api_returns_valid_data()
    {
        $response = $this->get('/user/aws-ec2/ami-data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'regions',
                'region_system_support',
                'system_icons',
                'data_source'
            ]
        ]);

        $data = $response->json('data');
        
        // 验证数据结构
        $this->assertIsArray($data['regions']);
        $this->assertIsArray($data['region_system_support']);
        $this->assertIsArray($data['system_icons']);
        
        // 验证至少有一些地区数据
        $this->assertGreaterThan(0, count($data['regions']));
        
        // 验证地区数据结构
        foreach ($data['regions'] as $regionCode => $regionData) {
            $this->assertArrayHasKey('name', $regionData);
            $this->assertArrayHasKey('code', $regionData);
            $this->assertArrayHasKey('systems', $regionData);
            $this->assertEquals($regionCode, $regionData['code']);
            
            // 验证系统数据结构
            foreach ($regionData['systems'] as $osName => $osData) {
                $this->assertArrayHasKey('name', $osData);
                $this->assertArrayHasKey('versions', $osData);
                $this->assertEquals($osName, $osData['name']);
                
                // 验证版本数据结构
                foreach ($osData['versions'] as $version) {
                    $this->assertArrayHasKey('name', $version);
                    $this->assertArrayHasKey('ami_id', $version);
                    $this->assertArrayHasKey('display_name', $version);
                    
                    // 验证AMI ID格式
                    $this->assertMatchesRegularExpression('/^ami-[a-f0-9]{17}$/', $version['ami_id']);
                }
            }
        }
    }

    /**
     * 测试支持的操作系统
     */
    public function test_supported_operating_systems()
    {
        $response = $this->get('/user/aws-ec2/ami-data');
        $data = $response->json('data');

        $expectedSystems = [
            'Amazon Linux',
            'Ubuntu',
            'Windows Server',
            'Red Hat',
            'SUSE Linux',
            'Debian',
            'CentOS',
            'Oracle Linux',
            'Rocky Linux',
            'AlmaLinux'
        ];

        // 检查系统图标配置
        foreach ($expectedSystems as $system) {
            $this->assertArrayHasKey($system, $data['system_icons']);
        }

        // 检查至少有一个地区支持这些系统
        $allSupportedSystems = [];
        foreach ($data['regions'] as $regionData) {
            $allSupportedSystems = array_merge($allSupportedSystems, array_keys($regionData['systems']));
        }
        $allSupportedSystems = array_unique($allSupportedSystems);

        foreach ($expectedSystems as $system) {
            $this->assertContains($system, $allSupportedSystems, "系统 {$system} 应该被至少一个地区支持");
        }
    }

    /**
     * 测试地区系统支持映射
     */
    public function test_region_system_support_mapping()
    {
        $response = $this->get('/user/aws-ec2/ami-data');
        $data = $response->json('data');

        foreach ($data['region_system_support'] as $regionCode => $supportedSystems) {
            // 验证地区存在
            $this->assertArrayHasKey($regionCode, $data['regions']);
            
            // 验证支持的系统与实际数据一致
            $actualSystems = array_keys($data['regions'][$regionCode]['systems']);
            $this->assertEquals(sort($actualSystems), sort($supportedSystems));
        }
    }

    /**
     * 测试缓存功能
     */
    public function test_ami_data_caching()
    {
        // 第一次请求
        $start1 = microtime(true);
        $response1 = $this->get('/user/aws-ec2/ami-data');
        $time1 = microtime(true) - $start1;

        // 第二次请求（应该使用缓存）
        $start2 = microtime(true);
        $response2 = $this->get('/user/aws-ec2/ami-data');
        $time2 = microtime(true) - $start2;

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        // 验证数据一致性
        $this->assertEquals($response1->json(), $response2->json());

        // 第二次请求应该更快（使用缓存）
        $this->assertLessThan($time1, $time2, '缓存应该使第二次请求更快');
    }

    /**
     * 测试清除缓存功能
     */
    public function test_clear_cache_functionality()
    {
        // 先获取数据以建立缓存
        $this->get('/user/aws-ec2/ami-data');

        // 清除缓存
        $response = $this->post('/user/aws-ec2/ami-clear-cache');
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'AMI数据缓存已清除'
        ]);
    }

    /**
     * 测试数据刷新功能
     */
    public function test_refresh_data_functionality()
    {
        $response = $this->post('/user/aws-ec2/ami-refresh-data');
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => '数据已刷新，下次请求将重新加载'
        ]);
    }

    /**
     * 测试AMI ID格式验证
     */
    public function test_ami_id_format_validation()
    {
        $response = $this->get('/user/aws-ec2/ami-data');
        $data = $response->json('data');

        foreach ($data['regions'] as $regionData) {
            foreach ($regionData['systems'] as $osData) {
                foreach ($osData['versions'] as $version) {
                    $amiId = $version['ami_id'];
                    
                    // 验证AMI ID格式：ami-xxxxxxxxxxxxxxxxx (ami- + 17位十六进制字符)
                    $this->assertMatchesRegularExpression(
                        '/^ami-[a-f0-9]{17}$/',
                        $amiId,
                        "AMI ID {$amiId} 格式不正确"
                    );
                }
            }
        }
    }
}
