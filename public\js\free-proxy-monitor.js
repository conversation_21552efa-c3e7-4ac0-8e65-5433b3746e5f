/**
 * 免费代理状态监控器
 * 用于检查免费代理状态并显示相应提示
 */

// 全局免费代理监控器
window.FreeProxyMonitor = {
    // 检查免费代理状态
    checkStatus: function(currentProxyMode, freeProxyStatus, freeProxyErrorMessage) {
        if (currentProxyMode === 'free_proxy' && freeProxyStatus !== 'active') {
            this.showErrorToast('免费代理IP异常，请联系管理员');
        }
    },

    // 显示错误提示
    showErrorToast: function(message) {
        // 优先使用代理状态栏的提示函数
        if (typeof showProxyToast === 'function') {
            showProxyToast('error', message);
            return;
        }

        // 如果没有代理状态栏，创建简单的提示
        this.createSimpleToast(message);
    },

    // 创建简单的提示元素
    createSimpleToast: function(message) {
        // 检查是否已经有提示存在
        const existingToast = document.getElementById('freeProxyErrorToast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建提示元素
        const toast = document.createElement('div');
        toast.id = 'freeProxyErrorToast';
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; max-width: 400px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
        toast.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 150);
            }
        }, 3000);
    },

    // 监听代理模式变更
    listenForModeChanges: function() {
        // 监听localStorage变化（代理模式切换）
        window.addEventListener('storage', (e) => {
            if (e.key === 'proxy_mode_changed' && e.newValue === 'free_proxy') {
                // 延迟检查，等待页面刷新后的状态
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        });
    },

    // 初始化监控器
    init: function(currentProxyMode, freeProxyStatus, freeProxyErrorMessage) {
        // 检查当前状态
        this.checkStatus(currentProxyMode, freeProxyStatus, freeProxyErrorMessage);
        
        // 监听模式变更
        this.listenForModeChanges();
    }
};

// 页面加载完成后自动初始化（如果有相关数据）
document.addEventListener('DOMContentLoaded', function() {
    // 这个函数会在各个页面中被调用，传入具体的状态数据
    if (typeof window.initFreeProxyMonitor === 'function') {
        window.initFreeProxyMonitor();
    }
});
