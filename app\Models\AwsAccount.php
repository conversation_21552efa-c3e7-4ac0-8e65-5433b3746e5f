<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AwsAccount extends Model
{
    use HasFactory;

    const STATUS_UNCHECK = 0; // 未测试
    const STATUS_NORMAL = 1;  // 正常
    const STATUS_BANNED = 2;  // 封禁
    const STATUS_INVALID = 3; // 无效

    // EC2开通状态常量
    const EC2_STATUS_NOT_ENABLED = 0; // 未开通
    const EC2_STATUS_ENABLED = 1;     // 已开通

    protected $fillable = [
        'account_name',
        'email_password',
        'aws_password',
        'access_key',
        'secret_key',
        'aws_mfa_key',
        'quota',
        'aws_account_id',
        'account_id',
        'iam_username',
        'iam_password',
        'iam_access_key',
        'iam_secret_key',
        'iam_status',
        'iam_created_at',
        'remarks'
    ];

    protected $guarded = [
        'id',
        'user_id',
        'status',
        'ec2_status',
        'last_check_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'status' => 'integer',
        'ec2_status' => 'integer',
        'last_check_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => self::STATUS_UNCHECK, // 设置默认状态为未测试
        'ec2_status' => self::EC2_STATUS_NOT_ENABLED, // 设置默认EC2状态为未开通
    ];

    /**
     * Get the user that owns the AWS account.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    protected function statusColor(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match($this->status) {
                    1 => '#28a745',  // 正常 - 绿色
                    2 => '#dc3545',  // 封禁 - 红色
                    3 => '#ffc107',  // 无效 - 黄色
                    default => '#6c757d',   // 未测 - 灰色
                };
            }
        );
    }

    protected function statusText(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match($this->status) {
                    1 => '正常',
                    2 => '封禁',
                    3 => '无效',
                    default => '未测',
                };
            }
        );
    }

    public function getQuotaAttribute($value)
    {
        if (!$value) {
            return null;
        }

        // 尝试解析JSON，如果失败则直接返回原始值
        try {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
        } catch (\Exception $e) {
            // JSON解析失败，返回原始值
        }

        // 如果不是有效的JSON，直接返回原始值
        return $value;
    }

    public static function getStatusList(): array
    {
        return [
            self::STATUS_UNCHECK => '未测试',
            self::STATUS_NORMAL => '正常',
            self::STATUS_BANNED => '封禁',
            self::STATUS_INVALID => '无效'
        ];
    }

    public static function getStatusBadgeClass($status): string
    {
        return match ($status) {
            self::STATUS_UNCHECK => 'bg-secondary',
            self::STATUS_NORMAL => 'bg-success',
            self::STATUS_BANNED => 'bg-danger',
            self::STATUS_INVALID => 'bg-warning',
            default => 'bg-secondary'
        };
    }

    /**
     * 获取EC2状态列表
     */
    public static function getEc2StatusList(): array
    {
        return [
            self::EC2_STATUS_NOT_ENABLED => '未开通',
            self::EC2_STATUS_ENABLED => '已开通'
        ];
    }

    /**
     * 获取EC2状态文本
     */
    public function getEc2StatusTextAttribute(): string
    {
        return match($this->ec2_status) {
            self::EC2_STATUS_ENABLED => '已开通',
            default => '未开通',
        };
    }

    /**
     * 获取EC2状态徽章样式
     */
    public static function getEc2StatusBadgeClass($ec2Status): string
    {
        return match ($ec2Status) {
            self::EC2_STATUS_ENABLED => 'bg-success',
            default => 'bg-secondary'
        };
    }
} 