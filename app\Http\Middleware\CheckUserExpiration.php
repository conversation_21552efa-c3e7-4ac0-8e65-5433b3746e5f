<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Carbon\Carbon;

class CheckUserExpiration
{
    public function handle(Request $request, Closure $next)
    {
        if (auth()->check()) {
            $user = auth()->user();
            
            if ($user->expires_at && Carbon::parse($user->expires_at)->isPast()) {
                auth()->logout();
                return redirect()->route('login')->withErrors(['expired' => '您的账户已过期，请联系管理员续期']);
            }
        }

        return $next($request);
    }
} 