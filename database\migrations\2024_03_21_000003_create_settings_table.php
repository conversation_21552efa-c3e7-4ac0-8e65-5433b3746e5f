<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
        });

        // 插入默认设置
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'AWS云面板',
                'description' => '网站名称',
            ],
            [
                'key' => 'site_description',
                'value' => 'AWS账户管理系统',
                'description' => '网站描述',
            ],
            [
                'key' => 'login_title',
                'value' => 'AWS云面板',
                'description' => '登录页面标题',
            ],
            [
                'key' => 'dashboard_title',
                'value' => 'AWS云面板',
                'description' => '仪表盘标题',
            ],
            [
                'key' => 'register_enabled',
                'value' => '1',
                'description' => '是否启用注册功能',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'description' => '是否启用维护模式',
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('settings')->insert([
                'key' => $setting['key'],
                'value' => $setting['value'],
                'description' => $setting['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    public function down()
    {
        Schema::dropIfExists('settings');
    }
}; 