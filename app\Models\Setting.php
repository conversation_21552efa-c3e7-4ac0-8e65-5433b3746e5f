<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'description',
    ];

    /**
     * 获取设置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        $settings = static::getAll();
        return $settings[$key] ?? $default;
    }

    /**
     * 获取所有设置
     *
     * @return array
     */
    public static function getAll()
    {
        return Cache::rememberForever('settings', function () {
            return static::pluck('value', 'key')->all();
        });
    }

    /**
     * 设置值
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set($key, $value)
    {
        static::updateOrCreate(
            ['key' => $key],
            ['value' => (string)$value]
        );

        static::clearCache();
    }

    /**
     * 清除设置缓存
     *
     * @return void
     */
    public static function clearCache()
    {
        Cache::forget('settings');
    }

    /**
     * 获取布尔值设置
     *
     * @param string $key
     * @param bool $default
     * @return bool
     */
    public static function getBool($key, $default = false)
    {
        $value = static::get($key);
        if ($value === null) {
            return $default;
        }
        return $value === '1' || $value === 'true' || $value === true;
    }
}
