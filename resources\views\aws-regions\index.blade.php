@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 区域选择卡片 */
.region-card {
    border-radius: 8px;
    border: 1px solid rgba(0,0,0,.05);
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
}

.region-card:hover {
    background-color: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
}

.region-card.selected {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: #4361ee;
}

.region-card .region-icon {
    font-size: 1.25rem;
    width: 2rem;
    color: #4361ee;
}

.region-card h6 {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    font-weight: 600;
    color: #2d3748;
}

.region-card small {
    font-size: 0.75rem;
    color: #718096;
}

/* 区域网格布局 */
.region-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.75rem;
}

@media (max-width: 1400px) {
    .region-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .region-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .region-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .region-grid {
        grid-template-columns: 1fr;
    }
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: none;
}

.btn-soft-primary:hover {
    background-color: #4361ee;
    color: #fff;
}

.btn-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    border: 1px solid rgba(46, 212, 122, 0.2);
}

.btn-soft-success:hover,
.btn-soft-success:focus,
.btn-soft-success:active {
    background-color: #2ed47a;
    color: #fff;
    border-color: #2ed47a;
    box-shadow: none;
}

.btn-soft-info {
    color: #37b9f1;
    background-color: rgba(55, 185, 241, 0.1);
    border: 1px solid rgba(55, 185, 241, 0.2);
}

.btn-soft-info:hover,
.btn-soft-info:focus,
.btn-soft-info:active {
    background-color: #37b9f1;
    color: #fff;
    border-color: #37b9f1;
    box-shadow: none;
}

.btn-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    border: 1px solid rgba(242, 87, 103, 0.2);
}

.btn-soft-danger:hover,
.btn-soft-danger:focus,
.btn-soft-danger:active {
    background-color: #f25767;
    color: #fff;
    border-color: #f25767;
    box-shadow: none;
}

/* 徽章样式 - 与其他页面保持一致 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    font-size: 0.9rem;
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    font-size: 0.9rem;
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    font-size: 0.9rem;
}

.badge-soft-secondary {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    font-size: 0.9rem;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.btn-soft-primary:hover,
.btn-soft-primary:focus,
.btn-soft-primary:active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
    box-shadow: none;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

.table > :not(caption) > * > * {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
    text-align: center;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 所有列居中对齐 */
.table th, .table td {
    text-align: center;
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 分页样式 */
.pagination {
    margin: 0;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #4361ee;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.page-link:hover {
    z-index: 2;
    color: #4361ee;
    text-decoration: none;
    background-color: rgba(67, 97, 238, 0.1);
    border-color: #dee2e6;
}

.page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #4361ee;
    border-color: #4361ee;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.page-item:last-child .page-link {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.regions-container {
    padding: 10px;
}

.region-group {
    margin-bottom: 20px;
}

.region-group-title {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #4361ee;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 2px solid rgba(67, 97, 238, 0.1);
}

.region-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
}

.region-card {
    background: #fff;
    border: 1px solid rgba(0,0,0,.05);
    border-radius: 6px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.region-card:hover {
    background-color: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
}

.region-card.selected {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: #4361ee;
}

.region-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    width: 100%;
}

.region-icon {
    font-size: 1.1rem;
    color: #4361ee;
    margin-bottom: 4px;
}

.region-info {
    text-align: center;
    width: 100%;
}

.region-info h6 {
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
    line-height: 1.3;
}

.region-info .region-location {
    font-size: 1rem;
    color: #4361ee;
    display: block;
    margin: 4px 0;
    font-weight: 600;
}

.region-info .region-id {
    font-size: 0.85rem;
    color: #4361ee;
    display: block;
    font-weight: 600;
}

@media (max-width: 1400px) {
    .region-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .region-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .region-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .region-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid fade-in">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">AWS区域开通</h4>
                            <p class="text-muted mb-0 mt-1">选择并开通AWS账户的区域服务</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <!-- 占位元素，保持布局平衡 -->
                        <div style="width: 120px;"></div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="regions-container">
                                @php
                                $regions = [
                                    '美洲地区' => [
                                        ['id' => 'us-gov-east-1', 'name' => 'AWS GovCloud（美国东部）'],
                                        ['id' => 'us-gov-west-1', 'name' => 'AWS GovCloud（美国西部）'],
                                        ['id' => 'mx-central-1', 'name' => '墨西哥（中部）'],
                                        // ['id' => 'us-east-1', 'name' => '美国东部（弗吉尼亚北部）'],
                                        // ['id' => 'us-east-2', 'name' => '美国东部（俄亥俄）'],
                                        // ['id' => 'us-west-1', 'name' => '美国西部（加利福北部）'],
                                        // ['id' => 'us-west-2', 'name' => '美国西部（俄勒冈）'],
                                        // ['id' => 'sa-east-1', 'name' => '南美洲（圣保罗）'],
                                        // ['id' => 'ca-west-1', 'name' => '加拿大（卡尔加里）']
                                    ],
                                    '亚太地区' => [
                                        ['id' => 'ap-east-1', 'name' => '亚太地区（香港）'],
                                        ['id' => 'ap-south-2', 'name' => '亚太地区（海得拉巴）'],
                                        // ['id' => 'ap-south-1', 'name' => '亚太地区（孟买）'],
                                        // ['id' => 'ap-northeast-1', 'name' => '亚太地区（东京）'],
                                        // ['id' => 'ap-northeast-2', 'name' => '亚太地区（首尔）'],
                                        // ['id' => 'ap-northeast-3', 'name' => '亚太地区（大阪）'],
                                        // ['id' => 'ap-southeast-1', 'name' => '亚太地区（新加坡）'],
                                        // ['id' => 'ap-southeast-2', 'name' => '亚太地区（悉尼）'],
                                        ['id' => 'ap-southeast-3', 'name' => '亚太地区（雅加达）'],
                                        ['id' => 'ap-southeast-4', 'name' => '亚太地区（墨尔本）'],
                                        ['id' => 'ap-southeast-5', 'name' => '亚太地区（马来西亚）'],
                                        ['id' => 'ap-southeast-7', 'name' => '亚太地区（泰国）']
                                    ],
                                    '欧洲地区' => [
                                        // ['id' => 'eu-central-1', 'name' => '欧洲（法兰克福）'],
                                        // ['id' => 'eu-north-1', 'name' => '欧洲（斯德哥尔摩）'],
                                        // ['id' => 'eu-west-1', 'name' => '欧洲（爱尔兰）'],
                                        // ['id' => 'eu-west-2', 'name' => '欧洲（伦敦）'],
                                        // ['id' => 'eu-central-2', 'name' => '欧洲（苏黎世）'],
                                        // ['id' => 'eu-west-3', 'name' => '欧洲（巴黎）'],
                                        ['id' => 'eu-south-1', 'name' => '欧洲（米兰）'],
                                        ['id' => 'eu-south-2', 'name' => '欧洲（西班牙）']
                                    ],
                                    '中东和非洲' => [
                                        ['id' => 'me-south-1', 'name' => '中东（巴林）'],
                                        ['id' => 'me-central-1', 'name' => '中东（阿联酋）'],
                                        ['id' => 'af-south-1', 'name' => '非洲（开普敦）'],
                                        ['id' => 'il-central-1', 'name' => '中东（特拉维夫）']
                                    ]
                                ];
                                @endphp

                                @foreach($regions as $groupName => $groupRegions)
                                    <div class="region-group">
                                        <h3 class="region-group-title">{{ $groupName }}</h3>
                                        <div class="region-grid">
                                            @foreach($groupRegions as $region)
                                                @php
                                                    $nameParts = explode('（', $region['name']);
                                                    $mainName = $nameParts[0];
                                                    $location = isset($nameParts[1]) ? '（'.trim($nameParts[1]) : '';
                                                @endphp
                                                <div class="region-card" data-region="{{ $region['id'] }}">
                                                    <div class="region-content">
                                                        <i class="bi {{ $groupName === '美洲地区' ? 'bi-globe-americas' : 
                                                            ($groupName === '亚太地区' ? 'bi-globe-asia-australia' : 
                                                            'bi-globe-europe-africa') }} region-icon"></i>
                                                        <div class="region-info">
                                                            <h6>{{ $mainName }}</h6>
                                                            <span class="region-location">{{ $location }}</span>
                                                            <span class="region-id">{{ $region['id'] }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="col-md-4" style="margin-top: 280px;">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title mb-3">选中的区域</h5>
                                    <div id="selectedRegion" class="mb-3">
                                        <p class="text-muted mb-0">请选择要开通的区域</p>
                                    </div>
                                    <button class="btn btn-soft-primary w-100" id="enableRegionBtn" disabled>
                                        <i class="bi bi-check2-circle me-1"></i>开通选中区域
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 使用筛选组件 -->
                    <x-filter-panel 
                        :status-options="App\Models\AwsAccount::getStatusList()" />
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th width="40">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                                </div>
                                            </th>
                                            <th>账户邮箱</th>
                                            <th>邮箱密码</th>
                                            <th>AWS密码</th>
                                            <th>访问密钥</th>
                                            <th>秘密访问密钥</th>
                                            <th>状态</th>
                                            <th>开通结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if($accounts && count($accounts) > 0)
                                            @foreach($accounts as $account)
                                            <tr data-account-id="{{ $account->id }}">
                                                <td>
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input account-select" value="{{ $account->id }}">
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate copy-cell" style="max-width: 200px; cursor:pointer;user-select:all;" title="{{ $account->account_name }}">
                                                        {{ $account->account_name }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate copy-cell" style="max-width: 100px; cursor:pointer;user-select:all;" title="{{ $account->email_password }}">
                                                        {{ $account->email_password }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate copy-cell" style="max-width: 100px; cursor:pointer;user-select:all;" title="{{ $account->aws_password }}">
                                                        {{ $account->aws_password }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate copy-cell" style="max-width: 150px; cursor:pointer;user-select:all;" title="{{ $account->access_key }}">
                                                        {{ $account->access_key }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate copy-cell" style="max-width: 150px; cursor:pointer;user-select:all;" title="{{ $account->secret_key }}">
                                                        {{ $account->secret_key }}
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($account->status === 0)
                                                        <span class="badge badge-soft-warning">未测</span>
                                                    @elseif($account->status === 1)
                                                        <span class="badge badge-soft-success">正常</span>
                                                    @elseif($account->status === 2)
                                                        <span class="badge badge-soft-danger">封禁</span>
                                                    @else
                                                        <span class="badge badge-soft-secondary">无效</span>
                                                    @endif
                                                </td>
                                                <td class="enable-result">
                                                    <div class="region-status">
                                                        <!--<span class="badge rounded-pill bg-secondary" style="font-size: 14px; padding: 8px 12px;">未开通</span>-->
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="8" class="text-center">暂无数据</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-4 d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    总共 {{ $accounts->total() }} 条记录
                                </div>
                                <div>
                                    {{ $accounts->withQueryString()->links('components.pagination') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载进度条 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 class="mb-3" id="loadingText">正在开通区域...</h5>
        <div class="progress mb-2" style="width: 200px;">
            <div class="progress-bar" id="progressBar" role="progressbar"></div>
        </div>
        <p class="mb-0" id="progressText">0%</p>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成');
    console.log('showProxyToast function available:', typeof showProxyToast === 'function');
    if (typeof showProxyToast === 'function') {
        console.log('showProxyToast function details:', showProxyToast.toString());
    }
    let selectedRegion = null;
    const enableRegionBtn = document.getElementById('enableRegionBtn');
    const selectedRegionDiv = document.getElementById('selectedRegion');
    const selectAllCheckbox = document.getElementById('selectAll');

    // 初始化所有工具提示
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    }

    // 全选功能
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.account-select');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // 区域选择
    document.querySelectorAll('.region-card').forEach(item => {
        item.addEventListener('click', function() {
            selectedRegion = this.getAttribute('data-region');
            document.querySelectorAll('.region-card').forEach(r => r.classList.remove('selected'));
            this.classList.add('selected');
            
            // 更新选中区域显示
            const regionName = this.querySelector('h6').textContent;
            const regionLocation = this.querySelector('.region-location').textContent;
            const regionId = this.querySelector('.region-id').textContent;
            selectedRegionDiv.innerHTML = `
                <h6 class="mb-2">${regionName}</h6>
                <div class="fw-semibold text-primary mb-1">${regionLocation}</div>
                <div class="fw-semibold text-primary">${regionId}</div>
            `;
            
            // 启用开通按钮
            enableRegionBtn.disabled = false;
        });
    });

    // 开通选中区域
    document.getElementById('enableRegionBtn').addEventListener('click', function() {
        const selectedAccounts = Array.from(document.querySelectorAll('.account-select:checked')).map(cb => cb.value);
        const selectedRegionCard = document.querySelector('.region-card.selected');
        
        if (!selectedAccounts.length) {
            alert('请选择要开通的账户');
            return;
        }

        if (!selectedRegionCard) {
            alert('请选择要开通的区域');
            return;
        }

        const regionName = selectedRegionCard.querySelector('h6').textContent;
        const regionLocation = selectedRegionCard.querySelector('.region-location').textContent;
        const regionId = selectedRegionCard.querySelector('.region-id').textContent;

        if (!confirm(`确定要为选中的 ${selectedAccounts.length} 个账户开通 ${regionName}${regionLocation} (${regionId}) 区域吗？`)) {
            return;
        }

        // 显示加载遮罩
        document.getElementById('loadingOverlay').style.display = 'flex';
        document.getElementById('loadingText').textContent = '正在开通区域...';
        
        // 初始化进度条
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        let progress = 0;
        let totalAccounts = selectedAccounts.length;
        let currentAccount = 0;
        
        // 更新进度条函数
        function updateProgress() {
            progress = Math.round((currentAccount / totalAccounts) * 100);
            progressBar.style.width = progress + '%';
            progressText.textContent = progress + '%';
            document.getElementById('loadingText').textContent = `正在开通区域... (${currentAccount}/${totalAccounts})`;
        }
        
        // 初始化进度条
        updateProgress();
        
        // 创建进度条动画
        const progressInterval = setInterval(() => {
            if (currentAccount < totalAccounts) {
                currentAccount++;
                updateProgress();
            }
        }, 1500);

        // 发送请求
        fetch('/user/aws-regions/enable', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                account_ids: selectedAccounts,
                region: selectedRegion
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            if (!response.ok) {
                return response.json().then(json => {
                    console.log('Error response JSON:', json);
                    // 检查是否是代理异常错误
                    if (json.message && (json.message.includes('当前免费代理模式IP异常，请切换其他模式进行操作') ||
                                       json.message.includes('当前代理模式IP异常，请切换其他模式进行操作'))) {
                        console.log('检测到代理异常消息');
                        // 显示代理异常提示
                        clearInterval(progressInterval);
                        document.getElementById('loadingOverlay').style.display = 'none';
                        if (typeof showProxyToast === 'function') {
                            console.log('调用 showProxyToast');
                            showProxyToast('error', json.message);
                        } else {
                            console.error('showProxyToast function not available');
                            alert(json.message);
                        }
                        // 抛出错误以停止Promise链
                        throw new Error('PROXY_ERROR_STOP');
                    }
                    throw new Error(json.message || '请求失败');
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('Success response data:', data);
            // 清除进度条动画
            clearInterval(progressInterval);
            
            // 确保进度达到100%
            currentAccount = totalAccounts;
            updateProgress();
            
            // 检查是否是代理异常错误
            if (data && data.error_type === 'proxy_error') {
                console.log('检测到代理异常 error_type');
                if (typeof showProxyToast === 'function') {
                    console.log('调用 showProxyToast');
                    showProxyToast('error', data.message);
                } else {
                    console.error('showProxyToast function not available');
                    alert(data.message);
                }
                document.getElementById('loadingOverlay').style.display = 'none';
                return;
            }
            
            console.log('API响应:', data);

            // 更新每个账户的开通结果
            if (data.results) {
                let successCount = 0;
                let failedCount = 0;

                // 处理失败的账户
                if (Array.isArray(data.results.failed)) {
                    data.results.failed.forEach(result => {
                        if (result && result.account_id) {
                            let isSuccess = false;
                            
                            try {
                                // 解析enable_result字符串为JSON对象
                                const enableResult = JSON.parse(result.enable_result);
                                const responses = enableResult.responses;
                                
                                // 1. 检查错误消息
                                if (responses.error) {
                                    const errorMessage = responses.error;
                                    const successConditions = [
                                        'is currently being enabled or disabled',
                                        'is not a valid region for opt-in or opt-out',
                                        'Unable to switch region status because of its current opt-in status'
                                    ];

                                    // 检查是否满足任一成功条件
                                    isSuccess = successConditions.some(condition => errorMessage.includes(condition));

                                    if (isSuccess) {
                                        successCount++;
                                    } else {
                                        // 如果错误消息不满足成功条件，尝试从错误消息中提取区域名称
                                        let regionName = '';
                                        const match1 = errorMessage.match(/Account \d+ and region ([a-z]+-[a-z]+-\d+)/);
                                        const match2 = errorMessage.match(/([a-z]+-[a-z]+-\d+) is not a valid region/);
                                        const match3 = errorMessage.match(/region ([a-z]+-[a-z]+-\d+)/);
                                        
                                        if (match1) {
                                            regionName = match1[1];
                                        } else if (match2) {
                                            regionName = match2[1];
                                        } else if (match3) {
                                            regionName = match3[1];
                                        }

                                        // 检查区域状态
                                        if (regionName && responses.describeRegions && responses.describeRegions.Regions) {
                                            const region = responses.describeRegions.Regions.find(r => 
                                                r.RegionName === regionName
                                            );
                                            
                                            if (region && 
                                                (region.RegionOptStatus === 'ENABLING' || 
                                                 region.RegionOptStatus === 'ENABLED' || 
                                                 region.RegionOptStatus === 'ENABLED_BY_DEFAULT')) {
                                                isSuccess = true;
                                                successCount++;
                                            }
                                        }
                                    }
                                }
                                
                                if (!isSuccess) {
                                    failedCount++;
                                }

                                // 更新UI显示
                                const row = document.querySelector(`tr[data-account-id="${result.account_id}"]`);
                                if (row) {
                                    // 更新开通结果
                                    const resultCell = row.querySelector('.enable-result');
                                    if (resultCell) {
                                        let statusHtml = '<div class="region-status">';
                                        if (isSuccess) {
                                            statusHtml += '<span class="badge badge-soft-success">开通成功</span>';
                                        } else {
                                            statusHtml += '<span class="badge badge-soft-danger">开通失败</span>';
                                        }
                                        statusHtml += '</div>';
                                        resultCell.innerHTML = statusHtml;
                                    }

                                    // 更新账户状态
                                    try {
                                        const enableResult = JSON.parse(result.enable_result);
                                        if (enableResult.account_status !== null && enableResult.account_status !== undefined) {
                                            const statusCell = row.querySelector('td:nth-child(7)'); // 状态列
                                            if (statusCell) {
                                                let statusHtml = '';
                                                switch (enableResult.account_status) {
                                                    case 0:
                                                        statusHtml = '<span class="badge badge-soft-warning">未测</span>';
                                                        break;
                                                    case 1:
                                                        statusHtml = '<span class="badge badge-soft-success">正常</span>';
                                                        break;
                                                    case 2:
                                                        statusHtml = '<span class="badge badge-soft-danger">封禁</span>';
                                                        break;
                                                    case 3:
                                                        statusHtml = '<span class="badge badge-soft-secondary">无效</span>';
                                                        break;
                                                    default:
                                                        statusHtml = '<span class="badge badge-soft-secondary">未知</span>';
                                                }
                                                statusCell.innerHTML = statusHtml;
                                                
                                                // 添加调试日志
                                                console.log('更新账户状态:', {
                                                    accountId: result.account_id,
                                                    newStatus: enableResult.account_status,
                                                    statusHtml: statusHtml
                                                });
                                            }
                                        }
                                    } catch (error) {
                                        console.error('解析enable_result失败:', error, result.enable_result);
                                    }
                                }
                            } catch (error) {
                                console.error('解析enable_result失败:', error);
                                failedCount++;
                            }
                        }
                    });
                }

                // 处理成功的账户
                if (Array.isArray(data.results.success)) {
                    data.results.success.forEach(result => {
                        if (result && result.account_id) {
                            successCount++;
                            const row = document.querySelector(`tr[data-account-id="${result.account_id}"]`);
                            if (row) {
                                const resultCell = row.querySelector('.enable-result');
                                if (resultCell) {
                                    let statusHtml = '<div class="region-status">';
                                    statusHtml += '<span class="badge badge-soft-success">开通成功</span>';
                                    statusHtml += '</div>';
                                    resultCell.innerHTML = statusHtml;
                                }
                            }
                        }
                    });
                }

                // 显示结果消息
                const message = `开通完成！成功：${successCount}，失败：${failedCount}`;
                alert(message);
                
                // 自动滚动到账户列表
                const tableElement = document.querySelector('.table-responsive');
                if (tableElement) {
                    tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }

            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
        })
        .catch(error => {
            console.error('开通区域失败:', error);
            clearInterval(progressInterval);
            document.getElementById('loadingOverlay').style.display = 'none';
            
            // 检查是否是代理异常错误
            if (error && error.message === 'PROXY_ERROR_STOP') {
                console.log('Catch 中检测到代理异常停止信号');
                // 如果错误是PROXY_ERROR_STOP，则不显示代理异常提示，因为已经在then块中处理
                return;
            }
            
            if (error && error.error_type === 'proxy_error') {
                console.log('Catch 中检测到代理异常');
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', error.message);
                } else {
                    console.error('showProxyToast function not available');
                    alert(error.message);
                }
                return;
            }
            
            // Check error message for proxy strings if error_type is not present
            const errorMessage = error.message || error.toString() || '';
            if (errorMessage.includes('当前免费代理模式IP异常') || errorMessage.includes('当前代理模式IP异常')) {
                console.log('检测到代理异常消息');
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', errorMessage);
                } else {
                    console.error('showProxyToast function not available');
                    alert(errorMessage);
                }
                return;
            }

            alert('操作失败: ' + errorMessage); // Fallback for non-proxy errors
        });
    });

    function showCopyToast(text, target) {
        let toast = document.createElement('div');
        toast.textContent = text;
        toast.style.position = 'fixed';
        toast.style.zIndex = 999999;
        toast.style.background = '#222';
        toast.style.color = '#fff';
        toast.style.padding = '10px 30px';
        toast.style.borderRadius = '8px';
        toast.style.fontSize = '20px';
        toast.style.left = '50vw';
        toast.style.top = '30vh';
        toast.style.opacity = 1;
        toast.style.pointerEvents = 'none';
        document.body.appendChild(toast);
        setTimeout(() => { toast.style.opacity = 0; }, 1800);
        setTimeout(() => { toast.remove(); }, 2200);
    }
    document.querySelector('tbody').addEventListener('click', function(e) {
        if (e.target.classList.contains('copy-cell')) {
            const code = e.target.textContent.trim();
            navigator.clipboard.writeText(code).then(function() {
                showCopyToast('复制成功', e.target);
            });
        }
    });
});
</script>
@endpush