# AWS AMI数据更新指南

## 🎯 更新AMI数据的方法

### 方法1：使用修复脚本（推荐）
```bash
php scripts/fix_ami_config.php
```
- **用途**：从AMI.txt文件重新生成主配置文件
- **输出**：更新 `config/aws_ami_data.php`
- **优点**：自动解析，包含所有33个地区

### 方法2：使用构建脚本
```bash
php scripts/build_complete_ami_config.php
```
- **用途**：从AMI.txt文件生成完整配置
- **输出**：更新 `config/aws_ami_data.php`
- **特点**：与方法1类似，但可能有不同的解析逻辑

### 方法3：使用批处理文件（Windows）
```cmd
build-ami-config.bat
```
- **用途**：Windows环境下的便捷方式
- **实际执行**：调用 `php scripts/build_complete_ami_config.php`

## 📁 配置文件优先级

系统按以下优先级加载AMI数据：

1. **分区域配置文件**（优先级最高）：
   - `config/aws_ami_americas.php` - 美洲地区
   - `config/aws_ami_asia_pacific_1.php` - 亚太地区1
   - `config/aws_ami_asia_pacific_2.php` - 亚太地区2
   - `config/aws_ami_europe.php` - 欧洲地区
   - `config/aws_ami_middle_east.php` - 中东地区
   - `config/aws_ami_africa.php` - 非洲地区

2. **主配置文件**（备用）：
   - `config/aws_ami_data.php` - 包含所有33个地区

3. **硬编码数据**（最后备用）：
   - 控制器中的备用数据

## 🔄 更新流程

### 完整更新流程：
1. **更新AMI.txt文件**：
   ```
   将真实的AMI ID数据放入根目录的AMI.txt文件
   ```

2. **运行更新脚本**：
   ```bash
   php scripts/fix_ami_config.php
   ```

3. **清除缓存**（可选）：
   ```bash
   php artisan cache:clear
   ```

4. **验证更新**：
   - 访问 `/user/aws-ec2/test-ami-config` 查看配置详情
   - 测试单独创建页面：`/user/aws-ec2/create`
   - 测试批量创建页面：`/user/aws-ec2/batch-create`

## 📊 数据格式

### AMI.txt文件格式：
```
地区名称【地区代码】----系统名【版本1（AMI-ID1）、版本2（AMI-ID2）】、系统名2【版本（AMI-ID）】
```

### 示例：
```
美国西部（俄勒冈）【us-west-2】----Amazon Linux【Amazon Linux 2023（ami-0c94855ba95b798c7）、Amazon Linux 2（ami-0c02fb55956c7d316）】、Ubuntu【Ubuntu 22.04 LTS（ami-0c7217cdde317cfec）、Ubuntu 20.04 LTS（ami-0c2d3e23f202824c5）】
```

## ⚠️ 注意事项

1. **AMI ID格式**：必须是 `ami-` 开头 + 17位十六进制字符
2. **地区代码**：必须是有效的AWS地区代码
3. **编码**：AMI.txt文件必须使用UTF-8编码
4. **备份**：更新前会自动备份原配置文件

## 🎉 更新后的效果

- ✅ 单独创建和批量创建页面都会使用新的AMI数据
- ✅ 地区选择后只显示该地区支持的操作系统
- ✅ 系统选择后只显示该系统在当前地区的版本
- ✅ 所有33个AWS地区都得到支持
