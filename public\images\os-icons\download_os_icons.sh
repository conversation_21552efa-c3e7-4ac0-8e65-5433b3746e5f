#!/bin/bash

# 创建目录
mkdir -p temp_icons

# 下载 Red Hat 图标
echo "下载 Red Hat 图标..."
curl -s -o temp_icons/redhat.svg "https://www.redhat.com/themes/custom/rhdc/img/red-hat-logo.svg" || echo "Red Hat 图标下载失败"

# 下载 SUSE 图标
echo "下载 SUSE 图标..."
curl -s -o temp_icons/suse.svg "https://www.suse.com/assets/img/suse-white-logo-green.svg" || echo "SUSE 图标下载失败"

# 下载 Debian 图标
echo "下载 Debian 图标..."
curl -s -o temp_icons/debian.svg "https://www.debian.org/logos/openlogo-nd.svg" || echo "Debian 图标下载失败"

# 下载 CentOS 图标
echo "下载 CentOS 图标..."
curl -s -o temp_icons/centos.svg "https://raw.githubusercontent.com/CentOS/logos/main/web/centos_name_logo.svg" || echo "CentOS 图标下载失败"

# 下载 Oracle Linux 图标
echo "下载 Oracle Linux 图标..."
curl -s -o temp_icons/oracle.svg "https://upload.wikimedia.org/wikipedia/commons/c/c3/Oracle_Logo.svg" || echo "Oracle Linux 图标下载失败"

# 下载 Rocky Linux 图标
echo "下载 Rocky Linux 图标..."
curl -s -o temp_icons/rocky.svg "https://raw.githubusercontent.com/rocky-linux/branding/main/logo/icon-color.svg" || echo "Rocky Linux 图标下载失败"

# 下载 AlmaLinux 图标
echo "下载 AlmaLinux 图标..."
curl -s -o temp_icons/almalinux.svg "https://almalinux.org/images/logo.svg" || echo "AlmaLinux 图标下载失败"

# 转换 SVG 到 PNG 并调整大小为 64x64
echo "转换图标格式并调整大小..."
for icon in temp_icons/*.svg; do
  filename=$(basename "$icon" .svg)
  if [ -f "$icon" ]; then
    echo "处理 $filename 图标..."
    # 使用 ImageMagick 转换 SVG 到 PNG 并调整大小
    convert "$icon" -resize 64x64 -background none "$filename.png" || echo "$filename 图标转换失败"
  fi
done

# 清理临时文件
rm -rf temp_icons

echo "图标下载和处理完成！" 