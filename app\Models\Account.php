<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Account extends Model
{
    protected $fillable = [
        'user_id',
        'username',
        'password',
        'status',
        'expires_at',
        'remarks',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getStatusColorAttribute()
    {
        return [
            'active' => 'success',
            'suspended' => 'warning',
            'expired' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    public function getStatusTextAttribute()
    {
        return [
            'active' => '正常',
            'suspended' => '已暂停',
            'expired' => '已过期',
        ][$this->status] ?? '未知';
    }

    public function getRemainingDaysAttribute()
    {
        if (!$this->expires_at) {
            return null;
        }

        $now = Carbon::now();
        if ($now->gt($this->expires_at)) {
            return 0;
        }

        return $now->diffInDays($this->expires_at);
    }

    public function isExpired()
    {
        return $this->expires_at && Carbon::now()->gt($this->expires_at);
    }

    public function isActive()
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    public function isSuspended()
    {
        return $this->status === 'suspended';
    }
}
