<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作系统图标下载</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
        }
        .icon-item {
            margin: 20px;
            text-align: center;
            width: 150px;
        }
        .icon-item img {
            max-width: 100px;
            max-height: 100px;
        }
        .instructions {
            margin-top: 40px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>操作系统图标下载</h1>
    
    <div class="icon-container">
        <div class="icon-item">
            <h3>Red Hat</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d8/Red_Hat_logo.svg/200px-Red_Hat_logo.svg.png" alt="Red Hat Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/d/d8/Red_Hat_logo.svg" download="redhat.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>SUSE</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/SUSE_Logo.svg/200px-SUSE_Logo.svg.png" alt="SUSE Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/d/d0/SUSE_Logo.svg" download="suse.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>Debian</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/66/Openlogo-debianV2.svg/200px-Openlogo-debianV2.svg.png" alt="Debian Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/6/66/Openlogo-debianV2.svg" download="debian.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>CentOS</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/bf/Centos-logo-light.svg/200px-Centos-logo-light.svg.png" alt="CentOS Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/b/bf/Centos-logo-light.svg" download="centos.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>Oracle Linux</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/Oracle_Logo.svg/200px-Oracle_Logo.svg.png" alt="Oracle Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/c/c3/Oracle_Logo.svg" download="oracle.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>Rocky Linux</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Rocky_Linux_logo.svg/200px-Rocky_Linux_logo.svg.png" alt="Rocky Linux Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/7/77/Rocky_Linux_logo.svg" download="rocky.svg">下载SVG</a></p>
        </div>
        
        <div class="icon-item">
            <h3>AlmaLinux</h3>
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/13/AlmaLinux_Icon_Logo.svg/200px-AlmaLinux_Icon_Logo.svg.png" alt="AlmaLinux Logo">
            <p><a href="https://upload.wikimedia.org/wikipedia/commons/1/13/AlmaLinux_Icon_Logo.svg" download="almalinux.svg">下载SVG</a></p>
        </div>
    </div>
    
    <div class="instructions">
        <h2>图标处理说明</h2>
        <ol>
            <li>点击上方的"下载SVG"链接下载各个操作系统的SVG图标</li>
            <li>使用图像编辑软件（如GIMP、Photoshop或在线工具）将SVG转换为PNG格式</li>
            <li>调整图像大小为64x64像素</li>
            <li>确保背景是透明的</li>
            <li>将处理后的PNG图像保存为对应的文件名（如redhat.png、suse.png等）</li>
        </ol>
        <p>如果您有图像处理软件，可以使用以下命令批量处理图像：</p>
        <pre>convert input.svg -resize 64x64 -background none output.png</pre>
    </div>
</body>
</html> 