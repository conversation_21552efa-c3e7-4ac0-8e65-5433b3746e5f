@extends('admin.layouts.app')

@push('styles')
<style>
.table th {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}
.table td {
    color: #495057;
    vertical-align: middle;
}
.table-centered {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}
.table-centered tr {
    background-color: #fff;
    transition: all 0.3s ease;
}
.table-centered tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,.08);
}
.table-centered td, .table-centered th {
    border: none;
    padding: 1rem;
}
.table-centered td:first-child, .table-centered th:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}
.table-centered td:last-child, .table-centered th:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    letter-spacing: 0.3px;
}
.badge-soft-success {
    color: #fff;
    background-color: #0ab39c;
    border: 1px solid #0ab39c;
}
.badge-soft-new {
    color: #ffffff;
    background-color: red;
    border: 1px solid: red;
}
.badge-soft-danger {
    color: #f06548;
    background-color: rgba(240, 101, 72, 0.18);
    font-weight: 500;
}
.badge-soft-warning {
    color: #fff;
    background-color: #f7b84b;
    border: 1px solid #f7b84b;
}
.badge-soft-info {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.1);
}
.btn-soft-primary {
    color: #5156be;
    background-color: rgba(81,86,190,.1);
    border-color: transparent;
}
.btn-soft-primary:hover {
    color: #fff;
    background-color: #5156be;
}
.btn-soft-danger {
    color: #f06548;
    background-color: rgba(240,101,72,.1);
    border-color: transparent;
}
.btn-soft-danger:hover {
    color: #fff;
    background-color: #f06548;
}
.pagination {
    margin-bottom: 0;
}
.page-link {
    color: #5156be;
    border-radius: 0.25rem;
    margin: 0 0.2rem;
    border: none;
    min-width: 32px;
    text-align: center;
    transition: all 0.3s ease;
}
.page-link:hover {
    color: #4347a5;
    background-color: rgba(81,86,190,.1);
}
.page-item.active .page-link {
    background-color: #5156be;
    border-color: #5156be;
}
.empty-state {
    text-align: center;
    padding: 2rem;
}
.empty-state i {
    font-size: 3rem;
    color: #74788d;
    margin-bottom: 1rem;
}
.empty-state p {
    color: #74788d;
    margin-bottom: 0;
}
.search-box {
    position: relative;
}
.search-box .form-control {
    padding-left: 2.5rem;
}
.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #74788d;
}
.table-tools {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 0 8px rgba(0,0,0,.05);
}
.table-tools .form-select {
    min-width: 120px;
}
</style>
@endpush

@section('content')
<div class="page-header-modern">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title-modern mb-2">会员管理</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="text-secondary">
                            <i class="bi bi-house-door me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="bi bi-people me-1"></i>会员管理
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.users.create') }}" class="btn btn-soft-primary">
                <i class="bi bi-plus-lg me-2"></i>添加会员
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-tools d-flex flex-wrap gap-2 mb-3">
            <div class="d-flex gap-2 flex-grow-1">
                <div class="search-box flex-grow-1">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索会员...">
                </div>
            </div>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm" style="width: auto" onchange="window.location.href='{{ route('admin.users.index') }}?per_page=' + this.value">
                    @foreach([10, 20, 50, 100] as $size)
                        <option value="{{ $size }}" {{ $perPage == $size ? 'selected' : '' }}>{{ $size }}条/页</option>
                    @endforeach
                </select>
            </div>
        </div>

        @if($users->isEmpty())
            <div class="empty-state">
                <i class="bi bi-inbox"></i>
                <p>暂无会员数据</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-centered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>AWS账户数量</th>
                            <th>到期时间</th>
                            <th>到期状态</th>
                            <th>注册时间</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <td>{{ $user->id }}</td>
                                <td>{{ $user->username }}</td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <span class="badge badge-soft-info">
                                            总数：{{ $user->aws_accounts_count ?? 0 }}
                                        </span>
                                        <span class="badge badge-soft-new">
                                            新增：{{ $user->new_accounts_count ?? 0 }}
                                        </span>
                                        <span class="badge badge-soft-success">
                                            正常：{{ $user->normal_accounts_count ?? 0 }}
                                        </span>
                                        <span class="badge badge-soft-warning">
                                            未测试：{{ $user->untested_accounts_count ?? 0 }}
                                        </span>
                                    </div>
                                </td>
                                <td>{{ $user->expires_at ? $user->expires_at->format('Y-m-d H:i:s') : '-' }}</td>
                                <td>
                                    @if($user->expires_at)
                                        @if($user->expires_at->lt(now()))
                                            <span class="badge badge-soft-danger">已过期</span>
                                        @else
                                            @php
                                                $remainingDays = $user->remaining_days;
                                                $displayDays = $remainingDays > 0 && $remainingDays < 1 ? 1 : ceil($remainingDays);
                                            @endphp
                                            <span class="badge badge-soft-success">剩余 {{ $displayDays }} 天</span>
                                        @endif
                                    @else
                                        <span class="badge badge-soft-warning">未设置</span>
                                    @endif
                                </td>
                                <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                                <td>
                                    @if($user->last_login_at)
                                        <span class="text-muted">{{ $user->last_login_at->format('Y-m-d H:i:s') }}</span>
                                    @else
                                        <span class="text-muted">从未登录</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.users.edit', $user) }}" class="btn-action btn-action-primary" title="编辑会员">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form action="{{ route('admin.users.destroy', $user) }}"
                                              method="POST"
                                              onsubmit="return confirm('确定要删除这个会员吗？');"
                                              class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn-action btn-action-danger" title="删除会员">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-end mt-4">
                {{ $users->withQueryString()->links('components.pagination') }}
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            const search = searchInput.value;
            const url = new URL(window.location.href);
            url.searchParams.set('search', search);
            window.location.href = url.toString();
        }, 500);
    });
});
</script>
@endpush 