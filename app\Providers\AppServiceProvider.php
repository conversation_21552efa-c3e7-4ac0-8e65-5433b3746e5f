<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 添加自定义解密指令
        Blade::directive('try_decrypt', function ($expression) {
            return "<?php 
                try {
                    echo $expression ? decrypt($expression) : '';
                } catch (\Exception \$e) {
                    echo '******';
                }
            ?>";
        });
    }
}
