<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统维护中 - {{ \App\Models\Setting::get('site_name', config('app.name')) }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fd;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", "Noto Sans", "Liberation Sans", <PERSON><PERSON>, sans-serif;
        }
        .maintenance-container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
            width: 100%;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        }
        .maintenance-icon {
            font-size: 4rem;
            color: #5156be;
            margin-bottom: 1.5rem;
            animation: spin 4s linear infinite;
        }
        .maintenance-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2b2f4c;
        }
        .maintenance-text {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .admin-link {
            color: #5156be;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }
        .admin-link:hover {
            color: #4347a5;
        }
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="bi bi-gear"></i>
        </div>
        <h1 class="maintenance-title">系统维护中</h1>
        <div class="maintenance-text">
            <p>
                尊敬的用户，我们正在进行系统维护和升级。<br>
                此期间系统暂时无法访问，请稍后再试。
            </p>
            <p>
                预计维护时间：1-2小时<br>
                给您带来不便，敬请谅解。
            </p>
        </div>
        <div>
            <!--<a href="{{ route('admin.login') }}" class="admin-link">
                <i class="bi bi-shield-lock me-1"></i>管理员登录
            </a>-->
        </div>
    </div>
</body>
</html> 