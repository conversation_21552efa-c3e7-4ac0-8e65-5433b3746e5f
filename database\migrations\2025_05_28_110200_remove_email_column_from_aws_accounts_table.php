<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            if (Schema::hasColumn('aws_accounts', 'email')) {
                $table->dropColumn('email');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            if (!Schema::hasColumn('aws_accounts', 'email')) {
                $table->string('email')->nullable();
            }
        });
    }
}; 