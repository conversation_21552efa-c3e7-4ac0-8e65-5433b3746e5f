<?php

namespace App\Policies;

use App\Models\User;
use App\Models\AwsAccount;
use Illuminate\Auth\Access\HandlesAuthorization;

class AwsAccountPolicy
{
    use HandlesAuthorization;

    /**
     * 确定用户是否可以查看AWS账户
     */
    public function view(User $user, AwsAccount $awsAccount)
    {
        return $user->id === $awsAccount->user_id;
    }

    /**
     * 确定用户是否可以更新AWS账户
     */
    public function update(User $user, AwsAccount $awsAccount)
    {
        return $user->id === $awsAccount->user_id;
    }

    /**
     * 确定用户是否可以删除AWS账户
     */
    public function delete(User $user, AwsAccount $awsAccount)
    {
        return $user->id === $awsAccount->user_id;
    }

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function restore(User $user, AwsAccount $awsAccount): bool
    {
        return $user->id === $awsAccount->user_id;
    }

    public function forceDelete(User $user, AwsAccount $awsAccount): bool
    {
        return $user->id === $awsAccount->user_id;
    }
} 