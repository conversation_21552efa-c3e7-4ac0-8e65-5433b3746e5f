<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            // 确保所有必要的字段都存在
            if (!Schema::hasColumn('aws_accounts', 'account_name')) {
                $table->string('account_name');
            }
            if (!Schema::hasColumn('aws_accounts', 'email_password')) {
                $table->string('email_password');
            }
            if (!Schema::hasColumn('aws_accounts', 'aws_password')) {
                $table->string('aws_password');
            }
            if (!Schema::hasColumn('aws_accounts', 'access_key')) {
                $table->string('access_key');
            }
            if (!Schema::hasColumn('aws_accounts', 'secret_key')) {
                $table->string('secret_key');
            }
            if (!Schema::hasColumn('aws_accounts', 'status')) {
                $table->tinyInteger('status')->default(0);
            }
            if (!Schema::hasColumn('aws_accounts', 'user_id')) {
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 不需要做任何事情，因为我们只是确保字段存在
    }
}; 