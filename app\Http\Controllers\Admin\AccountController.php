<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AwsAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;

class AccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 10;
        
        $query = AwsAccount::with('user');

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where('account_name', 'like', "%{$search}%");
        }

        // 用户筛选
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // 排序
        $sort = $request->input('sort', 'id');
        $direction = $request->input('direction', 'desc');
        $query->orderBy($sort, $direction);

        $accounts = $query->paginate($perPage);
        $users = User::all(); // 用于筛选的用户列表

        return view('admin.accounts.index', compact('accounts', 'users', 'perPage'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::all();
        return view('admin.accounts.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'accounts_data' => 'required|string'
        ]);

        $lines = explode("\n", trim($request->accounts_data));
        $createdAccounts = [];
        $errors = [];

        foreach ($lines as $index => $line) {
            try {
                // 解析账户信息
                if (preg_match('/①微软账号：(.*?) ②微软密码：(.*?) ③AWS密码：(.*?) ④访问密钥：(.*?) ⑤秘密访问密钥：(.*)/', $line, $matches)) {
                    [, $microsoftAccount, $microsoftPassword, $awsPassword, $accessKey, $secretKey] = $matches;

                    $account = new AwsAccount([
                        'account_name' => trim($microsoftAccount),
                        'email_password' => trim($microsoftPassword),
                        'aws_password' => trim($awsPassword),
                        'access_key' => trim($accessKey),
                        'secret_key' => trim($secretKey),
                    ]);

                    // 设置受保护的字段
                    $account->user_id = $request->user_id;
                    $account->status = 0;
                    $account->ec2_status = 0;
                    $account->save();

                    $createdAccounts[] = $account;
                } else {
                    $errors[] = "第 " . ($index + 1) . " 行格式不正确";
                }
            } catch (\Exception $e) {
                $errors[] = "第 " . ($index + 1) . " 行处理失败：" . $e->getMessage();
            }
        }

        if (count($errors) > 0) {
            return redirect()
                ->route('admin.accounts.index')
                ->with('error', implode("\n", $errors));
        }

        return redirect()
            ->route('admin.accounts.index')
            ->with('success', '成功添加 ' . count($createdAccounts) . ' 个AWS账户');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AwsAccount $account)
    {
        // 保存当前页面URL到session
        session(['admin_accounts_previous_url' => url()->previous()]);
        
        $users = User::all();
        $statusList = AwsAccount::getStatusList();
        return view('admin.accounts.edit', compact('account', 'users', 'statusList'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AwsAccount $account)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'account_name' => 'required|string|max:255',
            'email_password' => 'nullable|string',
            'aws_password' => 'nullable|string',
            'access_key' => 'required|string|max:255',
            'secret_key' => 'nullable|string',
            'status' => 'required|integer|in:' . implode(',', array_keys(AwsAccount::getStatusList())),
            'quota' => 'nullable|string',
            'last_check_at' => 'nullable|date',
            'remarks' => 'nullable|string|max:1000',
            'aws_account_id' => 'nullable|string|max:255',
            'iam_username' => 'nullable|string|max:255',
            'iam_password' => 'nullable|string',
            'iam_access_key' => 'nullable|string|max:255',
            'iam_secret_key' => 'nullable|string',
            'iam_status' => 'nullable|string|in:success,failed',
        ]);

        $data = $request->all();
        
        // 如果配额为空字符串，设置为null
        if (empty($data['quota'])) {
            $data['quota'] = null;
        }

        // 处理最后测号时间
        if (empty($data['last_check_at'])) {
            $data['last_check_at'] = null;
        }

        $account->update($data);

        // 获取之前保存的URL，如果没有则使用默认列表页
        $previousUrl = session('admin_accounts_previous_url', route('admin.accounts.index'));
        session()->forget('admin_accounts_previous_url');

        return redirect($previousUrl)
            ->with('success', 'AWS账户更新成功');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AwsAccount $account)
    {
        $previousUrl = url()->previous();
        $account->delete();

        return redirect($previousUrl)
            ->with('success', 'AWS账户删除成功');
    }

    /**
     * Batch delete accounts
     */
    public function batchDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:aws_accounts,id'
        ]);

        $previousUrl = url()->previous();
        AwsAccount::whereIn('id', $request->ids)->delete();

        if ($request->wantsJson()) {
        return response()->json(['message' => '账户删除成功']);
        }

        return redirect($previousUrl)
            ->with('success', '账户删除成功');
    }

    /**
     * Get decrypted password
     */
    public function getPassword(Request $request, $id)
    {
        try {
            $parts = explode('_', $id);
            if (count($parts) !== 2) {
                return response()->json(['error' => '无效的请求'], 400);
            }

            $accountId = $parts[0];
            $type = $parts[1];

            $account = AwsAccount::findOrFail($accountId);
            
            $password = match($type) {
                'email' => $account->email_password ? decrypt($account->email_password) : null,
                'aws' => $account->aws_password ? decrypt($account->aws_password) : null,
                'secret' => $account->secret_key ? decrypt($account->secret_key) : null,
                default => null
            };

            if (!$password) {
                return response()->json(['error' => '未设置密码或密钥'], 400);
            }

            return response()->json(['password' => $password]);
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return response()->json(['error' => '解密失败，可能是数据已损坏'], 500);
        } catch (\Exception $e) {
            return response()->json(['error' => '获取密码失败'], 500);
        }
    }
}
