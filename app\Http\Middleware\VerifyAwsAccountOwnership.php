<?php

namespace App\Http\Middleware;

use App\Models\AwsAccount;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class VerifyAwsAccountOwnership
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取路由参数中的AWS账户ID
        $awsAccountId = $request->route('awsAccount');
        
        if ($awsAccountId) {
            // 如果是模型绑定，直接检查
            if ($awsAccountId instanceof AwsAccount) {
                if ($awsAccountId->user_id !== Auth::id()) {
                    abort(403, '您没有权限访问此AWS账户');
                }
            } else {
                // 如果是ID，查询并检查
                $awsAccount = AwsAccount::find($awsAccountId);
                if ($awsAccount && $awsAccount->user_id !== Auth::id()) {
                    abort(403, '您没有权限访问此AWS账户');
                }
            }
        }

        return $next($request);
    }
}
