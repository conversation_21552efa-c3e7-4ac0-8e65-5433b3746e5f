<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE aws_accounts MODIFY email_password MEDIUMTEXT');
        DB::statement('ALTER TABLE aws_accounts MODIFY aws_password MEDIUMTEXT');
        DB::statement('ALTER TABLE aws_accounts MODIFY secret_key MEDIUMTEXT');
        DB::statement('ALTER TABLE aws_accounts MODIFY access_key MEDIUMTEXT');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE aws_accounts MODIFY email_password VARCHAR(255)');
        DB::statement('ALTER TABLE aws_accounts MODIFY aws_password VARCHAR(255)');
        DB::statement('ALTER TABLE aws_accounts MODIFY secret_key VARCHAR(255)');
        DB::statement('ALTER TABLE aws_accounts MODIFY access_key VARCHAR(255)');
    }
}; 