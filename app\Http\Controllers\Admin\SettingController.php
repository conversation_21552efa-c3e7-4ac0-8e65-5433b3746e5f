<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\ProxyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    protected $proxyService;

    public function __construct(ProxyService $proxyService)
    {
        $this->proxyService = $proxyService;
    }

    public function index()
    {
        $settings = Setting::pluck('value', 'key')->all();
        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        try {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'login_title' => 'required|string|max:255',
            'dashboard_title' => 'required|string|max:255',
            ]);

            // 获取所有文本设置项
            $settings = $request->only([
                'site_name',
                'site_description',
                'login_title',
                'dashboard_title',
                'free_proxy_type',
                'free_proxy_host',
                'free_proxy_port',
                'free_proxy_username',
                'free_proxy_password',
            ]);

            // 处理布尔值设置项
            $booleanSettings = [
                'register_enabled',
                'maintenance_mode',
                'free_proxy_enabled'
            ];

            foreach ($booleanSettings as $key) {
                $settings[$key] = $request->has($key) ? '1' : '0';
            }

            // 更新所有设置
            foreach ($settings as $key => $value) {
                Setting::set($key, $value);
        }

        return redirect()
            ->route('admin.settings.index')
            ->with('success', '系统设置已更新');

        } catch (\Exception $e) {
            Log::error('设置更新失败：' . $e->getMessage());

            return redirect()
                ->route('admin.settings.index')
                ->with('error', '设置更新失败，请重试');
        }
    }

    /**
     * 测试免费代理连接
     */
    public function testFreeProxy(Request $request)
    {
        try {
            $request->validate([
                'type' => 'required|in:http,https,socks5',
                'host' => 'required|string',
                'port' => 'required|integer|min:1|max:65535',
                'username' => 'nullable|string',
                'password' => 'nullable|string',
            ]);

            $result = $this->proxyService->testProxy(
                $request->type,
                $request->host,
                $request->port,
                $request->username,
                $request->password
            );

            // 更新免费代理状态
            if ($result['success']) {
                Setting::set('free_proxy_status', 'active');
                Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                Setting::set('free_proxy_error_message', '');
            } else {
                Setting::set('free_proxy_status', 'inactive');
                Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
                Setting::set('free_proxy_error_message', $result['message']);
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('免费代理测试失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新错误状态
            Setting::set('free_proxy_status', 'inactive');
            Setting::set('free_proxy_last_test', now()->format('Y-m-d H:i:s'));
            Setting::set('free_proxy_error_message', $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '测试失败: ' . $e->getMessage()
            ], 500);
        }
    }
}