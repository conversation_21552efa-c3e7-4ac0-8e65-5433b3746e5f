@extends('admin.layouts.app')

@push('styles')
<style>
.table th {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}
.table td {
    color: #495057;
    vertical-align: middle;
}
.table-centered {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}
.table-centered tr {
    background-color: #fff;
    transition: all 0.3s ease;
}
.table-centered tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,.08);
}
.table-centered td, .table-centered th {
    border: none;
    padding: 1rem;
}
.table-centered td:first-child, .table-centered th:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}
.table-centered td:last-child, .table-centered th:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    letter-spacing: 0.3px;
}
.badge-soft-success {
    color: #fff;
    background-color: #0ab39c;
    border: 1px solid #0ab39c;
}
.badge-soft-danger {
    color: #f06548;
    background-color: rgba(240, 101, 72, 0.18);
    font-weight: 500;
}
.badge-soft-warning {
    color: #fff;
    background-color: #f7b84b;
    border: 1px solid #f7b84b;
}
.btn-soft-primary {
    color: #5156be;
    background-color: rgba(81,86,190,.1);
    border-color: transparent;
}
.btn-soft-primary:hover {
    color: #fff;
    background-color: #5156be;
}
.btn-soft-danger {
    color: #f06548;
    background-color: rgba(240,101,72,.1);
    border-color: transparent;
}
.btn-soft-danger:hover {
    color: #fff;
    background-color: #f06548;
}
.pagination {
    margin-bottom: 0;
}
.page-link {
    color: #5156be;
    border-radius: 0.25rem;
    margin: 0 0.2rem;
    border: none;
    min-width: 32px;
    text-align: center;
    transition: all 0.3s ease;
}
.page-link:hover {
    color: #4347a5;
    background-color: rgba(81,86,190,.1);
}
.page-item.active .page-link {
    background-color: #5156be;
    border-color: #5156be;
}
.code-badge {
    font-family: monospace;
    background: #f8f9fa;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    color: #495057;
    border: 1px solid #e9ecef;
}
.empty-state {
    text-align: center;
    padding: 2rem;
}
.empty-state i {
    font-size: 3rem;
    color: #74788d;
    margin-bottom: 1rem;
}
.empty-state p {
    color: #74788d;
    margin-bottom: 0;
}

/* 新增搜索和批量操作样式 */
.search-box {
    position: relative;
}
.search-box .form-control {
    padding-left: 2.5rem;
}
.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #74788d;
}
.table-tools {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 0 8px rgba(0,0,0,.05);
}
.table-tools .form-select {
    min-width: 120px;
}
.checkbox-column {
    width: 40px;
}
.table-centered td.checkbox-column, 
.table-centered th.checkbox-column {
    padding: 1rem 0.5rem;
}

/* 过期天数样式 */
.expired-days {
    color: #f06548;
    font-weight: 500;
}
</style>
@endpush

@section('content')
<!-- 页面标题 -->
<div class="page-header-modern">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title-modern mb-2">激活码管理</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="text-secondary">
                            <i class="bi bi-house-door me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="bi bi-key me-1"></i>激活码管理
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.activation-codes.create') }}" class="btn btn-soft-primary">
                <i class="bi bi-plus-lg me-2"></i>生成激活码
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <!-- 搜索和批量操作工具栏 -->
        <div class="table-tools d-flex flex-wrap gap-2 mb-3">
            <div class="d-flex gap-2 flex-grow-1">
                <div class="search-box flex-grow-1">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索激活码...">
                </div>
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="unused">未使用</option>
                    <option value="used">已使用</option>
                    <option value="expired">已过期</option>
                </select>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-soft-danger" id="batchDelete" disabled>
                    <i class="bi bi-trash me-1"></i> 批量删除
                </button>
                <button type="button" class="btn btn-soft-primary" id="exportSelected" disabled>
                    <i class="bi bi-download me-1"></i> 导出选中
                </button>
            </div>
        </div>

        @if($activationCodes->isEmpty())
            <div class="empty-state">
                <i class="bi bi-inbox"></i>
                <p>暂无激活码数据</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-centered">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>激活码</th>
                            <th>有效期(天)</th>
                            <th>状态</th>
                            <th>使用者</th>
                            <th>生成者</th>
                            <th>生成时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($activationCodes as $code)
                            <tr>
                            <td class="checkbox-column">
                                    <input type="checkbox" class="form-check-input code-checkbox" value="{{ $code->id }}" {{ !$code->canBeDeleted() ? 'disabled' : '' }}>
                            </td>
                            <td>
                                <span class="code-badge">{{ $code->code }}</span>
                            </td>
                                <td>
                                    {{ $code->formatted_remaining_time }}
                                </td>
                                <td>
                                    @if($code->is_used)
                                        <span class="badge badge-soft-warning">已使用</span>
                                @else
                                        <span class="badge badge-soft-success">未使用</span>
                                @endif
                            </td>
                                <td>{{ $code->user ? $code->user->username : '-' }}</td>
                                <td>{{ $code->createdByAdmin->username }}</td>
                                <td>{{ $code->created_at->format('Y-m-d H:i:s') }}</td>
                                <td>
                                    @if($code->canBeDeleted())
                                        <div class="action-buttons">
                                            <button type="button" class="btn-action btn-action-danger delete-code" data-id="{{ $code->id }}" title="删除激活码">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    @endif
                                </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-end mt-4">
                {{ $activationCodes->withQueryString()->links('components.pagination') }}
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // 设置 AJAX 默认的 CSRF token
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.code-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
        updateBatchButtons();
    });

    // 单个复选框变化时更新按钮状态
    $('.code-checkbox').change(function() {
        updateBatchButtons();
    });

    // 更新批量操作按钮状态
    function updateBatchButtons() {
        const checkedCount = $('.code-checkbox:checked').length;
        $('#batchDelete, #exportSelected').prop('disabled', checkedCount === 0);
    }

    // 删除单个激活码
    $('.delete-code').click(function() {
        const id = $(this).data('id');
        if (confirm('确定要删除这个激活码吗？')) {
            $.ajax({
                url: `{{ config('admin.url') }}/activation-codes/${id}`,
                type: 'DELETE',
                success: function(response) {
                    location.reload();
                }
            });
        }
    });

    // 批量删除
    $('#batchDelete').click(function() {
        const ids = $('.code-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (ids.length && confirm('确定要删除选中的激活码吗？')) {
            $.ajax({
                url: '{{ route("admin.activation-codes.batch-destroy") }}',
                type: 'POST',
                data: { ids },
                success: function(response) {
                    location.reload();
                },
                error: function(xhr) {
                    alert('删除失败：' + xhr.responseJSON.message);
                }
            });
        }
    });

    // 导出选中
    $('#exportSelected').click(function() {
        const ids = $('.code-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (ids.length) {
            const form = $('<form>')
                .attr('method', 'POST')
                .attr('action', '{{ route("admin.activation-codes.export") }}');

            // 添加 CSRF token
            $('<input>')
                .attr('type', 'hidden')
                .attr('name', '_token')
                .attr('value', $('meta[name="csrf-token"]').attr('content'))
                .appendTo(form);

            ids.forEach(function(id) {
                $('<input>')
                    .attr('type', 'hidden')
                    .attr('name', 'ids[]')
                    .attr('value', id)
                    .appendTo(form);
            });

            form.appendTo('body').submit().remove();
        }
    });

    // 搜索功能
    $('#searchInput').on('input', function() {
        const searchText = $(this).val().toLowerCase();
        $('tbody tr').each(function() {
            const code = $(this).find('.code-badge').text().toLowerCase();
            $(this).toggle(code.includes(searchText));
        });
    });

    // 状态筛选
    $('#statusFilter').change(function() {
        const status = $(this).val();
        $('tbody tr').each(function() {
            if (!status) {
                $(this).show();
                return;
            }
            const badge = $(this).find('.badge').text();
            const show = (status === 'unused' && badge === '未使用') ||
                        (status === 'used' && badge === '已使用') ||
                        (status === 'expired' && badge === '已过期');
            $(this).toggle(show);
        });
    });
});
</script>
@endpush 