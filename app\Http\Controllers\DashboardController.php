<?php

namespace App\Http\Controllers;

use App\Models\AwsAccount;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    public function index()
    {
        $userId = Auth::id();
        $cacheKey = "dashboard_stats_{$userId}";
        $cacheDuration = now()->addMinutes(5);

        $stats = Cache::remember($cacheKey, $cacheDuration, function () use ($userId) {
            // 账户状态统计
            $totalAccounts = AwsAccount::where('user_id', $userId)->count();
            $normalAccounts = AwsAccount::where('user_id', $userId)->where('status', 1)->count();
            $bannedAccounts = AwsAccount::where('user_id', $userId)->where('status', 2)->count();
            $untestedAccounts = $totalAccounts - $normalAccounts - $bannedAccounts;

            // 账户增长趋势
            $growthStats = AwsAccount::where('user_id', $userId)
                ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => $item->date,
                        'count' => $item->count,
                    ];
                });

            // 计算累计总数
            $total = 0;
            $growthStats = $growthStats->map(function ($item) use (&$total) {
                $total += $item['count'];
                return [
                    'date' => $item['date'],
                    'count' => $total,
                ];
            });

            // 最近添加的账户
            $recentAccounts = AwsAccount::where('user_id', $userId)
                ->latest()
                ->take(5)
                ->get();

            return [
                'totalAccounts' => $totalAccounts,
                'normalAccounts' => $normalAccounts,
                'bannedAccounts' => $bannedAccounts,
                'untestedAccounts' => $untestedAccounts,
                'growthStats' => $growthStats,
                'recentAccounts' => $recentAccounts,
            ];
        });

        return view('dashboard', $stats);
    }

    public function clearCache()
    {
        $userId = Auth::id();
        $cacheKey = "dashboard_stats_{$userId}";
        Cache::forget($cacheKey);
        
        return redirect()->route('dashboard')
            ->with('success', '仪表盘缓存已清除');
    }
} 