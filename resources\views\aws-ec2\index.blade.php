@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 现代化按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.btn-soft-primary:hover,
.btn-soft-primary:focus,
.btn-soft-primary:active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
    box-shadow: none;
}

.btn-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    border: 1px solid rgba(46, 212, 122, 0.2);
}

.btn-soft-success:hover,
.btn-soft-success:focus,
.btn-soft-success:active {
    background-color: #2ed47a;
    color: #fff;
    border-color: #2ed47a;
    box-shadow: none;
}

.btn-soft-info {
    color: #37b9f1;
    background-color: rgba(55, 185, 241, 0.1);
    border: 1px solid rgba(55, 185, 241, 0.2);
}

.btn-soft-info:hover,
.btn-soft-info:focus,
.btn-soft-info:active {
    background-color: #37b9f1;
    color: #fff;
    border-color: #37b9f1;
    box-shadow: none;
}

.btn-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.btn-soft-warning:hover,
.btn-soft-warning:focus,
.btn-soft-warning:active {
    background-color: #ffc107;
    color: #fff;
    border-color: #ffc107;
    box-shadow: none;
}

.btn-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    border: 1px solid rgba(242, 87, 103, 0.2);
}

.btn-soft-danger:hover,
.btn-soft-danger:focus,
.btn-soft-danger:active {
    background-color: #f25767;
    color: #fff;
    border-color: #f25767;
    box-shadow: none;
}

/* 表格样式优化 */
.table {
    margin-bottom: 0;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.badge-soft-info {
    color: #37b9f1;
    background-color: rgba(55, 185, 241, 0.1);
}

.badge-soft-secondary {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
}

/* 账户选择器样式 */
.account-selector {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.account-option {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 80px; /* 固定高度 */
    display: flex;
    align-items: center;
}

.account-option:hover {
    background-color: rgba(67, 97, 238, 0.05);
    border-color: #4361ee;
}

.account-option.selected {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: #4361ee;
}

.account-option .form-check {
    width: 100%;
}

.account-option .form-check-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 50px;
}

.account-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.account-status {
    font-size: 0.75rem;
    color: #718096;
}

/* 账户列表容器 */
.accounts-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    background: white;
}

.accounts-container::-webkit-scrollbar {
    width: 6px;
}

.accounts-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.accounts-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.accounts-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 账户统计信息 */
.accounts-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 0.875rem;
}

.accounts-stats .total-count {
    color: #6c757d;
}

.accounts-stats .selected-count {
    color: #4361ee;
    font-weight: 600;
}

/* 加载遮罩层样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 实例状态样式 */
.instance-running { color: #2ed47a; }
.instance-stopped { color: #f25767; }
.instance-pending { color: #ffc107; }
.instance-stopping { color: #ffc107; }
.instance-terminated { color: #6c757d; }

/* 响应式设计 */
@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
    
    .account-selector {
        padding: 1rem;
    }

    .account-option {
        height: 70px; /* 在小屏幕上稍微减小高度 */
    }

    .account-name {
        font-size: 0.8rem;
    }

    .account-status {
        font-size: 0.7rem;
    }
}

/* 超小屏幕适配 */
@media (max-width: 576px) {
    #accountsList .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .account-option {
        height: 65px;
        padding: 0.5rem;
    }

    .account-name {
        font-size: 0.75rem;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid fade-in">


    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">EC2实例管理</h4>
                            <p class="text-muted mb-0 mt-1">管理您的AWS EC2实例</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <a href="{{ route('aws-ec2.create') }}" class="btn btn-soft-primary">
                            <i class="bi bi-plus-lg me-1"></i>创建实例
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 智能批量选择 -->
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-body py-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1">
                                        <i class="bi bi-lightning-fill text-primary me-1"></i>智能批量选择
                                    </h6>
                                    <small class="text-muted">按时间和数量快速选择账户</small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <!-- 移除单独的批量创建按钮，统一使用下方的创建实例按钮 -->
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-semibold mb-2">时间范围</label>
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="timeRange" id="today" value="today" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="today">
                                            今天 <span class="badge bg-primary ms-1" id="todayCount">0</span>
                                        </label>

                                        <input type="radio" class="btn-check" name="timeRange" id="yesterday" value="yesterday" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="yesterday">
                                            昨天 <span class="badge bg-primary ms-1" id="yesterdayCount">0</span>
                                        </label>

                                        <input type="radio" class="btn-check" name="timeRange" id="dayBefore" value="day_before" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="dayBefore">
                                            前天 <span class="badge bg-primary ms-1" id="dayBeforeCount">0</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-semibold mb-2">选择数量</label>
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="btn-group w-100" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm count-btn" data-count="5">5</button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm count-btn" data-count="10">10</button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm count-btn" data-count="15">15</button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm count-btn" data-count="20">20</button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm count-btn" data-count="all">全部</button>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control form-control-sm" id="customCount" placeholder="自定义" min="1" max="100">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 选择结果显示 -->
                            <div class="mt-3" id="selectionResult" style="display: none;">
                                <div class="alert alert-success py-2 mb-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="bi bi-check-circle-fill me-1"></i>
                                            <span id="selectionText">已选择账户</span>
                                        </div>
                                        <div>
                                            <button class="btn btn-link btn-sm p-0 text-decoration-none" id="viewDetailsBtn">
                                                <i class="bi bi-eye me-1"></i>查看详细
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户选择器 -->
                    <div class="account-selector">
                        <div class="row align-items-center mb-3">
                            <div class="col-md-6">
                                <h6 class="mb-0">选择AWS账户</h6>
                                <small class="text-muted" id="accountSelectorDescription">请先使用智能批量选择功能筛选账户</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="d-flex gap-2 justify-content-end">
                                    <!-- EC2状态筛选 -->
                                    <div class="btn-group">
                                        <button class="btn btn-soft-info btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" id="ec2StatusFilter">
                                            <i class="bi bi-funnel me-1"></i>EC2状态
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" data-filter="all">全部状态</a></li>
                                            <li><a class="dropdown-item" href="#" data-filter="enabled">已开通</a></li>
                                            <li><a class="dropdown-item" href="#" data-filter="not_enabled">未开通</a></li>
                                        </ul>
                                    </div>
                                    <!-- 账户操作按钮 -->
                                    <div class="btn-group">
                                        <button class="btn btn-soft-primary btn-sm" id="selectAllAccounts" disabled>全选</button>
                                        <button class="btn btn-outline-primary btn-sm" id="clearAllAccounts" disabled>清空</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 账户统计信息 -->
                        <div class="accounts-stats">
                            <div class="total-count">
                                当前显示 <span id="totalAccountsCount">0</span> 个账户
                            </div>
                            <div class="selected-count">
                                已选择 <span id="selectedAccountsCount">0</span> 个账户
                            </div>
                        </div>

                        <!-- 账户列表容器 -->
                        <div class="accounts-container">
                            <div class="row" id="accountsList">
                                <!-- 账户将通过智能选择动态加载 -->
                                <div class="col-12 text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-info-circle me-2"></i>
                                        请使用上方的智能批量选择功能来筛选和显示账户
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">选择区域</label>
                                <select class="form-select" id="regionSelect">
                                    <option value="">请选择区域</option>
                                </select>
                            </div>
                            <div class="col-md-8 d-flex align-items-end gap-2">
                                <button class="btn btn-soft-success" id="loadInstancesBtn">
                                    <i class="bi bi-arrow-clockwise me-1"></i>加载实例
                                </button>
                                <button class="btn btn-primary" id="createInstanceBtn">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    <span id="createBtnText">创建实例</span>
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-soft-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-gear me-1"></i>更多操作
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" id="batchLoadInstancesBtn">
                                            <i class="bi bi-arrow-clockwise me-2"></i>批量加载实例
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" id="batchManageInstancesBtn">
                                            <i class="bi bi-gear me-2"></i>批量管理实例
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实例操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mb-4" id="instanceActions">
                        <div class="btn-group">
                            <button class="btn btn-soft-success" id="startInstancesBtn">
                                <i class="bi bi-play-fill me-1"></i>启动
                            </button>
                            <button class="btn btn-soft-warning" id="stopInstancesBtn">
                                <i class="bi bi-stop-fill me-1"></i>停止
                            </button>
                            <button class="btn btn-soft-info" id="rebootInstancesBtn">
                                <i class="bi bi-arrow-clockwise me-1"></i>重启
                            </button>
                            <button class="btn btn-soft-danger" id="terminateInstancesBtn">
                                <i class="bi bi-x-circle me-1"></i>终止
                            </button>
                            <div class="btn-group ms-2">
                                <button class="btn btn-soft-primary" data-bs-toggle="modal" data-bs-target="#manageTagsModal">
                                    <i class="bi bi-tags me-1"></i>标签管理
                                </button>
                                <button class="btn btn-soft-secondary" id="createSnapshotBtn">
                                    <i class="bi bi-camera me-1"></i>创建快照
                                </button>
                            </div>
                        </div>
                        <div class="text-muted">
                            <span id="selectedInstancesCount">0</span> 个实例已选中
                        </div>
                    </div>

                    <!-- 实例列表 -->
                    <div class="table-responsive" id="instancesTableContainer" style="display: none;">
                        <table class="table table-hover" id="instancesTable">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAllInstances">
                                        </div>
                                    </th>
                                    <th>实例ID</th>
                                    <th>账户</th>
                                    <th>状态</th>
                                    <th>实例类型</th>
                                    <th>公网IP</th>
                                    <th>私网IP</th>
                                    <th>可用区</th>
                                    <th>启动时间</th>
                                    <th>标签</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="instancesTableBody">
                            </tbody>
                        </table>
                    </div>

                    <!-- 空状态 -->
                    <div class="text-center py-5" id="emptyState">
                        <i class="bi bi-server" style="font-size: 3rem; color: #e2e8f0;"></i>
                        <h5 class="mt-3 text-muted">请选择账户和区域来加载EC2实例</h5>
                        <p class="text-muted">选择一个或多个AWS账户，然后选择区域来查看实例列表</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量创建实例模态框 -->
<div class="modal fade" id="batchCreateInstanceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">
                    <i class="bi bi-lightning-fill text-primary me-2"></i>批量创建EC2实例
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    批量创建功能将在所有选中的账户中创建相同配置的EC2实例
                </div>

                <form id="batchCreateInstanceForm">
                    <!-- 账户选择显示 -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">选中的账户</label>
                        <div id="selectedAccountsDisplay" class="border rounded p-3 bg-light">
                            <div class="text-muted">请先在上方选择要操作的账户</div>
                        </div>
                    </div>

                    <!-- 基本配置 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">区域</label>
                                <select class="form-select" name="batch_region" required>
                                    <option value="">请选择区域</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">实例数量（每个账户）</label>
                                <input type="number" class="form-control" name="instance_count" value="1" min="1" max="10" required>
                                <small class="text-muted">每个选中的账户将创建指定数量的实例</small>
                            </div>
                        </div>
                    </div>

                    <!-- 实例配置 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">实例类型</label>
                                <select class="form-select" name="batch_instance_type" required>
                                    <option value="">请选择实例类型</option>
                                    <option value="t2.micro">t2.micro (1 vCPU, 1 GB RAM)</option>
                                    <option value="t2.small">t2.small (1 vCPU, 2 GB RAM)</option>
                                    <option value="t2.medium">t2.medium (2 vCPU, 4 GB RAM)</option>
                                    <option value="t3.micro">t3.micro (2 vCPU, 1 GB RAM)</option>
                                    <option value="t3.small">t3.small (2 vCPU, 2 GB RAM)</option>
                                    <option value="t3.medium">t3.medium (2 vCPU, 4 GB RAM)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">AMI ID</label>
                                <input type="text" class="form-control" name="batch_ami_id" placeholder="ami-xxxxxxxxx" required>
                                <small class="text-muted">Amazon Machine Image ID</small>
                            </div>
                        </div>
                    </div>

                    <!-- 安全组和密钥对 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">安全组ID</label>
                                <input type="text" class="form-control" name="batch_security_group" placeholder="sg-xxxxxxxxx">
                                <small class="text-muted">留空使用默认安全组</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">密钥对名称</label>
                                <input type="text" class="form-control" name="batch_key_name" placeholder="my-key-pair">
                                <small class="text-muted">用于SSH访问的密钥对</small>
                            </div>
                        </div>
                    </div>

                    <!-- 标签 -->
                    <div class="mb-3">
                        <label class="form-label">实例标签</label>
                        <input type="text" class="form-control" name="batch_instance_name" placeholder="BatchInstance" value="BatchInstance">
                        <small class="text-muted">实例的Name标签</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchCreateBtn">
                    <i class="bi bi-lightning-fill me-1"></i>开始批量创建
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 创建实例模态框 -->
<div class="modal fade" id="createInstanceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">创建EC2实例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createInstanceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">选择账户</label>
                                <select class="form-select" name="account_id" required>
                                    <option value="">请选择账户</option>
                                    @foreach($accounts as $account)
                                    <option value="{{ $account->id }}">{{ $account->account_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">区域</label>
                                <select class="form-select" name="region" required>
                                    <option value="">请选择区域</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">实例名称</label>
                                <input type="text" class="form-control" name="instance_name" placeholder="输入实例名称" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">实例数量</label>
                                <input type="number" class="form-control" name="instance_count" value="1" min="1" max="20" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">操作系统</label>
                        <select class="form-select" name="os_type" required>
                            <option value="">选择操作系统</option>
                            <option value="Amazon Linux">Amazon Linux</option>
                            <option value="Ubuntu">Ubuntu</option>
                            <option value="Windows">Windows</option>
                            <option value="CentOS">CentOS</option>
                            <option value="Red Hat">Red Hat</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">AMI</label>
                        <select class="form-select" name="ami_id" required>
                            <option value="">先选择操作系统</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">实例类型</label>
                        <select class="form-select" name="instance_type" required>
                            <option value="">请选择实例类型</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">密钥对</label>
                                <select class="form-select" name="key_name">
                                    <option value="">选择密钥对 (可选)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">子网</label>
                                <select class="form-select" name="subnet_id">
                                    <option value="">选择子网 (可选)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3" id="passwordSection">
                        <label class="form-label" id="passwordLabel">系统密码</label>
                        <input type="password" class="form-control" name="password" minlength="8" placeholder="设置系统登录密码">
                        <div class="form-text" id="passwordHelp">密码至少8位，用于系统登录</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用户数据脚本 (可选)</label>
                        <textarea class="form-control" name="user_data" rows="4" placeholder="输入启动脚本或配置命令"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">标签 (可选)</label>
                        <div id="tagsContainer">
                            <div class="row mb-2">
                                <div class="col-5">
                                    <input type="text" class="form-control" placeholder="标签键" name="tag_keys[]">
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" placeholder="标签值" name="tag_values[]">
                                </div>
                                <div class="col-2">
                                    <button type="button" class="btn btn-soft-primary" id="addTagBtn">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="createInstanceBtn">创建实例</button>
            </div>
        </div>
    </div>
</div>

<!-- 标签管理模态框 -->
<div class="modal fade" id="manageTagsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">标签管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">操作类型</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="tagAction" id="addTags" value="add" checked>
                        <label class="btn btn-outline-primary" for="addTags">添加标签</label>

                        <input type="radio" class="btn-check" name="tagAction" id="removeTags" value="remove">
                        <label class="btn btn-outline-danger" for="removeTags">删除标签</label>
                    </div>
                </div>

                <div id="tagManageContainer">
                    <div class="row mb-2">
                        <div class="col-5">
                            <input type="text" class="form-control" placeholder="标签键" name="manage_tag_keys[]">
                        </div>
                        <div class="col-5">
                            <input type="text" class="form-control" placeholder="标签值" name="manage_tag_values[]">
                        </div>
                        <div class="col-2">
                            <button type="button" class="btn btn-soft-primary" id="addManageTagBtn">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="executeTagManageBtn">执行操作</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载进度条 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 class="mb-3" id="loadingText">正在处理中...</h5>
        <p class="mb-0" id="progressText">请稍候...</p>
    </div>
</div>

@push('scripts')
<script>
// AWS官方固定区域数据（与创建页面保持一致）
const awsRegions = {
    // 美洲地区
    'us-east-1': '美国东部（弗吉尼亚北部）',
    'us-east-2': '美国东部（俄亥俄）',
    'us-west-1': '美国西部（加利福尼亚北部）',
    'us-west-2': '美国西部（俄勒冈）',
    'us-gov-east-1': 'AWS GovCloud（美国东部）',
    'us-gov-west-1': 'AWS GovCloud（美国西部）',
    'ca-central-1': '加拿大（中部）',
    'ca-west-1': '加拿大（卡尔加里）',
    'sa-east-1': '南美洲（圣保罗）',
    'mx-central-1': '墨西哥（中部）',

    // 欧洲地区
    'eu-west-1': '欧洲（爱尔兰）',
    'eu-west-2': '欧洲（伦敦）',
    'eu-west-3': '欧洲（巴黎）',
    'eu-central-1': '欧洲（法兰克福）',
    'eu-central-2': '欧洲（苏黎世）',
    'eu-north-1': '欧洲（斯德哥尔摩）',
    'eu-south-1': '欧洲（米兰）',
    'eu-south-2': '欧洲（西班牙）',

    // 亚太地区
    'ap-southeast-1': '亚太地区（新加坡）',
    'ap-southeast-2': '亚太地区（悉尼）',
    'ap-southeast-3': '亚太地区（雅加达）',
    'ap-southeast-4': '亚太地区（墨尔本）',
    'ap-southeast-5': '亚太地区（马来西亚）',
    'ap-southeast-7': '亚太地区（泰国）',
    'ap-northeast-1': '亚太地区（东京）',
    'ap-northeast-2': '亚太地区（首尔）',
    'ap-northeast-3': '亚太地区（大阪）',
    'ap-south-1': '亚太地区（孟买）',
    'ap-south-2': '亚太地区（海得拉巴）',
    'ap-east-1': '亚太地区（香港）',

    // 中东和非洲
    'me-south-1': '中东（巴林）',
    'me-central-1': '中东（阿联酋）',
    'af-south-1': '非洲（开普敦）',
    'il-central-1': '中东（特拉维夫）'
};

document.addEventListener('DOMContentLoaded', function() {
    let selectedInstances = [];
    let allInstances = [];
    let smartSelectedAccounts = []; // 智能选择的账户列表

    // 初始化
    loadRegions();
    initSmartBatchSelection();
    loadInstanceTypes();
    initEc2StatusFilter();

    // 账户选择功能
    document.getElementById('selectAllAccounts').addEventListener('click', function() {
        document.querySelectorAll('.account-checkbox').forEach(checkbox => {
            // 只选择未被禁用的账户（排除无效、封禁状态的账户）
            if (!checkbox.disabled) {
                checkbox.checked = true;
                checkbox.closest('.account-option').classList.add('selected');
            }
        });
        updateAccountsCount();
    });

    document.getElementById('clearAllAccounts').addEventListener('click', function() {
        document.querySelectorAll('.account-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            checkbox.closest('.account-option').classList.remove('selected');
        });
        updateAccountsCount();
    });

    // 更新账户计数
    function updateAccountsCount() {
        const selectedCount = document.querySelectorAll('.account-checkbox:checked').length;
        document.getElementById('selectedAccountsCount').textContent = selectedCount;

        // 更新描述文本显示已选择的账户数量
        const descriptionElement = document.getElementById('accountSelectorDescription');
        if (descriptionElement) {
            const filterText = getFilterDisplayText();
            descriptionElement.textContent = `${filterText} ${selectedCount} 个账户`;
        }

        // 更新创建按钮文本
        updateCreateButtonText(selectedCount);
    }

    // 更新创建按钮文本
    function updateCreateButtonText(selectedCount) {
        const createBtnText = document.getElementById('createBtnText');
        const createBtn = document.getElementById('createInstanceBtn');

        if (selectedCount === 0) {
            createBtnText.textContent = '创建实例';
            createBtn.disabled = true;
            createBtn.classList.add('disabled');
        } else if (selectedCount === 1) {
            createBtnText.textContent = '创建实例';
            createBtn.disabled = false;
            createBtn.classList.remove('disabled');
        } else {
            createBtnText.textContent = `批量创建实例 (${selectedCount}个账户)`;
            createBtn.disabled = false;
            createBtn.classList.remove('disabled');
        }
    }

    // 账户选项点击事件
    document.querySelectorAll('.account-option').forEach(option => {
        option.addEventListener('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = this.querySelector('.account-checkbox');
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);
                updateAccountsCount();
            } else {
                this.classList.toggle('selected', e.target.checked);
                updateAccountsCount();
            }
        });
    });

    // 加载实例
    document.getElementById('loadInstancesBtn').addEventListener('click', function() {
        const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);
        const selectedRegion = document.getElementById('regionSelect').value;

        if (selectedAccounts.length === 0) {
            alert('请至少选择一个账户');
            return;
        }

        if (!selectedRegion) {
            alert('请选择区域');
            return;
        }

        loadInstances(selectedAccounts, selectedRegion);
    });

    // 批量加载实例
    document.getElementById('batchLoadInstancesBtn').addEventListener('click', function() {
        const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);

        if (selectedAccounts.length === 0) {
            alert('请选择至少一个账户');
            return;
        }

        if (selectedAccounts.length < 2) {
            alert('批量操作需要选择至少2个账户');
            return;
        }

        // 获取所有可用区域并批量加载
        batchLoadAllRegions(selectedAccounts);
    });

    // 移除旧的批量创建按钮事件（已整合到统一的创建实例按钮中）

    // 批量管理实例
    document.getElementById('batchManageInstancesBtn').addEventListener('click', function() {
        const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);

        if (selectedAccounts.length === 0) {
            alert('请选择至少一个账户');
            return;
        }

        // 加载所有选中账户的实例进行统一管理
        batchManageInstances(selectedAccounts);
    });

    // 实例操作按钮
    document.getElementById('startInstancesBtn').addEventListener('click', () => manageInstances('start'));
    document.getElementById('stopInstancesBtn').addEventListener('click', () => manageInstances('stop'));
    document.getElementById('rebootInstancesBtn').addEventListener('click', () => manageInstances('reboot'));
    document.getElementById('terminateInstancesBtn').addEventListener('click', () => manageInstances('terminate'));

    // 全选实例
    document.getElementById('selectAllInstances').addEventListener('change', function() {
        document.querySelectorAll('.instance-checkbox').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedInstances();
    });

    // 创建实例 - 智能切换单个/批量模式
    document.getElementById('createInstanceBtn').addEventListener('click', function() {
        const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);

        if (selectedAccounts.length === 0) {
            alert('请先选择账户');
            return;
        }

        if (selectedAccounts.length === 1) {
            // 单个账户模式 - 跳转到创建页面
            window.location.href = '{{ route("aws-ec2.create") }}?account_id=' + selectedAccounts[0];
        } else {
            // 批量模式 - 跳转到批量创建页面
            const accountIds = selectedAccounts.join(',');
            window.location.href = `{{ route('aws-ec2.batch-create') }}?accounts=${accountIds}`;
        }
    });

    // 批量创建实例确认
    document.getElementById('confirmBatchCreateBtn').addEventListener('click', function() {
        const form = document.getElementById('batchCreateInstanceForm');
        const formData = new FormData(form);
        const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);

        // 验证表单
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        if (selectedAccounts.length === 0) {
            alert('请选择至少一个账户');
            return;
        }

        const batchData = {
            account_ids: selectedAccounts,
            region: formData.get('batch_region'),
            instance_type: formData.get('batch_instance_type'),
            ami_id: formData.get('batch_ami_id'),
            instance_count: parseInt(formData.get('instance_count')),
            security_group: formData.get('batch_security_group'),
            key_name: formData.get('batch_key_name'),
            instance_name: formData.get('batch_instance_name')
        };

        if (!confirm(`确定要在 ${selectedAccounts.length} 个账户中各创建 ${batchData.instance_count} 个实例吗？\n总共将创建 ${selectedAccounts.length * batchData.instance_count} 个实例。`)) {
            return;
        }

        batchCreateInstances(batchData);
    });

    // 创建快照
    document.getElementById('createSnapshotBtn').addEventListener('click', createSnapshot);

    // 标签管理
    document.getElementById('executeTagManageBtn').addEventListener('click', executeTagManage);

    // 添加标签功能
    document.getElementById('addTagBtn').addEventListener('click', function() {
        const container = document.getElementById('tagsContainer');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2';
        newRow.innerHTML = `
            <div class="col-5">
                <input type="text" class="form-control" placeholder="标签键" name="tag_keys[]">
            </div>
            <div class="col-5">
                <input type="text" class="form-control" placeholder="标签值" name="tag_values[]">
            </div>
            <div class="col-2">
                <button type="button" class="btn btn-soft-danger remove-tag-btn">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(newRow);

        // 添加删除按钮事件
        newRow.querySelector('.remove-tag-btn').addEventListener('click', function() {
            newRow.remove();
        });
    });

    // 标签管理添加按钮
    document.getElementById('addManageTagBtn').addEventListener('click', function() {
        const container = document.getElementById('tagManageContainer');
        const newRow = document.createElement('div');
        newRow.className = 'row mb-2';
        newRow.innerHTML = `
            <div class="col-5">
                <input type="text" class="form-control" placeholder="标签键" name="manage_tag_keys[]">
            </div>
            <div class="col-5">
                <input type="text" class="form-control" placeholder="标签值" name="manage_tag_values[]">
            </div>
            <div class="col-2">
                <button type="button" class="btn btn-soft-danger remove-manage-tag-btn">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(newRow);

        // 添加删除按钮事件
        newRow.querySelector('.remove-manage-tag-btn').addEventListener('click', function() {
            newRow.remove();
        });
    });

    // 加载区域列表（使用固定数据）
    function loadRegions() {
        const regionSelects = document.querySelectorAll('select[name="region"], #regionSelect');
        regionSelects.forEach(select => {
            select.innerHTML = '<option value="">请选择区域</option>';
            Object.entries(awsRegions).forEach(([code, name]) => {
                select.innerHTML += `<option value="${code}">${name}</option>`;
            });
        });
    }

    // 加载实例类型（带前端缓存）
    function loadInstanceTypes() {
        // 检查前端缓存（30分钟）
        const cacheKey = 'aws_instance_types';
        const cacheTime = 30 * 60 * 1000; // 30分钟
        const cached = localStorage.getItem(cacheKey);
        const cacheTimestamp = localStorage.getItem(cacheKey + '_timestamp');

        if (cached && cacheTimestamp && (Date.now() - parseInt(cacheTimestamp)) < cacheTime) {
            console.log('🚀 使用缓存的实例类型数据');
            const types = JSON.parse(cached);
            populateInstanceTypes(types);
            return;
        }

        console.log('📡 从服务器加载实例类型数据');
        fetch('{{ route("aws-ec2.instance-types") }}')
            .then(response => response.json())
            .then(types => {
                // 缓存到本地存储
                localStorage.setItem(cacheKey, JSON.stringify(types));
                localStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
                populateInstanceTypes(types);
            })
            .catch(error => {
                console.error('加载实例类型失败:', error);
            });
    }

    // 填充实例类型选择框
    function populateInstanceTypes(types) {
        const typeSelect = document.querySelector('select[name="instance_type"]');
        typeSelect.innerHTML = '<option value="">请选择实例类型</option>';
        types.forEach(type => {
            typeSelect.innerHTML += `<option value="${type.name}">${type.name} - ${type.specs}</option>`;
        });
    }

    // 加载AMI列表
    function loadAmis(region) {
        fetch(`{{ route("aws-ec2.amis") }}?region=${region}`)
            .then(response => response.json())
            .then(amis => {
                window.availableAmis = amis;
                updateAmiOptions();
            });
    }

    // 更新AMI选项
    function updateAmiOptions() {
        const osType = document.querySelector('select[name="os_type"]').value;
        const amiSelect = document.querySelector('select[name="ami_id"]');

        if (!osType) {
            amiSelect.innerHTML = '<option value="">先选择操作系统</option>';
            return;
        }

        amiSelect.innerHTML = '<option value="">选择AMI</option>';
        if (window.availableAmis && window.availableAmis[osType]) {
            Object.entries(window.availableAmis[osType]).forEach(([id, name]) => {
                amiSelect.innerHTML += `<option value="${id}">${name}</option>`;
            });
        }
    }

    // 加载密钥对
    function loadKeyPairs(accountId, region) {
        if (!accountId || !region) return;

        fetch('{{ route("aws-ec2.key-pairs") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    account_id: accountId,
                    region: region
                })
            })
            .then(response => response.json())
            .then(keyPairs => {
                const keySelect = document.querySelector('select[name="key_name"]');
                keySelect.innerHTML = '<option value="">选择密钥对 (可选)</option>';
                Object.entries(keyPairs).forEach(([name, displayName]) => {
                    keySelect.innerHTML += `<option value="${name}">${displayName}</option>`;
                });
            })
            .catch(error => {
                console.error('加载密钥对失败:', error);
            });
    }

    // 加载子网
    function loadSubnets(accountId, region) {
        if (!accountId || !region) return;

        fetch('{{ route("aws-ec2.subnets") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    account_id: accountId,
                    region: region
                })
            })
            .then(response => response.json())
            .then(data => {
                // 检查是否是代理异常错误
                if (data.error_type === 'proxy_error') {
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', data.message);
                    } else {
                        alert(data.message);
                    }
                    return;
                }

                if (data.success) {
                    const subnetSelect = document.querySelector('select[name="subnet_id"]');
                    subnetSelect.innerHTML = '<option value="">选择子网 (可选)</option>';
                    Object.entries(data.data).forEach(([id, name]) => {
                        subnetSelect.innerHTML += `<option value="${id}">${name}</option>`;
                    });
                } else {
                    console.error('加载子网失败:', data.message);
                }
            })
            .catch(error => {
                console.error('加载子网失败:', error);
                // 检查是否是代理异常错误（通过响应状态码判断）
                if (error && error.error_type === 'proxy_error') {
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', error.message);
                    } else {
                        alert(error.message);
                    }
                    return;
                }
            });
    }

    // 加载实例列表
    function loadInstances(accountIds, region) {
        showLoading('正在加载实例列表...');

        fetch('{{ route("aws-ec2.instances") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                account_ids: accountIds,
                region: region
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            // 检查是否是代理异常错误
            if (data.error_type === 'proxy_error') {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', data.message);
                } else {
                    alert(data.message);
                }
                return;
            }
            
            if (data.success) {
                allInstances = data.instances;
                displayInstances(data.instances);
            } else {
                alert('加载失败: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading();
            
            // 检查是否是代理异常错误
            if (error && error.error_type === 'proxy_error') {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', error.message);
                } else {
                    alert(error.message);
                }
                return;
            }
            
            alert('加载失败: ' + error.message);
        });
    }

    // 显示实例列表
    function displayInstances(instances) {
        const tbody = document.getElementById('instancesTableBody');
        const emptyState = document.getElementById('emptyState');
        const tableContainer = document.getElementById('instancesTableContainer');
        const actionsContainer = document.getElementById('instanceActions');

        if (instances.length === 0) {
            tableContainer.style.display = 'none';
            // 不移除操作按钮的显示，让它们始终可见
            emptyState.innerHTML = `
                <i class="bi bi-server" style="font-size: 3rem; color: #e2e8f0;"></i>
                <h5 class="mt-3 text-muted">未找到EC2实例</h5>
                <p class="text-muted">所选账户和区域中没有EC2实例</p>
            `;
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        tableContainer.style.display = 'block';
        // 确保操作按钮始终显示
        actionsContainer.style.display = 'flex';

        tbody.innerHTML = '';
        instances.forEach(instance => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input instance-checkbox"
                               value="${instance.instance_id}"
                               data-account-id="${instance.account_id}"
                               data-region="${instance.region}">
                    </div>
                </td>
                <td><code>${instance.instance_id}</code></td>
                <td>${instance.account_name}</td>
                <td><span class="badge badge-soft-${getStatusColor(instance.state)}">${instance.state}</span></td>
                <td>${instance.instance_type}</td>
                <td>${instance.public_ip}</td>
                <td>${instance.private_ip}</td>
                <td>${instance.availability_zone}</td>
                <td>${instance.launch_time}</td>
                <td><small>${instance.tags || 'N/A'}</small></td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-soft-primary btn-sm" onclick="viewInstanceDetails('${instance.instance_id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        // 添加实例选择事件
        document.querySelectorAll('.instance-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedInstances);
        });
    }

    // 更新选中的实例
    function updateSelectedInstances() {
        selectedInstances = Array.from(document.querySelectorAll('.instance-checkbox:checked')).map(cb => ({
            instance_id: cb.value,
            account_id: cb.dataset.accountId,
            region: cb.dataset.region
        }));

        document.getElementById('selectedInstancesCount').textContent = selectedInstances.length;
    }

    // 批量加载所有区域的实例
    function batchLoadAllRegions(accountIds) {
        showLoading('正在批量加载所有区域的实例...');

        const regions = ['us-east-1', 'us-west-1', 'us-west-2', 'eu-west-1', 'eu-central-1', 'ap-southeast-1', 'ap-northeast-1'];
        let allInstancesData = [];
        let completedRequests = 0;
        let hasProxyError = false;

        regions.forEach(region => {
            fetch('{{ route("aws-ec2.instances") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    account_ids: accountIds,
                    region: region
                })
            })
            .then(response => response.json())
            .then(data => {
                completedRequests++;
                
                // 检查是否是代理异常错误
                if (data.error_type === 'proxy_error') {
                    hasProxyError = true;
                    hideLoading();
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', data.message);
                    } else {
                        alert(data.message);
                    }
                    return;
                }
                
                if (data.success && data.instances.length > 0) {
                    allInstancesData = allInstancesData.concat(data.instances);
                }

                if (completedRequests === regions.length && !hasProxyError) {
                    hideLoading();
                    allInstances = allInstancesData;
                    displayInstances(allInstancesData);

                    if (allInstancesData.length === 0) {
                        alert('在所有区域中都没有找到实例');
                    } else {
                        alert(`成功加载了 ${allInstancesData.length} 个实例（来自 ${regions.length} 个区域）`);
                    }
                }
            })
            .catch(error => {
                completedRequests++;
                console.error(`加载区域 ${region} 失败:`, error);
                
                // 检查是否是代理异常错误
                if (error && error.error_type === 'proxy_error') {
                    hasProxyError = true;
                    hideLoading();
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', error.message);
                    } else {
                        alert(error.message);
                    }
                    return;
                }

                if (completedRequests === regions.length && !hasProxyError) {
                    hideLoading();
                    allInstances = allInstancesData;
                    displayInstances(allInstancesData);
                }
            });
        });
    }

    // 更新选中账户显示
    function updateSelectedAccountsDisplay(accountIds) {
        const accountsDisplay = document.getElementById('selectedAccountsDisplay');
        const accountElements = accountIds.map(id => {
            const accountOption = document.querySelector(`[data-account-id="${id}"]`);
            const accountName = accountOption ? accountOption.querySelector('.account-name').textContent : `账户 ${id}`;
            return `<span class="badge bg-primary me-2 mb-2">${accountName}</span>`;
        }).join('');

        accountsDisplay.innerHTML = `
            <div class="mb-2"><strong>已选择 ${accountIds.length} 个账户：</strong></div>
            ${accountElements}
        `;
    }

    // 加载批量创建的区域选项
    function loadRegionsForBatch() {
        const regionSelect = document.querySelector('#batchCreateInstanceModal select[name="batch_region"]');
        regionSelect.innerHTML = '<option value="">请选择区域</option>';

        const regions = [
            { value: 'us-east-1', text: '美国东部 (弗吉尼亚北部) - us-east-1' },
            { value: 'us-west-1', text: '美国西部 (加利福尼亚北部) - us-west-1' },
            { value: 'us-west-2', text: '美国西部 (俄勒冈) - us-west-2' },
            { value: 'eu-west-1', text: '欧洲 (爱尔兰) - eu-west-1' },
            { value: 'eu-central-1', text: '欧洲 (法兰克福) - eu-central-1' },
            { value: 'ap-southeast-1', text: '亚太地区 (新加坡) - ap-southeast-1' },
            { value: 'ap-northeast-1', text: '亚太地区 (东京) - ap-northeast-1' }
        ];

        regions.forEach(region => {
            const option = document.createElement('option');
            option.value = region.value;
            option.textContent = region.text;
            regionSelect.appendChild(option);
        });
    }

    // 批量管理实例
    function batchManageInstances(accountIds) {
        // 自动加载所有区域的实例并显示管理界面
        batchLoadAllRegions(accountIds);
    }

    // 批量创建实例
    function batchCreateInstances(batchData) {
        showLoading('正在批量创建实例...');

        fetch('{{ route("aws-ec2.batch-create-instances") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(batchData)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('batchCreateInstanceModal')).hide();

                // 显示结果
                let message = `批量创建完成！\n`;
                message += `成功: ${data.results.success.length} 个账户\n`;
                message += `失败: ${data.results.failed.length} 个账户\n`;

                if (data.results.failed.length > 0) {
                    message += `\n失败详情:\n`;
                    data.results.failed.forEach(fail => {
                        message += `- 账户 ${fail.account_id}: ${fail.error}\n`;
                    });
                }

                alert(message);

                // 如果有成功的，刷新实例列表
                if (data.results.success.length > 0) {
                    const selectedRegion = document.getElementById('regionSelect').value;
                    if (selectedRegion) {
                        loadInstances(batchData.account_ids, selectedRegion);
                    }
                }
            } else {
                alert('批量创建失败: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading();
            alert('批量创建失败: ' + error.message);
        });
    }

    // 获取状态颜色
    function getStatusColor(state) {
        const colors = {
            'running': 'success',
            'stopped': 'danger',
            'pending': 'warning',
            'stopping': 'warning',
            'terminated': 'secondary',
            'shutting-down': 'warning'
        };
        return colors[state] || 'secondary';
    }

    // 管理实例状态
    function manageInstances(action) {
        if (selectedInstances.length === 0) {
            alert('请选择要操作的实例');
            return;
        }

        const actionNames = {
            'start': '启动',
            'stop': '停止',
            'reboot': '重启',
            'terminate': '终止'
        };

        if (!confirm(`确定要${actionNames[action]}选中的 ${selectedInstances.length} 个实例吗？`)) {
            return;
        }

        showLoading(`正在${actionNames[action]}实例...`);

        const actionRoutes = {
            'start': '{{ route("aws-ec2.start-instances") }}',
            'stop': '{{ route("aws-ec2.stop-instances") }}',
            'reboot': '{{ route("aws-ec2.reboot-instances") }}',
            'terminate': '{{ route("aws-ec2.terminate-instances") }}'
        };

        fetch(actionRoutes[action], {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                instances: selectedInstances
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            alert(data.message);
            if (data.success) {
                // 重新加载实例列表
                const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);
                const selectedRegion = document.getElementById('regionSelect').value;
                loadInstances(selectedAccounts, selectedRegion);
            }
        })
        .catch(error => {
            hideLoading();
            alert('操作失败: ' + error.message);
        });
    }

    // 创建实例
    function createInstance() {
        const form = document.getElementById('createInstanceForm');
        const formData = new FormData(form);

        // 处理标签
        const tagKeys = formData.getAll('tag_keys[]').filter(key => key.trim());
        const tagValues = formData.getAll('tag_values[]').filter(value => value.trim());
        const tags = {};
        tagKeys.forEach((key, index) => {
            if (key.trim() && tagValues[index]) {
                tags[key.trim()] = tagValues[index].trim();
            }
        });

        const data = {
            account_id: formData.get('account_id'),
            region: formData.get('region'),
            ami_id: formData.get('ami_id'),
            instance_type: formData.get('instance_type'),
            key_name: formData.get('key_name'),
            subnet_id: formData.get('subnet_id'),
            min_count: parseInt(formData.get('min_count')),
            max_count: parseInt(formData.get('max_count')),
            tags: tags
        };

        showLoading('正在创建实例...');

        fetch('{{ route("aws-ec2.create-instance") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            alert(data.message);
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('createInstanceModal')).hide();
                form.reset();
                // 重新加载实例列表
                const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);
                const selectedRegion = document.getElementById('regionSelect').value;
                if (selectedAccounts.length > 0 && selectedRegion) {
                    loadInstances(selectedAccounts, selectedRegion);
                }
            }
        })
        .catch(error => {
            hideLoading();
            alert('创建失败: ' + error.message);
        });
    }

    // 显示加载动画
    function showLoading(text = '正在处理中...') {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // 隐藏加载动画
    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // 创建快照
    function createSnapshot() {
        if (selectedInstances.length === 0) {
            alert('请选择要创建快照的实例');
            return;
        }

        const description = prompt('请输入快照描述（可选）：');
        if (description === null) return; // 用户取消

        showLoading('正在创建快照...');

        fetch('{{ route("aws-ec2.create-snapshot") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                instances: selectedInstances,
                description: description
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            alert(data.message);
        })
        .catch(error => {
            hideLoading();
            alert('创建快照失败: ' + error.message);
        });
    }

    // 执行标签管理
    function executeTagManage() {
        if (selectedInstances.length === 0) {
            alert('请选择要管理标签的实例');
            return;
        }

        const action = document.querySelector('input[name="tagAction"]:checked').value;
        const tagKeys = Array.from(document.querySelectorAll('input[name="manage_tag_keys[]"]')).map(input => input.value.trim()).filter(key => key);
        const tagValues = Array.from(document.querySelectorAll('input[name="manage_tag_values[]"]')).map(input => input.value.trim()).filter(value => value);

        if (tagKeys.length === 0) {
            alert('请至少添加一个标签');
            return;
        }

        const tags = {};
        tagKeys.forEach((key, index) => {
            if (key && tagValues[index]) {
                tags[key] = tagValues[index];
            }
        });

        if (Object.keys(tags).length === 0) {
            alert('请确保标签键值对都已填写');
            return;
        }

        const actionText = action === 'add' ? '添加' : '删除';
        if (!confirm(`确定要${actionText}选中实例的标签吗？`)) {
            return;
        }

        showLoading(`正在${actionText}标签...`);

        fetch('{{ route("aws-ec2.manage-tags") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                instances: selectedInstances,
                action: action,
                tags: tags
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            alert(data.message);
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('manageTagsModal')).hide();
                // 重新加载实例列表
                const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);
                const selectedRegion = document.getElementById('regionSelect').value;
                if (selectedAccounts.length > 0 && selectedRegion) {
                    loadInstances(selectedAccounts, selectedRegion);
                }
            }
        })
        .catch(error => {
            hideLoading();
            alert('标签操作失败: ' + error.message);
        });
    }

    // 查看实例详情（占位函数）
    window.viewInstanceDetails = function(instanceId) {
        alert('实例详情功能待开发: ' + instanceId);
    };

    // 页面加载时初始化
    loadRegions();
    // loadInstanceTypes(); // 已在主初始化中调用，避免重复

    // 初始化创建按钮状态
    updateCreateButtonText(0);

    // 操作系统选择事件
    document.querySelector('select[name="os_type"]').addEventListener('change', function() {
        updateAmiOptions();
        // 更新密码字段标签和提示
        const passwordLabel = document.getElementById('passwordLabel');
        const passwordHelp = document.getElementById('passwordHelp');
        const passwordInput = document.querySelector('input[name="password"]');

        if (this.value === 'Windows') {
            passwordLabel.textContent = '管理员密码 (Windows)';
            passwordInput.placeholder = '设置Windows管理员密码';
            passwordHelp.textContent = '密码至少8位，用于Windows远程桌面登录';
        } else if (this.value) {
            passwordLabel.textContent = 'Root密码 (Linux)';
            passwordInput.placeholder = '设置Linux Root密码';
            passwordHelp.textContent = '密码至少8位，用于SSH登录和sudo操作';
        } else {
            passwordLabel.textContent = '系统密码';
            passwordInput.placeholder = '设置系统登录密码';
            passwordHelp.textContent = '密码至少8位，用于系统登录';
        }
    });

    // 账户选择事件
    document.querySelector('select[name="account_id"]').addEventListener('change', function() {
        const accountId = this.value;
        const region = document.querySelector('#createInstanceModal select[name="region"]').value;
        if (accountId && region) {
            loadKeyPairs(accountId, region);
            loadSubnets(accountId, region);
        }
    });

    // 区域选择事件（创建实例表单）
    document.querySelector('#createInstanceModal select[name="region"]').addEventListener('change', function() {
        const region = this.value;
        const accountId = document.querySelector('select[name="account_id"]').value;
        if (region) {
            loadAmis(region);
            if (accountId) {
                loadKeyPairs(accountId, region);
                loadSubnets(accountId, region);
            }
        }
    });

    // 创建实例表单提交
    document.getElementById('createInstanceForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());

        showLoading('正在创建实例...');

        fetch('{{ route("aws-ec2.create-instance") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            alert(data.message);
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('createInstanceModal')).hide();
                this.reset();
                // 重新加载实例列表
                const selectedAccounts = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => cb.value);
                const selectedRegion = document.getElementById('regionSelect').value;
                if (selectedAccounts.length > 0 && selectedRegion) {
                    loadInstances(selectedAccounts, selectedRegion);
                }
            }
        })
        .catch(error => {
            hideLoading();
            alert('创建实例失败: ' + error.message);
        });
    });

    // 智能批量选择功能
    function initSmartBatchSelection() {
        // 获取账户统计数据
        loadAccountStats();

        // 时间范围选择事件
        document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    updateCountButtons();
                    // 自动选择"全部"按钮并禁用其他按钮
                    selectAllAndDisableOthers();
                    // 自动执行智能选择（默认全部）
                    performSmartSelection('all');
                }
            });
        });

        // 数量选择按钮事件
        document.querySelectorAll('.count-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 如果点击的不是"全部"按钮，重新启用所有按钮
                if (this.dataset.count !== 'all') {
                    enableAllCountButtons();
                }

                // 移除其他按钮的active状态
                document.querySelectorAll('.count-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // 清空自定义输入框
                document.getElementById('customCount').value = '';

                // 执行智能选择
                performSmartSelection(this.dataset.count);
            });
        });

        // 自定义数量输入事件
        document.getElementById('customCount').addEventListener('input', function() {
            if (this.value) {
                // 移除按钮的active状态
                document.querySelectorAll('.count-btn').forEach(b => b.classList.remove('active'));

                // 执行智能选择
                performSmartSelection(parseInt(this.value));
            }
        });

        // 查看详细按钮事件
        document.getElementById('viewDetailsBtn').addEventListener('click', function() {
            showAccountDetails();
        });

        // 移除旧的批量创建按钮事件（已整合到统一的创建实例按钮中）

        // 模态框中的继续按钮事件
        document.getElementById('proceedToBatchCreate').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('accountDetailsModal')).hide();
            proceedToBatchCreate();
        });
    }

    // 获取账户统计数据
    function loadAccountStats() {
        fetch('{{ route("aws-ec2.account-stats") }}', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('todayCount').textContent = data.today || 0;
            document.getElementById('yesterdayCount').textContent = data.yesterday || 0;
            document.getElementById('dayBeforeCount').textContent = data.day_before || 0;
        })
        .catch(error => {
            console.error('获取账户统计失败:', error);
        });
    }

    // 更新数量按钮状态
    function updateCountButtons() {
        const selectedTimeRange = document.querySelector('input[name="timeRange"]:checked');
        if (!selectedTimeRange) return;

        const timeRange = selectedTimeRange.value;
        const countElement = document.getElementById(timeRange === 'today' ? 'todayCount' :
                                                   timeRange === 'yesterday' ? 'yesterdayCount' : 'dayBeforeCount');
        const maxCount = parseInt(countElement.textContent);

        // 更新按钮状态
        document.querySelectorAll('.count-btn').forEach(btn => {
            const count = btn.dataset.count;
            if (count !== 'all' && parseInt(count) > maxCount) {
                btn.disabled = true;
                btn.classList.add('disabled');
            } else {
                btn.disabled = false;
                btn.classList.remove('disabled');
            }
        });
    }

    // 选择全部并禁用其他按钮
    function selectAllAndDisableOthers() {
        // 选中"全部"按钮
        const allBtn = document.querySelector('.count-btn[data-count="all"]');
        if (allBtn) {
            // 移除所有按钮的active状态
            document.querySelectorAll('.count-btn').forEach(b => b.classList.remove('active'));
            // 激活"全部"按钮
            allBtn.classList.add('active');

            // 禁用其他数量按钮
            document.querySelectorAll('.count-btn').forEach(btn => {
                if (btn.dataset.count !== 'all') {
                    btn.disabled = true;
                    btn.classList.add('disabled');
                }
            });

            // 清空自定义输入框
            document.getElementById('customCount').value = '';
        }
    }

    // 重新启用所有按钮
    function enableAllCountButtons() {
        const selectedTimeRange = document.querySelector('input[name="timeRange"]:checked');
        if (!selectedTimeRange) return;

        const timeRange = selectedTimeRange.value;
        const countElement = document.getElementById(timeRange === 'today' ? 'todayCount' :
                                                   timeRange === 'yesterday' ? 'yesterdayCount' : 'dayBeforeCount');
        const maxCount = parseInt(countElement.textContent);

        document.querySelectorAll('.count-btn').forEach(btn => {
            const count = btn.dataset.count;
            if (count === 'all' || parseInt(count) <= maxCount) {
                btn.disabled = false;
                btn.classList.remove('disabled');
            }
        });
    }

    // 执行智能选择
    function performSmartSelection(count = 'all') {
        const selectedTimeRange = document.querySelector('input[name="timeRange"]:checked');
        if (!selectedTimeRange) {
            alert('请先选择时间范围');
            return;
        }

        const timeRange = selectedTimeRange.value;

        fetch('{{ route("aws-ec2.smart-select") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                time_range: timeRange,
                count: count
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                smartSelectedAccounts = data.accounts;
                updateSelectionDisplay(data.accounts, timeRange, count);

                // 同步更新账户选择器
                syncAccountSelector(data.accounts);
            } else {
                alert(data.message || '选择失败');
            }
        })
        .catch(error => {
            console.error('智能选择失败:', error);
            alert('选择失败，请重试');
        });
    }

    // 更新选择结果显示
    function updateSelectionDisplay(accounts, timeRange, count) {
        const timeRangeText = timeRange === 'today' ? '今天' :
                             timeRange === 'yesterday' ? '昨天' : '前天';
        const countText = count === 'all' ? accounts.length : count;

        document.getElementById('selectionText').textContent =
            `已显示${timeRangeText} ${accounts.length} 个账户`;

        document.getElementById('selectionResult').style.display = 'block';
    }

    // 同步账户选择器
    function syncAccountSelector(accounts) {
        const accountsList = document.getElementById('accountsList');
        const totalCountElement = document.getElementById('totalAccountsCount');
        const descriptionElement = document.getElementById('accountSelectorDescription');
        const selectAllBtn = document.getElementById('selectAllAccounts');
        const clearAllBtn = document.getElementById('clearAllAccounts');

        // 清空现有内容
        accountsList.innerHTML = '';

        if (accounts.length === 0) {
            // 没有账户时显示提示
            accountsList.innerHTML = `
                <div class="col-12 text-center py-4">
                    <div class="text-muted">
                        <i class="bi bi-info-circle me-2"></i>
                        当前时间范围内没有找到账户
                    </div>
                </div>
            `;
            totalCountElement.textContent = '0';
            descriptionElement.textContent = '当前时间范围内没有找到账户';
            selectAllBtn.disabled = true;
            clearAllBtn.disabled = true;
        } else {
            // 对账户进行降序排序，假设根据 account.id 降序
            accounts.sort((a, b) => b.id - a.id);
            // 动态生成账户选项
            accounts.forEach(account => {
                const accountName = account.account_name.includes('@')
                    ? account.account_name.split('@')[0]
                    : account.account_name;

                // 检查账户是否可用（状态为1=正常）
                const isAccountAvailable = account.status === 0 || account.status === 1;
                const disabledClass = isAccountAvailable ? '' : ' disabled';
                const disabledAttr = isAccountAvailable ? '' : ' disabled';
                const checkedAttr = isAccountAvailable ? ' checked' : '';

                const accountHtml = `
                    <div class="col-md-3 col-lg-2 mb-3">
                        <div class="account-option${disabledClass} ${isAccountAvailable ? 'selected' : ''}" data-account-id="${account.id}">
                            <div class="form-check">
                                <input class="form-check-input account-checkbox" type="checkbox" value="${account.id}" id="account${account.id}"${checkedAttr}${disabledAttr}>
                                <label class="form-check-label w-100" for="account${account.id}">
                                    <div class="account-name" title="${account.account_name}">
                                        ${accountName}
                                    </div>
                                    <div class="account-status">
                                        ${getAccountStatusDisplay(account)}
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                `;
                accountsList.insertAdjacentHTML('beforeend', accountHtml);
            });

            totalCountElement.textContent = accounts.length;
            // 描述文本将由updateAccountsCount函数更新，显示已选择的账户数量
            selectAllBtn.disabled = false;
            clearAllBtn.disabled = false;

            // 重新绑定事件
            bindAccountEvents();
        }

        updateAccountsCount();
    }

    // 重新绑定账户事件
    function bindAccountEvents() {
        // 账户选择事件
        document.querySelectorAll('.account-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const accountOption = this.closest('.account-option');
                if (this.checked) {
                    accountOption.classList.add('selected');
                } else {
                    accountOption.classList.remove('selected');
                }
                updateAccountsCount();
            });
        });
    }

    // 显示账户详细信息
    function showAccountDetails() {
        const accountsList = document.getElementById('selectedAccountsList');
        accountsList.innerHTML = '';

        // 获取已选择的账户ID
        const selectedAccountIds = Array.from(document.querySelectorAll('.account-checkbox:checked')).map(cb => parseInt(cb.value));

        // 只显示已选择的账户
        const selectedAccounts = smartSelectedAccounts.filter(account => selectedAccountIds.includes(account.id));

        selectedAccounts.forEach(account => {
            const accountName = account.account_name.includes('@') ?
                               account.account_name.split('@')[0] : account.account_name;

            const accountCard = `
                <div class="col-md-4 mb-2">
                    <div class="card border-0 bg-light">
                        <div class="card-body py-2 px-3">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle-fill text-success me-2"></i>
                                <div>
                                    <div class="fw-semibold">${accountName}</div>
                                    <small class="text-muted">${account.created_at}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            accountsList.innerHTML += accountCard;
        });

        // 显示模态框
        new bootstrap.Modal(document.getElementById('accountDetailsModal')).show();
    }

    // 跳转到批量创建
    function proceedToBatchCreate() {
        if (smartSelectedAccounts.length === 0) {
            alert('请先选择账户');
            return;
        }

        // 直接跳转到批量创建页面，传递选中的账户ID
        const accountIds = smartSelectedAccounts.map(account => account.id).join(',');
        window.location.href = `{{ route('aws-ec2.batch-create') }}?accounts=${accountIds}`;
    }

    // 初始化EC2状态筛选功能
    function initEc2StatusFilter() {
        const filterDropdown = document.querySelectorAll('#ec2StatusFilter + .dropdown-menu .dropdown-item');

        // 设置默认的active状态（全部状态）
        const defaultItem = document.querySelector('#ec2StatusFilter + .dropdown-menu .dropdown-item[data-filter="all"]');
        if (defaultItem) {
            defaultItem.classList.add('active');
        }

        filterDropdown.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const filter = this.getAttribute('data-filter');
                const filterText = this.textContent;

                // 移除所有active状态
                filterDropdown.forEach(i => i.classList.remove('active'));
                // 添加当前项的active状态
                this.classList.add('active');

                // 更新按钮文本
                document.getElementById('ec2StatusFilter').innerHTML =
                    `<i class="bi bi-funnel me-1"></i>${filterText}`;

                // 应用筛选
                applyEc2StatusFilter(filter);
            });
        });
    }

    // 应用EC2状态筛选
    function applyEc2StatusFilter(filter) {
        // 检查是否有已选择的账户
        if (!smartSelectedAccounts || smartSelectedAccounts.length === 0) {
            alert('请先使用智能批量选择功能选择账户');
            return;
        }

        // 对当前显示的账户进行筛选
        let filteredAccounts = smartSelectedAccounts;

        if (filter === 'enabled') {
            // 只显示已开通EC2的账户
            filteredAccounts = smartSelectedAccounts.filter(account => account.ec2_status === 1);
        } else if (filter === 'not_enabled') {
            // 只显示未开通EC2的账户
            filteredAccounts = smartSelectedAccounts.filter(account => account.ec2_status === 0);
        }
        // filter === 'all' 时显示全部账户，不需要筛选

        // 更新账户显示
        updateAccountsDisplay(filteredAccounts);
    }

    // 更新账户显示
    function updateAccountsDisplay(accounts) {
        // 同步更新账户选择器
        syncAccountSelector(accounts);

        // 更新显示的账户数量信息 - 显示已选择的账户数量
        const selectedCount = document.querySelectorAll('.account-checkbox:checked').length;
        const filterText = getFilterDisplayText();
        document.getElementById('selectionText').textContent =
            `${filterText} ${selectedCount} 个账户`;
    }

    // 获取筛选状态的显示文本
    function getFilterDisplayText() {
        const activeFilterItem = document.querySelector('#ec2StatusFilter').parentElement.querySelector('.dropdown-item.active');
        if (activeFilterItem) {
            const filter = activeFilterItem.dataset.filter;
            if (filter === 'enabled') {
                return '显示已开通EC2的';
            } else if (filter === 'not_enabled') {
                return '显示未开通EC2的';
            }
        }
        return '显示智能选择的';
    }

    // 获取账户状态显示
    function getAccountStatusDisplay(account) {
        const statusConfig = {
            0: { icon: 'bi-question-circle-fill', color: 'text-secondary', text: '未测' },
            1: { icon: 'bi-check-circle-fill', color: 'text-success', text: '可用' },
            2: { icon: 'bi-x-circle-fill', color: 'text-danger', text: '封禁' },
            3: { icon: 'bi-exclamation-triangle-fill', color: 'text-warning', text: '无效' }
        };

        const status = statusConfig[account.status] || statusConfig[0];
        return `<i class="bi ${status.icon} ${status.color} me-1"></i>${status.text}`;
    }

});
</script>
@endpush

<!-- 详细账户列表模态框 -->
<div class="modal fade" id="accountDetailsModal" tabindex="-1" aria-labelledby="accountDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="accountDetailsModalLabel">
                    <i class="bi bi-list-ul me-2"></i>选中的账户详细列表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row" id="selectedAccountsList">
                    <!-- 动态生成选中的账户列表 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="proceedToBatchCreate">
                    <i class="bi bi-arrow-right me-1"></i>继续批量创建
                </button>
            </div>
        </div>
    </div>
</div>

@endsection
