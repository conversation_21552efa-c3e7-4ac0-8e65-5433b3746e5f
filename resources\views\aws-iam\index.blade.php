@extends('layouts.app')

@section('title', 'IAM账户开通')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: none;
}

.btn-soft-primary:hover {
    background-color: #4361ee;
    color: #fff;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

.table > :not(caption) > * > * {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
    text-align: center;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 所有列居中对齐 */
.table th, .table td {
    text-align: center;
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    white-space: nowrap;
}

.badge-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
}

.badge-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 表格列宽设置 */
.table th:nth-child(1), .table td:nth-child(1) { width: 50px; } /* 选择框 */
.table th:nth-child(2), .table td:nth-child(2) { width: 12%; } /* 账户邮箱 */
.table th:nth-child(3), .table td:nth-child(3) { width: 8%; } /* AWS密码 */
.table th:nth-child(4), .table td:nth-child(4) { width: 10%; } /* 访问密钥 */
.table th:nth-child(5), .table td:nth-child(5) { width: 12%; } /* 秘密访问密钥 */
.table th:nth-child(6), .table td:nth-child(6) { width: 6%; } /* 状态 */
.table th:nth-child(7), .table td:nth-child(7) { width: 8%; } /* 账户ID */
.table th:nth-child(8), .table td:nth-child(8) { width: 8%; } /* IAM用户名 */
.table th:nth-child(9), .table td:nth-child(9) { width: 8%; } /* IAM密码 */
.table th:nth-child(10), .table td:nth-child(10) { width: 10%; } /* IAM访问密钥 */
.table th:nth-child(11), .table td:nth-child(11) { width: 12%; } /* IAM秘密密钥 */
.table th:nth-child(12), .table td:nth-child(12) { width: 6%; } /* IAM状态 */

/* 文本截断样式 */
.truncate-text {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    cursor: pointer;
    user-select: all;
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
    white-space: nowrap;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    font-size: 0.9rem;
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    font-size: 0.9rem;
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    font-size: 0.9rem;
}

/* 分页样式 */
.pagination {
    margin: 0;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #4361ee;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.page-link:hover {
    z-index: 2;
    color: #4361ee;
    text-decoration: none;
    background-color: rgba(67, 97, 238, 0.1);
    border-color: #dee2e6;
}

.page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #4361ee;
    border-color: #4361ee;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.page-item:last-child .page-link {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* 响应式表格 */
@media (max-width: 1200px) {
    .table-responsive {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1200px;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid fade-in">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">IAM账户创建</h4>
                            <p class="text-muted mb-0 mt-1">为AWS账户创建IAM用户</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <button class="btn btn-soft-primary" id="createIamBtn">
                            <i class="bi bi-person-plus me-1"></i>创建IAM账户
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row justify-content-center mb-4">
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="text" 
                                       id="iamUsername" 
                                       class="form-control" 
                                       placeholder="输入IAM用户名(至少4个字符)"
                                       minlength="8">
                                <div class="invalid-feedback">
                                    用户名至少需要4个字符
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="text" 
                                       id="iamPassword" 
                                       class="form-control" 
                                       placeholder="输入密码(至少8位，包含大小写字母和数字)"
                                       minlength="8">
                                <div class="invalid-feedback">
                                    密码至少需要8位，且必须包含大写字母、小写字母和数字
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用筛选组件 -->
                    <x-filter-panel :status-options="App\Models\AwsAccount::getStatusList()" />

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </div>
                                    </th>
                                    <th>账户邮箱</th>
                                    <th>AWS密码</th>
                                    <th>访问密钥</th>
                                    <th>秘密访问密钥</th>
                                    <th>状态</th>
                                    <th>账户ID</th>
                                    <th>IAM用户名</th>
                                    <th>IAM密码</th>
                                    <th>IAM访问密钥</th>
                                    <th>IAM秘密密钥</th>
                                    <th>IAM状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($accounts as $account)
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input account-select" value="{{ $account->id }}">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->account_name }}">
                                            {{ $account->account_name }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->aws_password }}">
                                            {{ $account->aws_password }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->access_key }}">
                                            {{ $account->access_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->secret_key }}">
                                            {{ $account->secret_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $account->status === 1 ? 'badge-soft-success' : ($account->status === 2 ? 'badge-soft-danger' : 'badge-soft-warning') }}">
                                            {{ $account->status_text }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->aws_account_id }}">
                                            {{ $account->aws_account_id }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->iam_username }}">
                                            {{ $account->iam_username }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->iam_password }}">
                                            {{ $account->iam_password }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->iam_access_key }}">
                                            {{ $account->iam_access_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text copy-cell" title="{{ $account->iam_secret_key }}">
                                            {{ $account->iam_secret_key }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($account->iam_status)
                                        <span class="badge bg-{{ $account->iam_status === 'success' ? 'success' : 'danger' }}">
                                            {{ $account->iam_status === 'success' ? '成功' : '失败' }}
                                        </span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            总共 {{ $accounts->total() }} 条记录
                        </div>
                        <div>
                            {{ $accounts->withQueryString()->links('components.pagination') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载进度条 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 class="mb-3" id="loadingText">正在创建IAM账户...</h5>
        <div class="progress mb-2" style="width: 200px;">
            <div class="progress-bar" id="progressBar" role="progressbar"></div>
        </div>
        <p class="mb-0" id="progressText">0%</p>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 复制功能
    function showCopyToast(text) {
        let toast = document.createElement('div');
        toast.textContent = text;
        toast.style.position = 'fixed';
        toast.style.zIndex = 999999;
        toast.style.background = '#222';
        toast.style.color = '#fff';
        toast.style.padding = '10px 30px';
        toast.style.borderRadius = '8px';
        toast.style.fontSize = '20px';
        toast.style.left = '50vw';
        toast.style.top = '30vh';
        toast.style.opacity = 1;
        toast.style.pointerEvents = 'none';
        document.body.appendChild(toast);
        setTimeout(() => { toast.style.opacity = 0; }, 1800);
        setTimeout(() => { toast.remove(); }, 2200);
    }
    
    // 点击复制功能
    document.querySelector('tbody').addEventListener('click', function(e) {
        if (e.target.classList.contains('copy-cell')) {
            const code = e.target.textContent.trim();
            navigator.clipboard.writeText(code).then(function() {
                showCopyToast('复制成功');
            });
        }
    });
    
    const iamUsername = document.getElementById('iamUsername');
    const iamPassword = document.getElementById('iamPassword');
    const createIamBtn = document.getElementById('createIamBtn');
    const timeRange = document.getElementById('timeRange');
    const dateRangeContainer = document.getElementById('dateRangeContainer');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const perPageSelect = document.getElementById('perPageSelect');

    // 全选功能
    document.getElementById('selectAll').addEventListener('change', function() {
        document.querySelectorAll('.account-select').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // 创建IAM账户
    createIamBtn.addEventListener('click', async function() {
        // 重置输入框样式
        iamUsername.classList.remove('is-invalid');
        iamPassword.classList.remove('is-invalid');
        
        // 验证用户名
        const usernameRegex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{4,}$/;
        if (!usernameRegex.test(iamUsername.value)) {
            iamUsername.classList.add('is-invalid');
            iamUsername.nextElementSibling.textContent = '用户名必须至少4位，且包含英文和数字';
            return;
        }
        
        // 验证密码
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
        if (!passwordRegex.test(iamPassword.value)) {
            iamPassword.classList.add('is-invalid');
            iamPassword.nextElementSibling.textContent = '密码必须至少8位，且包含大小写字母和数字';
            return;
        }

        const ids = Array.from(document.querySelectorAll('.account-select:checked')).map(cb => cb.value);
        
        if (ids.length === 0) {
            alert('请选择要创建IAM账户的AWS账户');
            return;
        }

        if (!confirm(`确定要为选中的 ${ids.length} 个账户创建IAM账户吗？`)) {
            return;
        }

        // 显示加载动画
        document.getElementById('loadingOverlay').style.display = 'flex';
        const progressBar = document.getElementById('progressBar');
        const loadingText = document.getElementById('loadingText');
        let progress = 0;

        // 模拟进度
        const progressInterval = setInterval(() => {
            progress += (90 - progress) / 10;
            if (progress > 89) progress = 89;
            progressBar.style.width = Math.round(progress) + '%';
            document.getElementById('progressText').textContent = Math.round(progress) + '%';
        }, 500);

        try {
            const response = await fetch('/user/aws-iam/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ 
                    ids: ids,
                    username: iamUsername.value,
                    password: iamPassword.value
                })
            });
            let data;
            try {
                data = await response.json();
            } catch (jsonError) {
                data = {};
            }

            if (!response.ok) {
                // 代理异常优先判断
                if (data.error_type === 'proxy_error') {
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', data.message);
                    } else {
                        alert(data.message);
                    }
                    clearInterval(progressInterval);
                    document.getElementById('loadingOverlay').style.display = 'none';
                    return;
                }
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            // 代理异常判断（正常响应也可能返回）
            if (data.error_type === 'proxy_error') {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', data.message);
                } else {
                    alert(data.message);
                }
                clearInterval(progressInterval);
                document.getElementById('loadingOverlay').style.display = 'none';
                return;
            }
            
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            document.getElementById('progressText').textContent = '100%';
            
            // 更新表格中的IAM信息
            if (data.results) {
                let successCount = 0;
                let failedCount = 0;

                data.results.forEach(result => {
                    try {
                        const checkbox = document.querySelector(`input[value="${result.id}"]`);
                        if (!checkbox) {
                            console.error(`找不到ID为${result.id}的账户行`);
                            return;
                        }
                        const row = checkbox.closest('tr');
                        if (!row) {
                            console.error(`找不到ID为${result.id}的账户行的tr元素`);
                            return;
                        }

                        // 立即更新账户状态（无论创建是否成功）
                        const statusCell = row.querySelector('td:nth-child(6)');
                        if (statusCell && result.account_status !== undefined) {
                            let statusText = '';
                            let statusClass = '';
                            switch (result.account_status) {
                                case 0:
                                    statusText = '未测试';
                                    statusClass = 'badge-soft-warning';
                                    break;
                                case 1:
                                    statusText = '正常';
                                    statusClass = 'badge-soft-success';
                                    break;
                                case 2:
                                    statusText = '封禁';
                                    statusClass = 'badge-soft-danger';
                                    break;
                                case 3:
                                    statusText = '无效';
                                    statusClass = 'badge-soft-danger';
                                    break;
                            }
                            statusCell.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
                        }

                        // 立即更新创建状态
                        const createStatusCell = row.querySelector('td:nth-child(12)');
                        if (createStatusCell) {
                            createStatusCell.innerHTML = `
                                <span class="badge bg-${result.status === 'success' ? 'success' : 'danger'}">
                                    ${result.status === 'success' ? '成功' : '失败'}
                                </span>
                            `;
                        }

                        if (result.status === 'success') {
                            successCount++;
                            
                            // 更新账户ID
                            const accountIdCell = row.querySelector('td:nth-child(7)');
                            if (accountIdCell && result.account_id) {
                                accountIdCell.innerHTML = `
                                    <span class="truncate-text copy-cell" title="${result.account_id}">
                                        ${result.account_id}
                                    </span>
                                `;
                            }

                            // 更新IAM用户名
                            const iamUsernameCell = row.querySelector('td:nth-child(8)');
                            if (iamUsernameCell && result.iam_username) {
                                iamUsernameCell.innerHTML = `
                                    <span class="truncate-text copy-cell" title="${result.iam_username}">
                                        ${result.iam_username}
                                    </span>
                                `;
                            }

                            // 更新IAM密码
                            const iamPasswordCell = row.querySelector('td:nth-child(9)');
                            if (iamPasswordCell && result.iam_password) {
                                // 从截断的密码中提取完整密码
                                const fullPassword = result.iam_password.endsWith('...') ? 
                                    result.iam_password.slice(0, -3) : result.iam_password;
                                iamPasswordCell.innerHTML = `
                                    <span class="truncate-text copy-cell" title="${fullPassword}">
                                        ${result.iam_password}
                                    </span>
                                `;
                            }

                            // 更新IAM访问密钥
                            const iamAccessKeyCell = row.querySelector('td:nth-child(10)');
                            if (iamAccessKeyCell && result.access_key) {
                                // 从截断的访问密钥中提取完整密钥
                                const fullAccessKey = result.access_key.endsWith('...') ? 
                                    result.access_key.slice(0, -3) : result.access_key;
                                iamAccessKeyCell.innerHTML = `
                                    <span class="truncate-text copy-cell" title="${fullAccessKey}">
                                        ${result.access_key}
                                    </span>
                                `;
                            }

                            // 更新IAM秘密密钥
                            const iamSecretKeyCell = row.querySelector('td:nth-child(11)');
                            if (iamSecretKeyCell && result.secret_key) {
                                // 从截断的秘密密钥中提取完整密钥
                                const fullSecretKey = result.secret_key.endsWith('...') ? 
                                    result.secret_key.slice(0, -3) : result.secret_key;
                                iamSecretKeyCell.innerHTML = `
                                    <span class="truncate-text copy-cell" title="${fullSecretKey}">
                                        ${result.secret_key}
                                    </span>
                                `;
                            }
                        } else {
                            failedCount++;
                        }
                    } catch (error) {
                        console.error('更新行时发生错误:', error);
                        console.error('结果数据:', result);
                    }
                });

                // 更新成功/失败计数显示
                setTimeout(() => {
                    document.getElementById('loadingOverlay').style.display = 'none';
                    alert(`创建完成！\n成功：${successCount}\n失败：${failedCount}`);
                }, 500);
            }
            
            // 重新绑定密码显示切换事件
            document.querySelectorAll('.password-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    const icon = this.querySelector('i');
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.replace('bi-eye', 'bi-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.replace('bi-eye-slash', 'bi-eye');
                    }
                });
            });
        } catch (error) {
            clearInterval(progressInterval);
            document.getElementById('loadingOverlay').style.display = 'none';
            // catch 里也判断代理异常
            if (error && error.error_type === 'proxy_error') {
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', error.message);
                } else {
                    alert(error.message);
                }
                return;
            }
            alert('操作失败：' + (error.message || '未知错误'));
        }
    });
});
</script>
@endpush 