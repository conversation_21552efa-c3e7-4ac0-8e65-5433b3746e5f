@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 表单控件样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    min-height: 120px;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.btn-primary:hover {
    background-color: #3651d4;
    border-color: #3651d4;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* 提示文本 */
.text-muted {
    color: #718096 !important;
}

/* 格式说明 */
.format-guide {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.format-guide pre {
    margin-bottom: 0;
    color: #4a5568;
}

.form-label {
    margin-bottom: .5rem;
    font-weight: 500;
    color: #495057;
}

.form-text {
    margin-top: .25rem;
    font-size: .875rem;
    color: #74788d;
}

.btn-soft-primary {
    color: #2a4fd7;
    background-color: rgba(42,79,215,.1);
    border-color: transparent;
}

.btn-soft-secondary {
    color: #74788d;
    background-color: rgba(116,120,141,.1);
    border-color: transparent;
}

.format-example {
    background-color: #f8f9fa;
    border-radius: .25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    font-family: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;
}

.format-example pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #495057;
}

.format-example .highlight {
    color: #2a4fd7;
    font-weight: 600;
}

.validation-indicator {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #34c38f;
}

.validation-indicator i {
    font-size: 1.2rem;
}

.validation-tips {
    font-size: .875rem;
    color: #74788d;
    margin-top: .5rem;
}

/* 添加导入结果样式 */
.import-result-container {
    padding: 1.5rem;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.result-card {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    border-radius: 0.5rem;
    background: #f8f9fa;
    transition: transform 0.2s;
}

.result-card:hover {
    transform: translateY(-2px);
}

.result-card.total {
    background: rgba(90, 102, 241, 0.1);
}

.result-card.total .result-icon {
    color: #5a66f1;
}

.result-card.success {
    background: rgba(10, 179, 156, 0.1);
}

.result-card.success .result-icon {
    color: #0ab39c;
}

.result-card.failed {
    background: rgba(240, 101, 72, 0.1);
}

.result-card.failed .result-icon {
    color: #f06548;
}

.result-icon {
    font-size: 2rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
}

.result-content {
    flex: 1;
}

.result-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.result-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
}

.failed-accounts {
    background: rgba(240, 101, 72, 0.05);
    border-radius: 0.5rem;
    padding: 1.25rem;
}

.failed-list {
    max-height: 200px;
    overflow-y: auto;
}

.failed-item {
    padding: 0.75rem;
    background: #fff;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #495057;
    border: 1px solid rgba(240, 101, 72, 0.2);
}

.failed-item:last-child {
    margin-bottom: 0;
}

.failed-item i {
    color: #f06548;
}
</style>
@endpush

@section('content')
<div class="container fade-in">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">添加AWS账户</h4>
                        <p class="text-muted mb-0 mt-1">批量导入AWS账户信息</p>
                    </div>
                    <a href="{{ route('aws-accounts.index') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="card-body">
                    <div class="format-guide mb-4">
                        <h6 class="mb-2">导入格式说明：</h6>
                        <pre>输入螺旋数卡数据，其他平台数据暂不支持</pre>
                        <!--<small class="text-muted">每行一个账户，按照上述格式填写</small>-->
                    </div>

                    <form method="POST" action="{{ route('aws-accounts.store') }}">
                        @csrf
                        <div class="mb-4">
                            <label for="accounts_data" class="form-label">账户信息</label>
                            <textarea class="form-control @error('accounts_data') is-invalid @enderror" 
                                      id="accounts_data" name="accounts_data" rows="10" 
                                      placeholder="请按照上述格式粘贴账户信息，每行一个账户">{{ old('accounts_data') }}</textarea>
                            @error('accounts_data')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-cloud-upload me-1"></i>导入账户
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // 表单验证和提交
    $('#awsAccountForm').submit(function(e) {
        e.preventDefault();
        
        const text = $('#accounts_data').val();
        const lines = text.split('\n').filter(line => line.trim() !== '');
        let hasError = false;
        let failedAccounts = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const match = line.match(/①微软账号：(.*?) ②微软密码：(.*?) ③AWS密码：(.*?) ④访问密钥：(.*?) ⑤秘密访问密钥：(.*)/);
            
            if (!match) {
                failedAccounts.push({
                    email: line.match(/①微软账号：([^ ]+)/) ? line.match(/①微软账号：([^ ]+)/)[1] : '格式错误',
                    reason: '格式不正确'
                });
                hasError = true;
                continue;
            }

            const [, email, , , accessKey, secretKey] = match;
            
            if (!/^[A-Z0-9]{20}$/.test(accessKey.trim())) {
                failedAccounts.push({
                    email: email,
                    reason: '访问密钥格式不正确'
                });
                hasError = true;
                continue;
            }
            
            if (!/^[A-Za-z0-9/+]{40}$/.test(secretKey.trim())) {
                failedAccounts.push({
                    email: email,
                    reason: '秘密访问密钥格式不正确'
                });
                hasError = true;
                continue;
            }
        }

        // 如果有错误，显示错误信息
        if (hasError) {
            const importResult = {
                total_lines: lines.length,
                success_count: lines.length - failedAccounts.length,
                failed_count: failedAccounts.length,
                failed_accounts: failedAccounts
            };
            
            // 显示导入结果
            showImportResult(importResult);
        } else {
            // 如果没有错误，提交表单
            this.submit();
        }
    });

    // 显示导入结果
    function showImportResult(result) {
        // 移除旧的结果显示
        $('.import-result-container').remove();
        
        // 创建新的结果显示
        const html = `
            <div class="import-result-container mt-4">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="result-card total">
                            <div class="result-icon">
                                <i class="bi bi-list-ul"></i>
                            </div>
                            <div class="result-content">
                                <div class="result-label">总行数</div>
                                <div class="result-value">${result.total_lines}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="result-card success">
                            <div class="result-icon">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="result-content">
                                <div class="result-label">格式正确</div>
                                <div class="result-value">${result.success_count}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="result-card failed">
                            <div class="result-icon">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="result-content">
                                <div class="result-label">格式错误</div>
                                <div class="result-value">${result.failed_count}</div>
                            </div>
                        </div>
                    </div>
                </div>
                ${result.failed_count > 0 ? `
                    <div class="failed-accounts mt-4">
                        <h6 class="text-danger mb-3">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            以下账户格式有误：
                        </h6>
                        <div class="failed-list">
                            ${result.failed_accounts.map(account => `
                                <div class="failed-item">
                                    <i class="bi bi-x-circle me-2"></i>
                                    ${account.email}
                                    <small class="text-muted ms-2">${account.reason}</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        $('#awsAccountForm').after(html);
    }
});
</script>
@endpush 