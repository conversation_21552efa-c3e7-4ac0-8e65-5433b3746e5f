@extends('admin.layouts.app')

@section('title', '系统设置')

@section('content')
<div class="page-header-modern">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title-modern mb-2">系统设置</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="text-secondary">
                            <i class="bi bi-house-door me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="bi bi-gear me-1"></i>系统设置
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.settings.update') }}" method="POST">
            @csrf
            
            <div class="mb-3">
                <label for="site_name" class="form-label">网站名称</label>
                <input type="text" 
                       class="form-control @error('site_name') is-invalid @enderror" 
                       id="site_name" 
                       name="site_name"
                       value="{{ old('site_name', $settings['site_name'] ?? '') }}"
                       required>
                <div class="form-text">显示在浏览器标签页和导航栏左上角</div>
                @error('site_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="site_description" class="form-label">网站描述</label>
                <textarea class="form-control @error('site_description') is-invalid @enderror" 
                          id="site_description" 
                          name="site_description"
                          rows="3">{{ old('site_description', $settings['site_description'] ?? '') }}</textarea>
                <div class="form-text">用于SEO优化，建议不超过200字</div>
                @error('site_description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="login_title" class="form-label">登录页面标题</label>
                <input type="text" 
                       class="form-control @error('login_title') is-invalid @enderror" 
                       id="login_title" 
                       name="login_title"
                       value="{{ old('login_title', $settings['login_title'] ?? '') }}"
                       required>
                <div class="form-text">显示在登录页面的大标题</div>
                @error('login_title')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="dashboard_title" class="form-label">仪表盘标题</label>
                <input type="text" 
                       class="form-control @error('dashboard_title') is-invalid @enderror" 
                       id="dashboard_title" 
                       name="dashboard_title"
                       value="{{ old('dashboard_title', $settings['dashboard_title'] ?? '') }}"
                       required>
                <div class="form-text">显示在仪表盘页面的标题</div>
                @error('dashboard_title')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="register_enabled" class="form-label">注册功能</label>
                <div class="form-check form-switch">
                    <input type="checkbox" 
                           class="form-check-input @error('register_enabled') is-invalid @enderror" 
                           id="register_enabled" 
                           name="register_enabled"
                           value="1"
                           {{ \App\Models\Setting::getBool('register_enabled', true) ? 'checked' : '' }}>
                    <label class="form-check-label" for="register_enabled">启用注册功能</label>
                </div>
                <div class="form-text">关闭后新用户将无法注册</div>
                @error('register_enabled')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="maintenance_mode" class="form-label">维护模式</label>
                <div class="form-check form-switch">
                    <input type="checkbox"
                           class="form-check-input @error('maintenance_mode') is-invalid @enderror"
                           id="maintenance_mode"
                           name="maintenance_mode"
                           value="1"
                           {{ \App\Models\Setting::getBool('maintenance_mode', false) ? 'checked' : '' }}>
                    <label class="form-check-label" for="maintenance_mode">启用维护模式</label>
                </div>
                <div class="form-text">启用后只有管理员可以访问系统</div>
                @error('maintenance_mode')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- 免费代理设置 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-shield-check me-2"></i>免费代理设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="free_proxy_enabled" class="form-label">启用免费代理</label>
                        <div class="form-check form-switch">
                            <input type="checkbox"
                                   class="form-check-input @error('free_proxy_enabled') is-invalid @enderror"
                                   id="free_proxy_enabled"
                                   name="free_proxy_enabled"
                                   value="1"
                                   {{ \App\Models\Setting::getBool('free_proxy_enabled', false) ? 'checked' : '' }}>
                            <label class="form-check-label" for="free_proxy_enabled">为用户提供免费代理服务</label>
                        </div>
                        <div class="form-text">启用后用户可以在请求模式中选择免费代理</div>
                        @error('free_proxy_enabled')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div id="freeProxyConfig" style="display: {{ \App\Models\Setting::getBool('free_proxy_enabled', false) ? 'block' : 'none' }};">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="free_proxy_type" class="form-label">协议类型</label>
                                    <select class="form-select @error('free_proxy_type') is-invalid @enderror"
                                            id="free_proxy_type"
                                            name="free_proxy_type">
                                        <option value="http" {{ \App\Models\Setting::get('free_proxy_type', 'http') === 'http' ? 'selected' : '' }}>HTTP</option>
                                        <option value="https" {{ \App\Models\Setting::get('free_proxy_type', 'http') === 'https' ? 'selected' : '' }}>HTTPS</option>
                                        <option value="socks5" {{ \App\Models\Setting::get('free_proxy_type', 'http') === 'socks5' ? 'selected' : '' }}>SOCKS5</option>
                                    </select>
                                    @error('free_proxy_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="free_proxy_host" class="form-label">服务器地址</label>
                                    <input type="text"
                                           class="form-control @error('free_proxy_host') is-invalid @enderror"
                                           id="free_proxy_host"
                                           name="free_proxy_host"
                                           value="{{ old('free_proxy_host', \App\Models\Setting::get('free_proxy_host', '')) }}"
                                           placeholder="例如: *************">
                                    @error('free_proxy_host')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="free_proxy_port" class="form-label">端口</label>
                                    <input type="number"
                                           class="form-control @error('free_proxy_port') is-invalid @enderror"
                                           id="free_proxy_port"
                                           name="free_proxy_port"
                                           value="{{ old('free_proxy_port', \App\Models\Setting::get('free_proxy_port', '')) }}"
                                           placeholder="例如: 8080"
                                           min="1" max="65535">
                                    @error('free_proxy_port')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="free_proxy_username" class="form-label">用户名（可选）</label>
                                    <input type="text"
                                           class="form-control @error('free_proxy_username') is-invalid @enderror"
                                           id="free_proxy_username"
                                           name="free_proxy_username"
                                           value="{{ old('free_proxy_username', \App\Models\Setting::get('free_proxy_username', '')) }}"
                                           placeholder="如果代理需要认证">
                                    @error('free_proxy_username')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="free_proxy_password" class="form-label">密码（可选）</label>
                                    <div class="input-group">
                                        <input type="password"
                                               class="form-control @error('free_proxy_password') is-invalid @enderror"
                                               id="free_proxy_password"
                                               name="free_proxy_password"
                                               value="{{ old('free_proxy_password', \App\Models\Setting::get('free_proxy_password', '')) }}"
                                               placeholder="如果代理需要认证">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleFreeProxyPassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    @error('free_proxy_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">连接测试</label>
                                    <div>
                                        <button type="button" class="btn btn-outline-primary" id="testFreeProxy">
                                            <i class="bi bi-arrow-clockwise me-1"></i>测试连接
                                        </button>
                                        <div id="freeProxyTestResult" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @php
                            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');
                            $freeProxyLastTest = \App\Models\Setting::get('free_proxy_last_test', '');
                            $freeProxyError = \App\Models\Setting::get('free_proxy_error_message', '');
                        @endphp

                        @if($freeProxyLastTest)
                        <div class="alert alert-{{ $freeProxyStatus === 'active' ? 'success' : 'warning' }} alert-sm">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-{{ $freeProxyStatus === 'active' ? 'check-circle' : 'exclamation-triangle' }} me-2"></i>
                                <div>
                                    <strong>最后测试:</strong> {{ $freeProxyLastTest }}
                                    <br>
                                    <strong>状态:</strong> {{ $freeProxyStatus === 'active' ? '连接正常' : '连接异常' }}
                                    @if($freeProxyError)
                                        <br><strong>错误:</strong> {{ $freeProxyError }}
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-soft-primary">
                    <i class="bi bi-save me-2"></i>保存设置
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 免费代理启用/禁用切换
    const freeProxyEnabled = document.getElementById('free_proxy_enabled');
    const freeProxyConfig = document.getElementById('freeProxyConfig');

    if (freeProxyEnabled) {
        freeProxyEnabled.addEventListener('change', function() {
            freeProxyConfig.style.display = this.checked ? 'block' : 'none';
        });
    }

    // 密码显示/隐藏切换
    const togglePasswordBtn = document.getElementById('toggleFreeProxyPassword');
    const passwordInput = document.getElementById('free_proxy_password');

    if (togglePasswordBtn && passwordInput) {
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const icon = this.querySelector('i');
            icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
        });
    }

    // 免费代理连接测试
    const testBtn = document.getElementById('testFreeProxy');
    const testResult = document.getElementById('freeProxyTestResult');

    if (testBtn) {
        testBtn.addEventListener('click', function() {
            const btn = this;
            const originalText = btn.innerHTML;

            // 获取表单数据
            const formData = {
                type: document.getElementById('free_proxy_type').value,
                host: document.getElementById('free_proxy_host').value,
                port: document.getElementById('free_proxy_port').value,
                username: document.getElementById('free_proxy_username').value,
                password: document.getElementById('free_proxy_password').value,
                _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            };

            // 验证必填字段
            if (!formData.host || !formData.port) {
                testResult.innerHTML = '<div class="alert alert-danger alert-sm mt-2">请先填写服务器地址和端口</div>';
                return;
            }

            // 更新按钮状态
            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>测试中...';
            testResult.innerHTML = '<div class="alert alert-info alert-sm mt-2">正在测试连接...</div>';

            // 发送测试请求
            fetch('{{ route("admin.settings.test-free-proxy") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': formData._token
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    testResult.innerHTML = `
                        <div class="alert alert-success alert-sm mt-2">
                            <i class="bi bi-check-circle me-1"></i>
                            连接成功！代理IP: ${data.ip || 'Unknown'}
                        </div>
                    `;
                } else {
                    testResult.innerHTML = `
                        <div class="alert alert-danger alert-sm mt-2">
                            <i class="bi bi-x-circle me-1"></i>
                            连接失败: ${data.message || '未知错误'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('测试请求失败:', error);
                testResult.innerHTML = `
                    <div class="alert alert-danger alert-sm mt-2">
                        <i class="bi bi-x-circle me-1"></i>
                        测试失败，请检查网络连接
                    </div>
                `;
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
            });
        });
    }
});
</script>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动隐藏提示信息
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
@endpush 