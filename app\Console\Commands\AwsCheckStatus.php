<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AwsAccount;
use Aws\Sdk;
use Aws\Exception\AwsException;

class AwsCheckStatus extends Command
{
    protected $signature = 'aws:check-status';
    protected $description = '检查AWS账户状态';

    public function handle()
    {
        $accounts = AwsAccount::where('status', '!=', 2) // 不检查已封禁的账户
                             ->get();

        foreach ($accounts as $account) {
            try {
                // 创建AWS SDK实例
                $sdk = new Sdk([
                    'region' => 'us-east-1',
                    'version' => 'latest',
                    'credentials' => [
                        'key' => $account->access_key,
                        'secret' => $account->secret_key,
                    ],
                ]);

                // 创建IAM客户端
                $iam = $sdk->createIam();
                
                // 尝试列出IAM用户
                $iam->listUsers();
                
                // 如果没有抛出异常，说明账户正常
                $account->status = 1; // 正常
                $account->last_check_at = now();
                $account->save();
                
                $this->info("账户 {$account->account_name} 状态正常");
            } catch (AwsException $e) {
                // 根据错误类型设置状态
                if (str_contains($e->getMessage(), 'InvalidClientTokenId') || 
                    str_contains($e->getMessage(), 'SignatureDoesNotMatch')) {
                    $account->status = 3; // 无效
                } elseif (str_contains($e->getMessage(), 'AccessDenied')) {
                    $account->status = 2; // 封禁
                }
                
                $account->last_check_at = now();
                $account->save();
                
                $this->error("账户 {$account->account_name} 检查失败: " . $e->getMessage());
            }
        }
    }
} 