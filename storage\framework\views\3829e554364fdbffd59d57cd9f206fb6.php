<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(\App\Models\Setting::get('site_name', config('app.name'))); ?> - 会员控制中心</title>

    <!-- jQuery -->
    <script src="<?php echo e(asset('assets/js/jquery-3.6.0.min.js')); ?>"></script>

    <!-- Bootstrap CSS -->
    <link href="<?php echo e(asset('assets/css/bootstrap-5.1.3.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('assets/css/bootstrap-icons-1.8.1.css')); ?>" rel="stylesheet">

    <!-- Bootstrap JS -->
    <script src="<?php echo e(asset('assets/js/popper-2.9.2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap-5.1.3.bundle.min.js')); ?>"></script>

    <!-- 免费代理监控器 -->
    <script src="<?php echo e(asset('js/free-proxy-monitor.js')); ?>"></script>

    <!-- Styles -->
    <link href="<?php echo e(asset('assets/css/app.min.css')); ?>" rel="stylesheet">

    <style>
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 9999;
        }
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        .btn-hover-shadow:hover {
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
            transform: translateY(-2px);
            transition: all .3s ease;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all .3s ease;
        }
        .card:hover {
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        /* 移动端导航栏样式 */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                top: 100%;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
                padding: 1rem;
                border-radius: 0 0 8px 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            .navbar-collapse.show {
                display: block !important;
            }
            
            .navbar-nav {
                margin: 0 !important;
            }
            
            .nav-item {
                margin: 0.5rem 0;
            }
            
            .nav-link {
                color: white !important;
                padding: 0.5rem 1rem !important;
                border-radius: 4px;
            }
            
            .nav-link:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div id="app">
        <?php echo $__env->make('layouts.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <main class="py-4">
            <?php if(session('success')): ?>
                <div class="container">
                    <div class="alert alert-success">
                        <?php echo e(session('success')); ?>

                    </div>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="container">
                    <div class="alert alert-danger">
                        <?php echo e(session('error')); ?>

                    </div>
                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </main>


    </div>

    <!-- 代理设置模态框 -->
    <?php if(auth()->guard()->check()): ?>
        <?php echo $__env->make('components.proxy-settings-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- 免费代理状态监控初始化 -->
    <?php if(auth()->guard()->check()): ?>
    <script>
    window.initFreeProxyMonitor = function() {
        const currentProxyMode = '<?php echo e(Auth::user()->proxy_mode ?? "local"); ?>';
        <?php
            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');
            $freeProxyErrorMessage = \App\Models\Setting::get('free_proxy_error_message', '');
        ?>
        const freeProxyStatus = '<?php echo e($freeProxyStatus); ?>';
        const freeProxyErrorMessage = '<?php echo e($freeProxyErrorMessage); ?>';

        // 初始化免费代理监控器
        if (window.FreeProxyMonitor) {
            window.FreeProxyMonitor.init(currentProxyMode, freeProxyStatus, freeProxyErrorMessage);
        }
    };
    </script>
    <?php endif; ?>
</body>
</html><?php /**PATH C:\Users\<USER>\Desktop\panel\resources\views/layouts/app.blade.php ENDPATH**/ ?>