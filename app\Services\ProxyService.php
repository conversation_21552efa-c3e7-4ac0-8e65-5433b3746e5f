<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class ProxyService
{
    /**
     * 测试代理连接
     */
    public function testProxy($type, $host, $port, $username = null, $password = null)
    {
        try {
            $proxyUrl = $this->buildProxyUrl($type, $host, $port, $username, $password);

            // 构建HTTP客户端配置
            $clientConfig = [
                'timeout' => 30,
                'proxy' => $proxyUrl,
                'http_errors' => false,
                'allow_redirects' => false
            ];

            // 根据配置决定是否禁用SSL验证
            if (config('aws.disable_ssl_verification', false)) {
                $clientConfig['verify'] = false;
            } else {
                $clientConfig['verify'] = false; // 默认禁用SSL验证以避免证书问题
            }

            $client = new Client($clientConfig);

            // 首先尝试HTTP连接测试（更稳定）
            try {
                $response = $client->get('http://httpbin.org/ip');
                if ($response->getStatusCode() === 200) {
                    $data = json_decode($response->getBody(), true);
                    return [
                        'success' => true,
                        'ip' => $data['origin'] ?? 'Unknown',
                        'message' => '代理连接成功'
                    ];
                }
            } catch (RequestException $e) {
                // HTTP失败，记录错误但继续尝试HTTPS
                Log::info('HTTP测试失败，尝试HTTPS', [
                    'proxy_type' => $type,
                    'error' => $e->getMessage()
                ]);

                // 如果是SOCKS5代理且出现Empty reply错误，可能是代理不支持该协议
                if ($type === 'socks5' && strpos($e->getMessage(), 'Empty reply from server') !== false) {
                    return [
                        'success' => false,
                        'message' => '该代理服务器不支持SOCKS5协议，请尝试使用HTTP协议'
                    ];
                }
            }

            // 如果HTTP失败，尝试HTTPS
            try {
                $response = $client->get('https://httpbin.org/ip');
                if ($response->getStatusCode() === 200) {
                    $data = json_decode($response->getBody(), true);
                    return [
                        'success' => true,
                        'ip' => $data['origin'] ?? 'Unknown',
                        'message' => '代理连接成功'
                    ];
                }
            } catch (RequestException $e) {
                // HTTPS也失败，记录错误
                Log::error('HTTPS测试也失败', [
                    'proxy_type' => $type,
                    'error' => $e->getMessage()
                ]);

                // 如果是SOCKS5代理且出现Empty reply错误，提供更具体的错误信息
                if ($type === 'socks5' && strpos($e->getMessage(), 'Empty reply from server') !== false) {
                    return [
                        'success' => false,
                        'message' => '该代理服务器不支持SOCKS5协议，请尝试使用HTTP协议'
                    ];
                }
            }

            return ['success' => false, 'message' => '代理响应异常'];

        } catch (RequestException $e) {
            Log::error('代理测试失败', [
                'proxy' => "{$type}://{$host}:{$port}",
                'error' => $e->getMessage()
            ]);

            // 提供更友好的错误信息
            $errorMessage = $this->getHumanReadableError($e->getMessage());

            return [
                'success' => false,
                'message' => $errorMessage
            ];
        } catch (\Exception $e) {
            Log::error('代理测试异常', [
                'proxy' => "{$type}://{$host}:{$port}",
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '代理测试失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 构建代理URL
     */
    private function buildProxyUrl($type, $host, $port, $username = null, $password = null)
    {
        $auth = '';
        if ($username && $password) {
            $auth = urlencode($username) . ':' . urlencode($password) . '@';
        }
        
        return "{$type}://{$auth}{$host}:{$port}";
    }
    
    /**
     * 获取用户代理配置
     */
    public function getUserProxyConfig($user)
    {
        if ($user->proxy_mode !== 'proxy' || $user->proxy_status !== 'active') {
            return null;
        }

        return $this->buildProxyUrl(
            $user->proxy_type,
            $user->proxy_host,
            $user->proxy_port,
            $user->proxy_username,
            $user->proxy_password // 直接使用明文密码
        );
    }
    
    /**
     * 检查代理是否可用
     */
    public function isProxyAvailable($user)
    {
        return $user->proxy_mode === 'proxy' 
            && $user->proxy_status === 'active'
            && $user->proxy_host 
            && $user->proxy_port;
    }
    
    /**
     * 获取代理状态描述
     */
    public function getProxyStatusText($user)
    {
        if ($user->proxy_mode === 'local') {
            return '本地模式';
        }
        
        switch ($user->proxy_status) {
            case 'active':
                return '代理正常';
            case 'testing':
                return '检测中';
            case 'error':
                return '代理异常';
            default:
                return '未配置';
        }
    }
    
    /**
     * 获取代理状态颜色
     */
    public function getProxyStatusColor($user)
    {
        if ($user->proxy_mode === 'local') {
            return 'success';
        }

        switch ($user->proxy_status) {
            case 'active':
                return 'success';
            case 'testing':
                return 'warning';
            case 'error':
                return 'danger';
            default:
                return 'secondary';
        }
    }

    /**
     * 获取人类可读的错误信息
     */
    private function getHumanReadableError($errorMessage)
    {
        // 代理认证相关错误
        if (strpos($errorMessage, '407') !== false ||
            strpos($errorMessage, 'Proxy Authentication Required') !== false ||
            strpos($errorMessage, 'authentication') !== false) {
            return '代理服务器认证失败，请检查用户名和密码';
        }

        // 代理网关错误
        if (strpos($errorMessage, '502') !== false ||
            strpos($errorMessage, 'Bad Gateway') !== false) {
            return '代理服务器网关错误，无法连接到目标服务器';
        }

        // 代理服务不可用
        if (strpos($errorMessage, '503') !== false ||
            strpos($errorMessage, 'Service Unavailable') !== false) {
            return '代理服务器暂时不可用，请稍后重试或联系代理服务商';
        }

        // 代理网关超时
        if (strpos($errorMessage, '504') !== false ||
            strpos($errorMessage, 'Gateway Timeout') !== false) {
            return '代理服务器网关超时，请检查网络连接或稍后重试';
        }

        // SSL相关错误
        if (strpos($errorMessage, 'SSL_ERROR_SYSCALL') !== false ||
            strpos($errorMessage, 'OpenSSL') !== false ||
            strpos($errorMessage, 'cURL error 35') !== false) {
            return '代理服务器SSL连接失败，请检查代理配置或联系代理服务商';
        }

        // 连接超时（cURL error 28）
        if (strpos($errorMessage, 'timeout') !== false ||
            strpos($errorMessage, 'timed out') !== false ||
            strpos($errorMessage, 'Operation timed out') !== false ||
            strpos($errorMessage, 'cURL error 28') !== false) {
            return '代理服务器连接超时，请检查网络连接或代理服务器状态';
        }

        // 连接被拒绝（cURL error 7）
        if (strpos($errorMessage, 'Connection refused') !== false ||
            strpos($errorMessage, 'Couldn\'t connect') !== false ||
            strpos($errorMessage, 'Failed to connect') !== false ||
            strpos($errorMessage, 'cURL error 7') !== false) {
            return '无法连接到代理服务器，请检查代理地址和端口是否正确';
        }

        // 服务器无响应（cURL error 52）
        if (strpos($errorMessage, 'Empty reply from server') !== false ||
            strpos($errorMessage, 'cURL error 52') !== false) {
            return '代理服务器无响应，可能是协议类型不匹配，请尝试切换协议类型';
        }

        // 代理连接中断（cURL error 56）
        if (strpos($errorMessage, 'CONNECT tunnel failed') !== false ||
            strpos($errorMessage, 'Proxy CONNECT aborted') !== false ||
            strpos($errorMessage, 'cURL error 56') !== false) {
            return '代理连接中断，请检查代理服务器状态或认证信息';
        }

        // DNS解析失败
        if (strpos($errorMessage, 'Could not resolve host') !== false ||
            strpos($errorMessage, 'getaddrinfo failed') !== false) {
            return '无法解析代理服务器地址，请检查代理主机名';
        }

        // 网络不可达
        if (strpos($errorMessage, 'Network is unreachable') !== false) {
            return '网络不可达，请检查网络连接';
        }

        // 默认cURL错误
        if (strpos($errorMessage, 'cURL error') !== false) {
            return '代理连接失败，请检查代理配置';
        }

        return '代理连接失败: ' . $errorMessage;
    }

    /**
     * 检查错误是否为代理相关错误
     */
    public function isProxyRelatedError($errorMessage)
    {
        $proxyErrorIndicators = [
            '407', 'Proxy Authentication Required',
            '502', 'Bad Gateway',
            '503', 'Service Unavailable',
            '504', 'Gateway Timeout',
            'CONNECT tunnel failed',
            'Proxy CONNECT aborted',
            'cURL error 7', 'cURL error 28', 'cURL error 35',
            'cURL error 52', 'cURL error 56',
            'Empty reply from server',
            'Connection refused',
            'Operation timed out'
        ];

        foreach ($proxyErrorIndicators as $indicator) {
            if (strpos($errorMessage, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }
}
