@extends('layouts.app')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.css" rel="stylesheet">
<style>
/* 现代化卡片样式 - 与aws-accounts页面保持一致 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 现代化按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.btn-soft-primary:hover,
.btn-soft-primary:focus,
.btn-soft-primary:active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
    box-shadow: none;
}

.btn-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    border: 1px solid rgba(46, 212, 122, 0.2);
}

.btn-soft-success:hover,
.btn-soft-success:focus,
.btn-soft-success:active {
    background-color: #2ed47a;
    color: #fff;
    border-color: #2ed47a;
    box-shadow: none;
}

.btn-soft-info {
    color: #37b9f1;
    background-color: rgba(55, 185, 241, 0.1);
    border: 1px solid rgba(55, 185, 241, 0.2);
}

.btn-soft-info:hover,
.btn-soft-info:focus,
.btn-soft-info:active {
    background-color: #37b9f1;
    color: #fff;
    border-color: #37b9f1;
    box-shadow: none;
}

.btn-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    border: 1px solid rgba(242, 87, 103, 0.2);
}

.btn-soft-danger:hover,
.btn-soft-danger:focus,
.btn-soft-danger:active {
    background-color: #f25767;
    color: #fff;
    border-color: #f25767;
    box-shadow: none;
}

/* 统计卡片样式 */
.stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 1.5rem;
}

.stats-icon-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
}

.stats-icon-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
}

.stats-icon-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
}

.stats-icon-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.stats-number {
    font-size: 1.75rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.stats-label {
    color: #6c757d;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
}

/* 表格样式优化 */
.table {
    margin-bottom: 0;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    font-size: 0.9rem;
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    font-size: 0.9rem;
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    font-size: 0.9rem;
}

.badge-soft-secondary {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    font-size: 0.9rem;
}

/* 图表容器高度统一 */
.chart-container {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#statusChart, #trendChart {
    height: 100% !important;
    width: 100% !important;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">仪表盘</h1>
        <div>
            <form action="{{ route('dashboard.clear-cache') }}" method="POST" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
            </form>
            <a href="{{ route('aws-accounts.create') }}" class="btn btn-soft-primary">
                <i class="bi bi-plus-lg"></i> 添加账户
            </a>
        </div>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- 统计卡片 -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon stats-icon-primary">
                            <i class="bi bi-cloud-fill"></i>
                        </div>
                        <div class="ms-3">
                            <p class="stats-label">总账户数</p>
                            <h3 class="stats-number">{{ $totalAccounts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon stats-icon-success">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="ms-3">
                            <p class="stats-label">正常账户</p>
                            <h3 class="stats-number">{{ $normalAccounts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon stats-icon-danger">
                            <i class="bi bi-x-circle-fill"></i>
                        </div>
                        <div class="ms-3">
                            <p class="stats-label">封禁账户</p>
                            <h3 class="stats-number">{{ $bannedAccounts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon stats-icon-warning">
                            <i class="bi bi-question-circle-fill"></i>
                        </div>
                        <div class="ms-3">
                            <p class="stats-label">未测账户</p>
                            <h3 class="stats-number">{{ $untestedAccounts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表和最近账户 -->
    <div class="row g-4">
        <!-- 状态分布图表 -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">账户状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="statusChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 每日添加趋势图表 -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">账户添加趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <div id="trendChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近添加的账户 -->
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">最近添加的AWS账户</h5>
                    <a href="{{ route('aws-accounts.index') }}" class="btn btn-sm btn-soft-primary">
                        查看全部
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>账户</th>
                                    <th>状态</th>
                                    <th>添加时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($recentAccounts ?? [] as $account)
                                <tr>
                                    <td>{{ $account->account_name }}</td>
                                    <td>
                                        @if($account->status == 1)
                                            <span class="badge badge-soft-success">正常</span>
                                        @elseif($account->status == 2)
                                            <span class="badge badge-soft-danger">封禁</span>
                                        @elseif($account->status == 3)
                                            <span class="badge badge-soft-warning">无效</span>
                                        @else
                                            <span class="badge badge-soft-secondary">未测</span>
                                        @endif
                                    </td>
                                    <td>{{ $account->created_at->format('Y-m-d H:i:s') }}</td>
                                    <td>
                                        <a href="{{ route('aws-accounts.edit', $account) }}" class="btn btn-sm btn-soft-primary">
                                            编辑
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 状态分布图表
    const statusOptions = {
        series: [{{ $normalAccounts }}, {{ $bannedAccounts }}, {{ $untestedAccounts }}],
        chart: {
            type: 'donut',
            height: '100%'
        },
        labels: ['正常', '封禁', '未测试'],
        colors: ['#2ed47a', '#f25767', '#6c757d'],
        legend: {
            position: 'bottom'
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    const statusChart = new ApexCharts(document.querySelector("#statusChart"), statusOptions);
    statusChart.render();

    // 账户增长趋势图表
    const growthData = @json($growthStats ?? []);
    const trendOptions = {
        series: [{
            name: '账户总数',
            data: growthData.map(item => item.count)
        }],
        chart: {
            type: 'area',
            height: '100%',
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth'
        },
        xaxis: {
            type: 'category',
            categories: growthData.map(item => item.date)
        },
        tooltip: {
            x: {
                format: 'yyyy-MM-dd'
            }
        },
        colors: ['#4361ee']
    };
    const trendChart = new ApexCharts(document.querySelector("#trendChart"), trendOptions);
    trendChart.render();
});
</script>
@endpush 