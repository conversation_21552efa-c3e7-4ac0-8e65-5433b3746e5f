<?php

/**
 * AWS AMI数据配置 - 美洲地区
 * 包含美国、加拿大、墨西哥、南美等地区
 */

return [
    'us-east-1' => [
        'name' => '美国东部（弗吉尼亚北部）',
        'code' => 'us-east-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a634ae95e11c6f91', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0a634ae95e11c6f91', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0a634ae95e11c6f91', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'us-east-2' => [
        'name' => '美国东部（俄亥俄）',
        'code' => 'us-east-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-085f9c64a9b75eed5', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0a91cd140a1fc148a', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'us-west-1' => [
        'name' => '美国西部（加利福尼亚北部）',
        'code' => 'us-west-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0827b6c5b977c020e', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0d382e80be7ffdae5', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0827b6c5b977c020e', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0d382e80be7ffdae5', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0827b6c5b977c020e', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0d382e80be7ffdae5', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0827b6c5b977c020e', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0d382e80be7ffdae5', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0827b6c5b977c020e', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0d382e80be7ffdae5', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'us-west-2' => [
        'name' => '美国西部（俄勒冈）',
        'code' => 'us-west-2',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 20.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0892d3c7ee96c0bf7', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0aff18ec83b712f05', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0892d3c7ee96c0bf7', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ca-central-1' => [
        'name' => '加拿大（中部）',
        'code' => 'ca-central-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0a7154091c5c6623e', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0a7154091c5c6623e', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0a7154091c5c6623e', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'ca-west-1' => [
        'name' => '加拿大西部（卡尔加里）',
        'code' => 'ca-west-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'mx-central-1' => [
        'name' => '墨西哥（中部）',
        'code' => 'mx-central-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ],
    'sa-east-1' => [
        'name' => '南美洲（圣保罗）',
        'code' => 'sa-east-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 22.04 LTS']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 8.10']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ]
        ]
    ]
];
