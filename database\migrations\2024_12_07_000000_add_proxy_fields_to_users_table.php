<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('proxy_mode', ['local', 'proxy'])->default('local')->comment('代理模式');
            $table->enum('proxy_type', ['http', 'socks5'])->nullable()->comment('代理类型');
            $table->string('proxy_host')->nullable()->comment('代理主机');
            $table->integer('proxy_port')->nullable()->comment('代理端口');
            $table->string('proxy_username')->nullable()->comment('代理用户名');
            $table->text('proxy_password')->nullable()->comment('代理密码(加密)');
            $table->enum('proxy_status', ['active', 'inactive', 'testing', 'error'])->default('inactive')->comment('代理状态');
            $table->timestamp('proxy_last_test')->nullable()->comment('最后测试时间');
            $table->text('proxy_error_message')->nullable()->comment('代理错误信息');
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'proxy_mode',
                'proxy_type', 
                'proxy_host',
                'proxy_port',
                'proxy_username',
                'proxy_password',
                'proxy_status',
                'proxy_last_test',
                'proxy_error_message'
            ]);
        });
    }
};
