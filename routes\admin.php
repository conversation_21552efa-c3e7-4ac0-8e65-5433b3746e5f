<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\ActivationCodeController;

Route::middleware('guest:admin')->group(function () {
    Route::get('login', [AuthController::class, 'showLoginForm'])->name('admin.login');
    Route::post('login', [AuthController::class, 'login']);
});

Route::middleware('auth:admin')->group(function () {
    Route::post('logout', [AuthController::class, 'logout'])->name('admin.logout');
    
    Route::get('/', [DashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('profile', [\App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('admin.profile');
    Route::patch('profile', [\App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('admin.profile.update');

    // 用户管理
    Route::resource('users', UserController::class)->names([
        'index' => 'admin.users.index',
        'create' => 'admin.users.create',
        'store' => 'admin.users.store',
        'edit' => 'admin.users.edit',
        'update' => 'admin.users.update',
        'destroy' => 'admin.users.destroy',
    ]);
    Route::post('users/{user}/ban', [UserController::class, 'ban'])->name('admin.users.ban');
    Route::post('users/{user}/unban', [UserController::class, 'unban'])->name('admin.users.unban');

    // 激活码管理
    Route::resource('activation-codes', ActivationCodeController::class)->names([
        'index' => 'admin.activation-codes.index',
        'create' => 'admin.activation-codes.create',
        'store' => 'admin.activation-codes.store',
        'destroy' => 'admin.activation-codes.destroy',
    ]);
    Route::post('activation-codes/batch', [ActivationCodeController::class, 'batchGenerate'])
        ->name('admin.activation-codes.batch');

    // 系统设置
    Route::get('settings', [SettingController::class, 'index'])->name('admin.settings.index');
    Route::put('settings', [SettingController::class, 'update'])->name('admin.settings.update');
}); 