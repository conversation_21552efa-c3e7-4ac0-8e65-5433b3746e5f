<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            // 修改加密字段为mediumtext类型，可以存储最多16MB的数据
            $table->mediumText('email_password')->change();
            $table->mediumText('aws_password')->change();
            $table->mediumText('secret_key')->change();
            $table->mediumText('access_key')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->string('email_password')->change();
            $table->string('aws_password')->change();
            $table->string('secret_key')->change();
            $table->string('access_key')->change();
        });
    }
}; 