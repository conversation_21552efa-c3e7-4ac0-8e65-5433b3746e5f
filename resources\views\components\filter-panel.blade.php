@props(['statusOptions' => [], 'showStatus' => true, 'showPerPage' => true])

<div class="row justify-content-center mb-3">
    <div class="col-md-2">
        <select class="form-select" id="timeRange">
            <option value="all" {{ !request('time_range') ? 'selected' : '' }}>全部时间</option>
            <option value="today" {{ request('time_range') == 'today' ? 'selected' : '' }}>今天</option>
            <option value="yesterday" {{ request('time_range') == 'yesterday' ? 'selected' : '' }}>昨天</option>
            <option value="custom" {{ request('time_range') == 'custom' ? 'selected' : '' }}>自定义</option>
        </select>
    </div>
    <div class="col-md-4" id="dateRangeContainer" style="{{ request('time_range') == 'custom' ? 'display: block;' : 'display: none;' }}">
        <div class="input-group">
            <input type="date" class="form-control" id="startDate" value="{{ request('start_date') }}">
            <span class="input-group-text">至</span>
            <input type="date" class="form-control" id="endDate" value="{{ request('end_date') }}">
        </div>
    </div>
    @if($showStatus)
    <div class="col-md-2">
        <select class="form-select" id="accountStatus">
            <option value="all" {{ !request('status') ? 'selected' : '' }}>全部状态</option>
            @foreach($statusOptions as $value => $label)
                <option value="{{ $value }}" {{ request('status') === (string)$value ? 'selected' : '' }}>
                    {{ $label }}
                </option>
            @endforeach
        </select>
    </div>
    @endif
    <div class="col-md-3">
        <div class="input-group">
            <span class="input-group-text">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control" id="searchInput" placeholder="输入邮箱搜索" value="{{ request('search') }}">
            <button type="button" class="btn btn-primary" id="searchBtn">搜索</button>
            <button type="button" class="btn btn-secondary" id="resetBtn">重置</button>
        </div>
    </div>
    @if($showPerPage)
    <div class="d-flex align-items-center">
        <label class="me-2 mb-0">每页显示：</label>
        <select class="form-select form-select-sm" style="width: auto" id="perPageSelect">
            @foreach([10, 20, 50, 100, 200, 500, 1000] as $size)
                <option value="{{ $size }}" {{ request('per_page', 10) == $size ? 'selected' : '' }}>{{ $size }}条</option>
            @endforeach
        </select>
    </div>
    @endif
</div>

@push('styles')
<style>
/* 确保所有按钮都有完整的边框 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    border: 1px solid #4361ee;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    border-color: #3651d4;
    box-shadow: none;
}

.btn-secondary {
    border: 1px solid #6c757d;
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active {
    border-color: #5a6268;
    box-shadow: none;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const timeRange = document.getElementById('timeRange');
    const dateRangeContainer = document.getElementById('dateRangeContainer');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const accountStatus = document.getElementById('accountStatus');
    const searchInput = document.getElementById('searchInput');
    const perPageSelect = document.getElementById('perPageSelect');

    // 时间范围选择
    timeRange.addEventListener('change', function() {
        if (this.value === 'custom') {
            dateRangeContainer.style.display = 'block';
        } else {
            dateRangeContainer.style.display = 'none';
        }
    });

    // 搜索功能
    document.getElementById('searchBtn').addEventListener('click', function() {
        const params = new URLSearchParams(window.location.search);
        
        // 处理搜索关键词
        if (searchInput.value) {
            params.set('search', searchInput.value);
        } else {
            params.delete('search');
        }
        
        // 处理状态
        if (accountStatus && accountStatus.value !== 'all') {
            params.set('status', accountStatus.value);
        } else {
            params.delete('status');
        }
        
        // 处理时间范围
        if (timeRange.value !== 'all') {
            params.set('time_range', timeRange.value);
            if (timeRange.value === 'custom' && startDate.value && endDate.value) {
                params.set('start_date', startDate.value);
                params.set('end_date', endDate.value);
            }
        } else {
            params.delete('time_range');
            params.delete('start_date');
            params.delete('end_date');
        }
        
        // 保持每页显示数量
        if (perPageSelect && perPageSelect.value !== '10') {
            params.set('per_page', perPageSelect.value);
        } else {
            params.delete('per_page');
        }
        
        window.location.href = `${window.location.pathname}?${params.toString()}`;
    });

    // 重置功能
    document.getElementById('resetBtn').addEventListener('click', function() {
        window.location.href = window.location.pathname;
    });

    // 每页显示数量变化
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            const params = new URLSearchParams(window.location.search);
            params.set('per_page', this.value);
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        });
    }
});
</script>
@endpush 