<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Services\ProxyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProxyController extends Controller
{
    protected $proxyService;
    
    public function __construct(ProxyService $proxyService)
    {
        $this->proxyService = $proxyService;
    }
    
    /**
     * 更新代理配置
     */
    public function update(Request $request)
    {
        try {
            Log::info('代理设置更新请求', [
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            // 基础验证
        $request->validate([
            'proxy_mode' => 'required|in:local,free_proxy,proxy',
        ]);

        // 只有在代理模式下才验证代理配置字段
        if ($request->proxy_mode === 'proxy') {
            $request->validate([
                'proxy_type' => 'required|in:http,socks5',
                'proxy_host' => 'required|ip',
                'proxy_port' => 'required|integer|min:1|max:65535',
                'proxy_username' => 'nullable|string|max:255',
                'proxy_password' => 'nullable|string|max:255',
            ]);
        }
        
        $user = Auth::user();
        
        if ($request->proxy_mode === 'local') {
            // 本地模式，清除代理配置
            try {
                DB::table('users')
                    ->where('id', $user->id)
                    ->update([
                        'proxy_mode' => 'local',
                        'proxy_type' => null,
                        'proxy_host' => null,
                        'proxy_port' => null,
                        'proxy_username' => null,
                        'proxy_password' => null,
                        'proxy_status' => 'inactive',
                        'proxy_last_test' => null,
                        'proxy_error_message' => null,
                        'updated_at' => now()
                    ]);
            } catch (\Exception $e) {
                Log::error('切换到本地模式失败', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => '切换到本地模式失败: ' . $e->getMessage()
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => '已切换到本地模式'
            ]);
        }

        if ($request->proxy_mode === 'free_proxy') {
            // 免费代理模式
            if (!Setting::getBool('free_proxy_enabled', false)) {
                return response()->json([
                    'success' => false,
                    'message' => '免费代理功能未启用'
                ], 400);
            }

            // 检查免费代理配置
            $freeProxyHost = Setting::get('free_proxy_host', '');
            $freeProxyPort = Setting::get('free_proxy_port', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                return response()->json([
                    'success' => false,
                    'message' => '免费代理未配置，请联系管理员'
                ], 400);
            }

            try {
                DB::table('users')
                    ->where('id', $user->id)
                    ->update([
                        'proxy_mode' => 'free_proxy',
                        'proxy_type' => null,
                        'proxy_host' => null,
                        'proxy_port' => null,
                        'proxy_username' => null,
                        'proxy_password' => null,
                        'proxy_status' => 'inactive',
                        'proxy_last_test' => null,
                        'proxy_error_message' => null,
                        'updated_at' => now()
                    ]);
            } catch (\Exception $e) {
                Log::error('切换到免费代理模式失败', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => '切换到免费代理模式失败: ' . $e->getMessage()
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => '已切换到免费代理模式'
            ]);
        }
        
        // 代理模式，先测试连接
        $testResult = $this->proxyService->testProxy(
            $request->proxy_type,
            $request->proxy_host,
            $request->proxy_port,
            $request->proxy_username,
            $request->proxy_password
        );

        // 无论测试结果如何，都保存用户配置
        try {
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'proxy_mode' => 'proxy',
                    'proxy_type' => $request->proxy_type,
                    'proxy_host' => $request->proxy_host,
                    'proxy_port' => $request->proxy_port,
                    'proxy_username' => $request->proxy_username,
                    'proxy_password' => $request->proxy_password, // 直接保存用户填写的密码
                    'proxy_status' => $testResult['success'] ? 'active' : 'error',
                    'proxy_last_test' => now(),
                    'proxy_error_message' => $testResult['success'] ? null : $testResult['message'],
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::error('代理配置保存失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '代理配置保存失败: ' . $e->getMessage()
            ], 500);
        }
         
        // 根据测试结果返回不同的消息
        if ($testResult['success']) {
            return response()->json([
                'success' => true,
                'message' => '代理配置保存成功，连接测试正常',
                'proxy_ip' => $testResult['ip'] ?? null
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => '连接测试失败：' . $testResult['message'],
                'proxy_ip' => null,
                'test_failed' => true
            ]);
        }

        } catch (\Exception $e) {
            Log::error('代理设置更新失败', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 测试代理连接
     */
    public function test(Request $request)
    {
        $request->validate([
            'proxy_type' => 'required|in:http,socks5',
            'proxy_host' => 'required|ip',
            'proxy_port' => 'required|integer|min:1|max:65535',
            'proxy_username' => 'nullable|string|max:255',
            'proxy_password' => 'nullable|string|max:255',
        ]);
        
        $result = $this->proxyService->testProxy(
            $request->proxy_type,
            $request->proxy_host,
            $request->proxy_port,
            $request->proxy_username,
            $request->proxy_password
        );
        
        return response()->json($result);
    }
    
    /**
     * 快速切换模式
     */
    public function quickSwitch(Request $request)
    {
        $request->validate([
            'proxy_mode' => 'required|in:local,free_proxy,proxy'
        ]);
        
        $user = Auth::user();
        
        if ($request->proxy_mode === 'local') {
            try {
                DB::table('users')
                    ->where('id', $user->id)
                    ->update([
                        'proxy_mode' => 'local',
                        'proxy_status' => 'inactive',
                        'proxy_error_message' => null,
                        'updated_at' => now()
                    ]);
            } catch (\Exception $e) {
                Log::error('快速切换到本地模式失败', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => '切换失败: ' . $e->getMessage()
                ], 500);
            }
            
            return response()->json([
                'success' => true,
                'message' => '已切换到本地模式'
            ]);
        }

        if ($request->proxy_mode === 'free_proxy') {
            // 检查免费代理是否启用
            if (!Setting::getBool('free_proxy_enabled', false)) {
                return response()->json([
                    'success' => false,
                    'message' => '免费代理功能未启用'
                ], 400);
            }

            // 检查免费代理配置
            $freeProxyHost = Setting::get('free_proxy_host', '');
            $freeProxyPort = Setting::get('free_proxy_port', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                return response()->json([
                    'success' => false,
                    'message' => '免费代理未配置，请联系管理员'
                ], 400);
            }

            try {
                DB::table('users')
                    ->where('id', $user->id)
                    ->update([
                        'proxy_mode' => 'free_proxy',
                        'proxy_status' => 'inactive',
                        'proxy_error_message' => null,
                        'updated_at' => now()
                    ]);
            } catch (\Exception $e) {
                Log::error('快速切换到免费代理模式失败', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => '切换失败: ' . $e->getMessage()
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => '已切换到免费代理模式'
            ]);
        }

        // 切换到代理模式，检查是否已配置
        if (!$user->proxy_host || !$user->proxy_port) {
            return response()->json([
                'success' => false,
                'message' => '请先配置代理服务器'
            ], 400);
        }
        
        try {
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'proxy_mode' => 'proxy',
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::error('快速切换到代理模式失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '切换失败: ' . $e->getMessage()
            ], 500);
        }

        // 切换到代理模式后，自动测试代理连接并更新状态
        $user->refresh(); // 刷新用户数据
        $password = $user->proxy_password;

        $result = $this->proxyService->testProxy(
            $user->proxy_type,
            $user->proxy_host,
            $user->proxy_port,
            $user->proxy_username,
            $password
        );

        // 更新代理状态
        try {
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'proxy_status' => $result['success'] ? 'active' : 'error',
                    'proxy_last_test' => now(),
                    'proxy_error_message' => $result['success'] ? null : $result['message'],
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::error('更新代理状态失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => '已切换到代理模式' . ($result['success'] ? '' : '，但代理连接测试失败')
        ]);
    }
    
    /**
     * 快速测试当前代理
     */
    public function quickTest()
    {
        $user = Auth::user();
        
        if ($user->proxy_mode !== 'proxy') {
            return response()->json([
                'success' => false,
                'message' => '当前不是代理模式'
            ], 400);
        }
        
        if (!$user->proxy_host || !$user->proxy_port) {
            return response()->json([
                'success' => false,
                'message' => '代理配置不完整'
            ], 400);
        }
        
        // 直接使用明文密码
        $password = $user->proxy_password;
        
        // 更新状态为检测中
        try {
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'proxy_status' => 'testing',
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::error('更新代理状态失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '状态更新失败: ' . $e->getMessage()
            ], 500);
        }

        $result = $this->proxyService->testProxy(
            $user->proxy_type,
            $user->proxy_host,
            $user->proxy_port,
            $user->proxy_username,
            $password
        );

        // 更新测试结果
        try {
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'proxy_status' => $result['success'] ? 'active' : 'error',
                    'proxy_last_test' => now(),
                    'proxy_error_message' => $result['success'] ? null : $result['message'],
                    'updated_at' => now()
                ]);
        } catch (\Exception $e) {
            Log::error('更新代理测试结果失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            // 这里不返回错误，因为测试已经完成，只是保存结果失败
        }
        
        return response()->json($result);
    }
}
