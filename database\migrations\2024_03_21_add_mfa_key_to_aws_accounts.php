<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->string('aws_mfa_key')->nullable()->after('secret_key')->comment('AWS MFA密钥');
        });
    }

    public function down()
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->dropColumn('aws_mfa_key');
        });
    }
}; 