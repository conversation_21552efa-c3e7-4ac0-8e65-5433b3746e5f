<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('activation_codes');

        Schema::create('activation_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 16)->unique();
            $table->integer('valid_days');
            $table->boolean('is_used')->default(false);
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by_admin_id')->constrained('admins')->onDelete('cascade');
            $table->timestamp('expires_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activation_codes');
    }
};
