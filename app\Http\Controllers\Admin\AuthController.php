<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        // 如果已登录，直接跳转到仪表盘
        if (Auth::guard('admin')->check()) {
            return redirect()->route('admin.dashboard');
        }

        // 从 Cookie 中获取记住的用户名
        $remembered_username = Cookie::get('admin_remembered_username');
        return view('admin.auth.login', [
            'title' => config('admin.login_title'),
            'adminUrl' => config('admin.url'),
            'remembered_username' => $remembered_username
        ]);
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // 无论登录是否成功,如果勾选了记住用户名,就保存用户名到 Cookie
        if ($request->remember) {
            Cookie::queue('admin_remembered_username', $request->username, 43200); // 保存30天
        } else {
            Cookie::queue(Cookie::forget('admin_remembered_username'));
        }

        if (Auth::guard('admin')->attempt($credentials, $request->filled('remember'))) {
            $request->session()->regenerate();
            
            // 获取intended URL，如果没有则使用仪表盘
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()->withErrors([
            'username' => '用户名或密码错误',
        ])->withInput($request->except('password'));
    }

    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        // 退出后重定向到后台登录页面
        return redirect()->route('admin.login');
    }
} 