<?php

namespace App\Http\Controllers;

use App\Models\AwsAccount;
use App\Services\AwsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use League\Csv\Writer;
use Carbon\Carbon;

class AwsAccountController extends Controller
{
    protected $awsService;

    public function __construct(AwsService $awsService)
    {
        $this->awsService = $awsService;
    }

    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100, 200, 500, 1000]) ? $perPage : 10;
        
        $query = AwsAccount::where('user_id', Auth::id());

        // 搜索条件
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where('account_name', 'like', '%' . $search . '%');
        }

        // 时间范围
        if ($request->filled('time_range')) {
            switch ($request->time_range) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', Carbon::yesterday());
                    break;
                case 'custom':
                    if ($request->filled('start_date') && $request->filled('end_date')) {
                        $query->whereBetween('created_at', [
                            Carbon::parse($request->start_date)->startOfDay(),
                            Carbon::parse($request->end_date)->endOfDay()
                        ]);
                    }
                    break;
            }
        }

        // 状态筛选
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', (int)$request->status);
        }

        $accounts = $query->orderBy('id', 'desc')->paginate($perPage);

        // 保持筛选条件
        $accounts->appends($request->only(['search', 'time_range', 'start_date', 'end_date', 'status', 'per_page']));

        return view('aws-accounts.index', [
            'accounts' => $accounts,
            'perPage' => $perPage
        ]);
    }

    public function create()
    {
        return view('aws-accounts.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'accounts_data' => 'required|string'
        ]);

        // 分割并过滤空行
        $lines = array_filter(explode("\n", trim($request->accounts_data)), function($line) {
            return trim($line) !== '';
        });

        $createdAccounts = [];
        $failedAccounts = [];
        $totalLines = count($lines);
        $successCount = 0;
        $originalLineNumber = 0;

        foreach ($lines as $line) {
            $originalLineNumber++;
            try {
                // 解析账户信息
                if (preg_match('/①(?:微软|谷歌|邮箱)账号：(.*?) ②(?:微软|谷歌|邮箱)密码：(.*?) ③AWS密码：(.*?) ④访问密钥：(.*?) ⑤秘密访问密钥：(.*?) ⑥MFA信息：(.*)/', $line, $matches)) {
                    [, $microsoftAccount, $microsoftPassword, $awsPassword, $accessKey, $secretKey, $mfaKey] = $matches;

                    // 检查账户是否已存在
                    $existingAccount = AwsAccount::where('user_id', Auth::id())
                        ->where('account_name', trim($microsoftAccount))
                        ->first();

                    if ($existingAccount) {
                        $failedAccounts[] = [
                            'email' => trim($microsoftAccount),
                            'reason' => "第 {$originalLineNumber} 行：账户已存在"
                        ];
                        continue;
                    }

                    $account = new AwsAccount([
                        'account_name' => trim($microsoftAccount),
                        'email_password' => trim($microsoftPassword),
                        'aws_password' => trim($awsPassword),
                        'access_key' => trim($accessKey),
                        'secret_key' => trim($secretKey),
                        'aws_mfa_key' => trim($mfaKey),
                    ]);

                    // 设置受保护的字段
                    $account->user_id = Auth::id();
                    $account->status = 0;
                    $account->ec2_status = 0;
                    $account->save();

                    $createdAccounts[] = $account;
                    $successCount++;
                } else {
                    $failedAccounts[] = [
                        'email' => "第 {$originalLineNumber} 行",
                        'reason' => '格式不正确，请确保包含所有必需的字段并使用正确的格式'
                    ];
                }
            } catch (\Exception $e) {
                $failedAccounts[] = [
                    'email' => "第 {$originalLineNumber} 行",
                    'reason' => $e->getMessage()
                ];
            }
        }

        // 清除用户的账户统计缓存（文件缓存）
        if ($successCount > 0) {
            $userId = auth()->id();
            $cacheFile = storage_path("app/cache/account_stats_{$userId}.json");
            if (file_exists($cacheFile)) {
                unlink($cacheFile);
            }
        }

        // 返回导入结果
        return redirect()
            ->route('aws-accounts.index')
            ->with('import_result', [
                'total_lines' => $totalLines,
                'success_count' => $successCount,
                'failed_count' => count($failedAccounts),
                'failed_accounts' => $failedAccounts
            ]);
    }

    public function edit(AwsAccount $awsAccount)
    {
        if ($awsAccount->user_id !== Auth::id()) {
            abort(403, '您没有权限编辑此账户');
        }
        
        // 保存当前页面URL到session
        session(['aws_accounts_previous_url' => url()->previous()]);
        
        return view('aws-accounts.edit', compact('awsAccount'));
    }

    public function update(Request $request, AwsAccount $awsAccount)
    {
        if ($awsAccount->user_id !== Auth::id()) {
            abort(403, '您没有权限更新此账户');
        }

        $validated = $request->validate([
            'account_name' => 'required|email|unique:aws_accounts,account_name,' . $awsAccount->id,
            'email_password' => 'required',
            'aws_password' => 'required',
            'access_key' => 'required',
            'secret_key' => 'required',
            'aws_mfa_key' => 'nullable',
        ]);

        $awsAccount->update($validated);

        // 获取之前保存的URL，如果没有则使用默认列表页
        $previousUrl = session('aws_accounts_previous_url', route('aws-accounts.index'));
        session()->forget('aws_accounts_previous_url');

        return redirect($previousUrl)
            ->with('success', '账户更新成功');
    }

    public function destroy(AwsAccount $awsAccount)
    {
        if ($awsAccount->user_id !== Auth::id()) {
            abort(403, '您没有权限删除此账户');
        }
        
        $previousUrl = url()->previous();
        $awsAccount->delete();

        // 清除用户的账户统计缓存（文件缓存）
        $userId = auth()->id();
        $cacheFile = storage_path("app/cache/account_stats_{$userId}.json");
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }

        if (request()->wantsJson()) {
            return response()->json(['message' => '账户删除成功']);
        }

        return redirect($previousUrl)
            ->with('success', '账户删除成功');
    }

    public function batchDelete(Request $request)
    {
        $ids = explode(',', $request->input('ids'));
        $accounts = AwsAccount::whereIn('id', $ids)
            ->where('user_id', Auth::id())
            ->get();

        foreach ($accounts as $account) {
            $account->delete();
        }

        // 清除用户的账户统计缓存（文件缓存）
        if (count($accounts) > 0) {
            $userId = auth()->id();
            $cacheFile = storage_path("app/cache/account_stats_{$userId}.json");
            if (file_exists($cacheFile)) {
                unlink($cacheFile);
            }
        }

        return response()->json(['message' => '删除成功']);
    }

    public function export(Request $request)
    {
        $ids = explode(',', $request->input('ids'));
        $accounts = AwsAccount::whereIn('id', $ids)
            ->where('user_id', Auth::id())
            ->orderBy('id', 'desc')
            ->get();

        $csv = Writer::createFromString('');
        $csv->insertOne([
            '邮箱',
            '邮箱密码',
            'AWS密码',
            '访问密钥',
            '秘密访问密钥',
            'MFA信息',
            '状态',
            '测号时间',
            '配额',
            '账户ID',
            'IAM用户名',
            'IAM密码',
            'IAM访问密钥',
            'IAM秘密密钥',
            'IAM创建状态'
        ]);

        foreach ($accounts as $account) {
            $csv->insertOne([
                $account->account_name,
                $account->email_password,
                $account->aws_password,
                $account->access_key,
                $account->secret_key,
                $account->aws_mfa_key ?: '',
                $account->status_text,
                $account->last_check_at?->format('Y-m-d H:i:s') ?: '',
                $account->quota ?: '',
                $account->aws_account_id ?: '',
                $account->iam_username ?: '',
                $account->iam_password ?: '',
                $account->iam_access_key ?: '',
                $account->iam_secret_key ?: '',
                $account->iam_status ? ($account->iam_status === 'success' ? '成功' : ($account->iam_status === 'failed' ? '失败' : '')) : ''
            ]);
        }

        $filename = 'aws_' . now()->format('Y_m_d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        return Response::make($csv->toString(), 200, $headers);
    }

    public function check(Request $request)
    {
        try {
            $ids = $request->input('ids');
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }
            
            $accounts = AwsAccount::whereIn('id', $ids)
                ->where('user_id', Auth::id())
                ->get();

            $normalCount = 0;
            $bannedCount = 0;
            $invalidCount = 0;
            $currentTime = now();

            foreach ($accounts as $account) {
                try {
                    \Log::info("开始处理账号: {$account->account_name}", [
                        'access_key' => $account->access_key,
                        'secret_key' => substr($account->secret_key, 0, 10) . '...'
                    ]);

                    // 如果账户状态为2或3，直接归类到失败
                    if (in_array($account->status, [2, 3])) {
                        \Log::info("账户状态异常(status={$account->status})，跳过测试");
                        if ($account->status == 2) {
                            $bannedCount++;
                        } else {
                            $invalidCount++;
                        }
                        $account->last_check_at = $currentTime;
                        $account->save();
                        continue;
                    }
                    
                    // 只对状态为0或1的账户进行测试
                    $result = $this->awsService->checkAccount($account);
                    \Log::info("账号测试结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));

                    // 如果是代理错误，不修改账户状态，直接跳过
                    if (isset($result['status']) && $result['status'] === 'proxy_error') {
                        \Log::warning("代理配置错误，跳过账户状态更新", [
                            'account_id' => $account->id,
                            'error' => $result['error']
                        ]);
                        continue; // 跳过这个账户，不修改状态
                    }

                    // 更新账号状态
                    $account->status = $result['status']; // 1=正常, 2=封禁, 3=无效
                    $account->last_check_at = $currentTime;

                    // 如果有错误信息，保存到备注
                    if (isset($result['error'])) {
                        $account->remarks = $result['error'];
                    }

                    $account->save();
                    
                    // 统计各种状态的账号数量
                    switch ($result['status']) {
                        case 1:
                            $normalCount++;
                            break;
                        case 2:
                            $bannedCount++;
                            break;
                        case 3:
                            $invalidCount++;
                            break;
                    }
                } catch (\Exception $e) {
                    \Log::error("账号测试异常: " . $e->getMessage());

                    // 检查是否是代理异常错误
                    if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                           strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                        // 代理异常时，直接抛出异常，不修改账户状态
                        throw $e;
                    }

                    // 处理其他异常（如网络错误等）
                    $account->status = 3; // 无效
                    $account->remarks = $e->getMessage();
                    $account->last_check_at = $currentTime;
                    $account->save();
                    $invalidCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => sprintf(
                    "测试完成！\n正常账户：%d\n封禁账户：%d\n无效账户：%d",
                    $normalCount,
                    $bannedCount,
                    $invalidCount
                )
            ]);
        } catch (\Exception $e) {
            \Log::error("账号检测失败: " . $e->getMessage());

            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }

            return response()->json([
                'success' => false,
                'message' => '操作失败：' . $e->getMessage()
            ], 500);
        }
    }

    public function checkQuotas(Request $request)
    {
        try {
            $ids = $request->input('ids');
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }
            
            $accounts = AwsAccount::whereIn('id', $ids)
                ->where('user_id', Auth::id())
                ->get();

            $successCount = 0;
            $failedCount = 0;
            $currentTime = now();

            foreach ($accounts as $account) {
                try {
                    \Log::info("开始检测账户配额: {$account->account_name}");
                    
                    // 1. 首先检查账户状态是否为2或3
                    if (in_array($account->status, [2, 3])) {
                        \Log::info("账户状态异常(status={$account->status})，跳过配额检测");
                        $failedCount++;
                        $account->quota = "0 x 0";
                        $account->last_check_at = $currentTime;
                        $account->save();
                        continue;
                    }

                    // 2. 如果账户状态为0或1，先进行测号
                    if (in_array($account->status, [0, 1])) {
                        $checkResult = $this->awsService->checkAccount($account);
                        \Log::info("账户测试结果: " . json_encode($checkResult, JSON_UNESCAPED_UNICODE));
                        
                        // 更新账户状态
                        $account->status = $checkResult['status'];
                        $account->last_check_at = $currentTime;
                        
                        // 如果有错误信息，保存到备注
                        if (isset($checkResult['error'])) {
                            $account->remarks = $checkResult['error'];
                        }
                        
                        // 3. 如果测号结果不是正常(status=1)，则停止检测
                        if ($checkResult['status'] !== 1) {
                            \Log::info("账户测试未通过，跳过配额检测");
                            $failedCount++;
                            $account->quota = "0 x 0";
                            $account->save();
                            continue;
                        }
                    }

                    // 4. 只有状态为1的账户才继续检测配额
                    $result = $this->awsService->checkQuotas($account);
                    \Log::info("配额检测结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE));
                    
                    if (isset($result['error']) && $result['error']) {
                        throw new \Exception($result['error']);
                    }
                    
                    $account->quota = $result['quota'];
                    $account->save();
                    
                    $successCount++;
                } catch (\Exception $e) {
                    \Log::error("配额检测失败: " . $e->getMessage());

                    // 检查是否是代理异常错误
                    if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                           strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                        // 代理异常时，直接抛出异常，不修改账户状态
                        throw $e;
                    }

                    $failedCount++;
                    $account->quota = "0 x 0";
                    $account->remarks = mb_convert_encoding($e->getMessage(), 'UTF-8', 'UTF-8');
                    $account->last_check_at = $currentTime;
                    $account->save();
                }
            }

            return response()->json([
                'success' => true,
                'message' => sprintf(
                    "检测完成！\n成功：%d\n失败：%d",
                    $successCount,
                    $failedCount
                )
            ], 200, [], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
        } catch (\Exception $e) {
            \Log::error("配额检测失败: " . $e->getMessage());

            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400, [], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
            }

            return response()->json([
                'success' => false,
                'message' => '操作失败：' . mb_convert_encoding($e->getMessage(), 'UTF-8', 'UTF-8')
            ], 500, [], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
        }
    }

    public function enableRegion(Request $request)
    {
        try {
            $ids = explode(',', $request->input('ids'));
            $region = $request->input('region');
            $currentTime = now();
            
            // 获取所有选中的账户
            $accounts = AwsAccount::whereIn('id', $ids)
                ->where('user_id', Auth::id())
                ->get();

            $successCount = 0;
            $failedCount = 0;

            foreach ($accounts as $account) {
                try {
                    \Log::info("开始处理账户: {$account->account_name}");
                    
                    // 1. 首先检查账户状态是否为2或3
                    if (in_array($account->status, [2, 3])) {
                        \Log::info("账户状态异常(status={$account->status})，跳过区域开通");
                        $failedCount++;
                        $account->update([
                            'error_message' => '账户状态异常，无法开通区域',
                            'last_check_at' => $currentTime
                        ]);
                        continue;
                    }

                    // 2. 如果账户状态为0或1，先进行测号
                    if (in_array($account->status, [0, 1])) {
                        $checkResult = $this->awsService->checkAccount($account);
                        \Log::info("账户测试结果: " . json_encode($checkResult, JSON_UNESCAPED_UNICODE));
                        
                        // 更新账户状态和相关信息
                        $updateData = [
                            'status' => $checkResult['status'],
                            'last_check_at' => $currentTime
                        ];
                        
                        // 如果有错误信息，保存到备注
                        if (isset($checkResult['error'])) {
                            $updateData['remarks'] = $checkResult['error'];
                        }
                        
                        $account->update($updateData);
                        
                        // 3. 如果测号结果不是正常(status=1)，则停止开通
                        if ($checkResult['status'] !== 1) {
                            \Log::info("账户测试未通过，跳过区域开通");
                            $failedCount++;
                            $account->update([
                                'error_message' => '账户测试未通过，无法开通区域'
                            ]);
                            continue;
                        }
                    }

                    // 4. 只有状态为1的账户才继续开通区域
                    \Log::info("开始开通区域: {$region}");
                    $result = $this->awsService->enableRegion($account, $region);
                
                    if ($result['success']) {
                        // 更新已开通的区域列表
                        $enabledRegions = json_decode($account->enabled_regions ?? '[]', true);
                        if (!in_array($region, $enabledRegions)) {
                            $enabledRegions[] = $region;
                        }

                        $account->update([
                            'enabled_regions' => json_encode($enabledRegions),
                            'error_message' => null
                        ]);
                
                        $successCount++;
                    } else {
                        throw new \Exception($result['error'] ?? '开通失败');
                    }
                } catch (\Exception $e) {
                    \Log::error("开通区域失败: {$account->account_name}, 错误: " . $e->getMessage());
                    $failedCount++;
                    
                    $account->update([
                        'error_message' => $e->getMessage(),
                        'last_check_at' => $currentTime
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'message' => sprintf(
                    "开通完成！\n成功：%d\n失败：%d",
                    $successCount,
                    $failedCount
                )
            ]);
        } catch (\Exception $e) {
            \Log::error("开通区域功能异常: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '操作失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 根据ID数组获取账户信息
     */
    public function getByIds(Request $request)
    {
        $request->validate([
            'account_ids' => 'required|array',
            'account_ids.*' => 'integer'
        ]);

        $user = Auth::user();
        $accounts = AwsAccount::where('user_id', $user->id)
            ->whereIn('id', $request->account_ids)
            ->whereIn('status', [0, 1]) // 显示未测试和正常状态的账户
            ->select('id', 'account_name', 'created_at', 'status')
            ->get();

        return response()->json([
            'success' => true,
            'accounts' => $accounts
        ]);
    }
}