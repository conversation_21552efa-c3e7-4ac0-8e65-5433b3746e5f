<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Aws\Ec2\Ec2Client;
use Aws\Exception\AwsException;
use App\Models\AwsAccount;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class UpdateAmiData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aws:update-ami-data {--force : 强制更新，忽略缓存}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新AWS AMI数据，获取最新的官方AMI ID';

    /**
     * AWS地区列表
     */
    private $regions = [
        'us-east-1' => '美国东部（弗吉尼亚北部）',
        'us-east-2' => '美国东部（俄亥俄）',
        'us-west-1' => '美国西部（加利福尼亚北部）',
        'us-west-2' => '美国西部（俄勒冈）',
        'af-south-1' => '非洲（开普敦）',
        'ap-east-1' => '亚太地区（香港）',
        'ap-south-1' => '亚太地区（孟买）',
        'ap-south-2' => '亚太地区（海德拉巴）',
        'ap-northeast-1' => '亚太地区（东京）',
        'ap-northeast-2' => '亚太地区（首尔）',
        'ap-northeast-3' => '亚太地区（大阪）',
        'ap-southeast-1' => '亚太地区（新加坡）',
        'ap-southeast-2' => '亚太地区（悉尼）',
        'ap-southeast-3' => '亚太地区（雅加达）',
        'ap-southeast-4' => '亚太地区（墨尔本）',
        'ca-central-1' => '加拿大（中部）',
        'ca-west-1' => '加拿大西部（卡尔加里）',
        'eu-central-1' => '欧洲（法兰克福）',
        'eu-central-2' => '欧洲（苏黎世）',
        'eu-west-1' => '欧洲（爱尔兰）',
        'eu-west-2' => '欧洲（伦敦）',
        'eu-west-3' => '欧洲（巴黎）',
        'eu-north-1' => '欧洲（斯德哥尔摩）',
        'eu-south-1' => '欧洲（米兰）',
        'eu-south-2' => '欧洲（西班牙）',
        'il-central-1' => '以色列（特拉维夫）',
        'mx-central-1' => '墨西哥（中部）',
        'me-south-1' => '中东（巴林）',
        'me-central-1' => '中东（阿联酋）',
        'sa-east-1' => '南美洲（圣保罗）'
    ];

    /**
     * 操作系统查询配置
     */
    private $osQueries = [
        'Amazon Linux' => [
            'Amazon Linux 2023' => ['amzn2023-ami-*', 'amazon'],
            'Amazon Linux 2' => ['amzn2-ami-hvm-*', 'amazon']
        ],
        'Ubuntu' => [
            'Ubuntu 24.04 LTS' => ['ubuntu/images/hvm-ssd/ubuntu-noble-24.04-*', '099720109477'],
            'Ubuntu 22.04 LTS' => ['ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-*', '099720109477'],
            'Ubuntu 20.04 LTS' => ['ubuntu/images/hvm-ssd/ubuntu-focal-20.04-*', '099720109477'],
            'Ubuntu 18.04 LTS' => ['ubuntu/images/hvm-ssd/ubuntu-bionic-18.04-*', '099720109477']
        ],
        'Windows Server' => [
            'Windows Server 2025' => ['Windows_Server-2025-English-Full-Base-*', 'amazon'],
            'Windows Server 2022' => ['Windows_Server-2022-English-Full-Base-*', 'amazon'],
            'Windows Server 2019' => ['Windows_Server-2019-English-Full-Base-*', 'amazon'],
            'Windows Server 2016' => ['Windows_Server-2016-English-Full-Base-*', 'amazon']
        ],
        'Red Hat' => [
            'RHEL 9.4' => ['RHEL-9.4*', '309956199498'],
            'RHEL 8.10' => ['RHEL-8.10*', '309956199498'],
            'RHEL 7.9' => ['RHEL-7.9*', '309956199498']
        ],
        'SUSE Linux' => [
            'SUSE Linux Enterprise Server 15 SP6' => ['suse-sles-15-sp6-*', '013907871322'],
            'SUSE Linux Enterprise Server 15 SP5' => ['suse-sles-15-sp5-*', '013907871322'],
            'SUSE Linux Enterprise Server 12 SP5' => ['suse-sles-12-sp5-*', '013907871322']
        ],
        'Debian' => [
            'Debian 12' => ['debian-12-*', '136693071363'],
            'Debian 11' => ['debian-11-*', '136693071363']
        ],
        'CentOS' => [
            'CentOS Stream 9' => ['CentOS Stream 9*', '125523088429'],
            'CentOS Stream 8' => ['CentOS Stream 8*', '125523088429']
        ],
        'Oracle Linux' => [
            'Oracle Linux 9' => ['OL9*', '131827586825'],
            'Oracle Linux 8' => ['OL8*', '131827586825']
        ],
        'Rocky Linux' => [
            'Rocky Linux 9' => ['Rocky-9-*', '************'],
            'Rocky Linux 8' => ['Rocky-8-*', '************']
        ],
        'AlmaLinux' => [
            'AlmaLinux 9' => ['AlmaLinux OS 9*', '************'],
            'AlmaLinux 8' => ['AlmaLinux OS 8*', '************']
        ]
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新AWS AMI数据...');
        
        // 检查是否有可用的AWS账户
        $account = AwsAccount::where('status', 1)->first();
        if (!$account) {
            $this->error('没有找到可用的AWS账户');
            return 1;
        }

        $force = $this->option('force');
        $cacheKey = 'aws_ami_real_data';
        
        // 检查缓存
        if (!$force && Cache::has($cacheKey)) {
            $this->info('AMI数据已缓存，使用 --force 参数强制更新');
            return 0;
        }

        $amiData = [];
        $totalRegions = count($this->regions);
        $currentRegion = 0;

        foreach ($this->regions as $regionCode => $regionName) {
            $currentRegion++;
            $this->info("处理地区 {$currentRegion}/{$totalRegions}: {$regionName} ({$regionCode})");
            
            try {
                $regionData = $this->getRegionAmiData($regionCode, $account);
                if (!empty($regionData)) {
                    $amiData[$regionCode] = [
                        'name' => $regionName,
                        'code' => $regionCode,
                        'systems' => $regionData,
                        'last_updated' => now()->toISOString()
                    ];
                    $this->info("✅ {$regionName} 数据获取成功");
                } else {
                    $this->warn("⚠️ {$regionName} 没有获取到AMI数据");
                }
            } catch (\Exception $e) {
                $this->error("❌ {$regionName} 数据获取失败: " . $e->getMessage());
                Log::error("AMI数据获取失败 - {$regionCode}: " . $e->getMessage());
            }
            
            // 避免API限制，添加延迟
            sleep(1);
        }

        if (!empty($amiData)) {
            // 缓存数据24小时
            Cache::put($cacheKey, $amiData, 24 * 60 * 60);
            $this->info('✅ AMI数据更新完成并已缓存');
            
            // 可选：保存到文件
            $this->saveToFile($amiData);
        } else {
            $this->error('❌ 没有获取到任何AMI数据');
            return 1;
        }

        return 0;
    }

    /**
     * 获取指定地区的AMI数据
     */
    private function getRegionAmiData($region, $account)
    {
        $ec2Client = $this->createEc2Client($region, $account);
        $regionData = [];

        foreach ($this->osQueries as $osName => $versions) {
            $osData = [
                'name' => $osName,
                'versions' => []
            ];

            foreach ($versions as $versionName => $queryConfig) {
                [$namePattern, $owner] = $queryConfig;
                
                try {
                    $ami = $this->getLatestAmi($ec2Client, $namePattern, $owner);
                    if ($ami) {
                        $osData['versions'][] = [
                            'name' => $versionName,
                            'ami_id' => $ami['ImageId'],
                            'display_name' => $versionName,
                            'description' => $ami['Description'] ?? '',
                            'creation_date' => $ami['CreationDate'] ?? ''
                        ];
                    }
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他版本
                    Log::warning("获取AMI失败 - {$region}/{$osName}/{$versionName}: " . $e->getMessage());
                }
            }

            if (!empty($osData['versions'])) {
                $regionData[$osName] = $osData;
            }
        }

        return $regionData;
    }

    /**
     * 创建EC2客户端
     */
    private function createEc2Client($region, $account)
    {
        $config = [
            'region' => $region,
            'version' => 'latest',
            'credentials' => [
                'key' => $account->access_key,
                'secret' => $account->secret_key,
            ]
        ];

        // 代理配置
        if (config('aws.proxy_mode') === 'proxy') {
            $config['http'] = [
                'proxy' => config('aws.proxy_ip') . ':' . config('aws.proxy_port'),
                'timeout' => 30,
                'connect_timeout' => 10
            ];
        }

        // SSL验证配置
        if (config('aws.disable_ssl_verification', false)) {
            $config['http']['verify'] = false;
        }

        return new Ec2Client($config);
    }

    /**
     * 获取最新的AMI
     */
    private function getLatestAmi($ec2Client, $namePattern, $owner)
    {
        $result = $ec2Client->describeImages([
            'Owners' => [$owner],
            'Filters' => [
                [
                    'Name' => 'name',
                    'Values' => [$namePattern]
                ],
                [
                    'Name' => 'state',
                    'Values' => ['available']
                ],
                [
                    'Name' => 'architecture',
                    'Values' => ['x86_64']
                ]
            ]
        ]);

        $images = $result['Images'];
        if (empty($images)) {
            return null;
        }

        // 按创建日期排序，获取最新的
        usort($images, function($a, $b) {
            return strcmp($b['CreationDate'], $a['CreationDate']);
        });

        return $images[0];
    }

    /**
     * 保存数据到文件（可选）
     */
    private function saveToFile($amiData)
    {
        $filePath = storage_path('app/ami_data_real.json');
        file_put_contents($filePath, json_encode($amiData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        $this->info("数据已保存到: {$filePath}");
    }
}
