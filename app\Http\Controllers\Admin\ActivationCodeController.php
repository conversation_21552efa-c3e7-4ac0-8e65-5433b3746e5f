<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivationCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ActivationCodeController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 10;
        
        $activationCodes = ActivationCode::with(['user', 'createdByAdmin'])
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return view('admin.activation-codes.index', compact('activationCodes', 'perPage'));
    }

    public function create()
    {
        return view('admin.activation-codes.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'count' => 'required|integer|min:1|max:100',
            'expires_in' => 'required|integer|min:1|max:365',
        ]);

        $codes = [];
        $now = Carbon::now();
        $count = (int) $request->count;
        $expiresIn = (int) $request->expires_in;

        for ($i = 0; $i < $count; $i++) {
            $codes[] = [
                'code' => strtoupper(Str::random(16)),
                'valid_days' => $expiresIn,
                'created_by_admin_id' => auth()->id(),
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        ActivationCode::insert($codes);

        return redirect()
            ->route('admin.activation-codes.index')
            ->with('success', '激活码生成成功');
    }

    public function destroy(ActivationCode $activationCode)
    {
        if (!$activationCode->canBeDeleted()) {
            return back()->with('error', '无法删除已使用或已过期的激活码');
        }

        $activationCode->delete();

        return back()->with('success', '激活码删除成功');
    }

    public function batchDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|exists:activation_codes,id',
        ]);

        $count = ActivationCode::whereIn('id', $request->ids)
            ->whereNull('user_id')
            ->delete();

        return response()->json([
            'message' => "成功删除 {$count} 个激活码",
        ]);
    }

    public function export(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|exists:activation_codes,id',
        ]);

        $codes = ActivationCode::whereIn('id', $request->ids)
            ->with(['user', 'createdByAdmin'])
            ->get();

        $filename = 'activation-codes-' . now()->format('YmdHis') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($codes) {
            $file = fopen('php://output', 'w');
            // 添加 BOM 头，解决中文乱码
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // 写入表头
            fputcsv($file, [
                '激活码',
                '有效期(天)',
                '过期时间',
                '状态',
                '使用者',
                '生成者',
                '生成时间',
            ]);

            // 写入数据
            foreach ($codes as $code) {
                fputcsv($file, [
                    $code->code,
                    $code->remaining_days,
                    $code->expires_at ? $code->expires_at->format('Y-m-d H:i:s') : '-',
                    $code->status,
                    $code->user ? $code->user->email : '-',
                    $code->createdByAdmin->email,
                    $code->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, Response::HTTP_OK, $headers);
    }
} 