<?php

namespace App\Http\Controllers;

use App\Models\AwsAccount;
use App\Services\AwsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\ActivityLog;

class AwsRegionController extends Controller
{
    protected $awsService;
    protected $regions = [
        'us-east-1' => '美国东部（弗吉尼亚北部）',
        'us-east-2' => '美国东部（俄亥俄）',
        'us-west-1' => '美国西部（加利福尼亚北部）',
        'us-west-2' => '美国西部（俄勒冈）',
        'us-gov-east-1' => 'AWS GovCloud（美国东部）',
        'us-gov-west-1' => 'AWS GovCloud（美国西部）',
        'af-south-1' => '非洲（开普敦）',
        'ap-east-1' => '亚太地区（香港）',
        'ap-south-1' => '亚太地区（孟买）',
        'ap-northeast-1' => '亚太地区（东京）',
        'ap-northeast-2' => '亚太地区（首尔）',
        'ap-northeast-3' => '亚太地区（大阪）',
        'ap-southeast-1' => '亚太地区（新加坡）',
        'ap-southeast-2' => '亚太地区（悉尼）',
        'ap-southeast-3' => '亚太地区（雅加达）',
        'ca-central-1' => '加拿大（中部）',
        'eu-central-1' => '欧洲（法兰克福）',
        'eu-west-1' => '欧洲（爱尔兰）',
        'eu-west-2' => '欧洲（伦敦）',
        'eu-west-3' => '欧洲（巴黎）',
        'eu-south-1' => '欧洲（米兰）',
        'eu-north-1' => '欧洲（斯德哥尔摩）',
        'me-south-1' => '中东（巴林）',
        'me-central-1' => '中东（阿联酋）',
        'sa-east-1' => '南美洲（圣保罗）',
        'ap-southeast-4' => '亚太地区（墨尔本）',
        'eu-central-2' => '欧洲（苏黎世）',
        'eu-south-2' => '欧洲（西班牙）',
        'il-central-1' => '以色列（特拉维夫）'
    ];

    public function __construct(AwsService $awsService)
    {
        $this->awsService = $awsService;
    }

    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        // 确保per_page是允许的值
        $perPage = in_array($perPage, [10, 20, 50, 100, 200, 500, 1000]) ? $perPage : 10;
        
        $query = AwsAccount::where('user_id', Auth::id());

        // 搜索条件
        if ($request->filled('search')) {
            $query->where('account_name', 'like', '%' . $request->search . '%');
        }

        // 时间范围
        if ($request->filled('time_range')) {
            switch ($request->time_range) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', Carbon::yesterday());
                    break;
                case 'custom':
                    if ($request->filled('start_date') && $request->filled('end_date')) {
                        $query->whereBetween('created_at', [
                            Carbon::parse($request->start_date)->startOfDay(),
                            Carbon::parse($request->end_date)->endOfDay()
                        ]);
                    }
                    break;
            }
        }

        // 状态筛选
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', (int)$request->status);
        }

        $accounts = $query->orderBy('id', 'desc')->paginate($perPage);

        // 保持筛选条件
        $accounts->appends($request->only(['search', 'time_range', 'start_date', 'end_date', 'status', 'per_page']));

        // 确保accounts不为null
        if (!$accounts) {
            $accounts = collect([]);
        }

        return view('aws-regions.index', [
            'accounts' => $accounts,
            'regions' => $this->regions,
            'perPage' => $perPage
        ]);
    }

    public function enable(Request $request)
    {
        try {
            \Log::info("收到开通区域请求", [
                'request' => $request->all()
            ]);

            $request->validate([
                'account_ids' => 'required|array',
                'account_ids.*' => 'exists:aws_accounts,id',
                'region' => 'required|string'
            ]);

            \Log::info("开始批量开通区域", [
                'account_ids' => $request->account_ids,
                'region' => $request->region
            ]);

            $accounts = AwsAccount::whereIn('id', $request->account_ids)
                ->where('user_id', auth()->id())
                ->get();

            if ($accounts->isEmpty()) {
                throw new \Exception('未找到有效的账户');
            }

            $results = [
                'success' => [],
                'failed' => []
            ];

            foreach ($accounts as $account) {
                \Log::info("处理账户", [
                    'account_id' => $account->id,
                    'account_name' => $account->account_name
                ]);

                try {
                    \Log::info("开始调用AWS API开通区域", [
                        'account_id' => $account->id,
                        'region' => $request->region
                    ]);

                    // 调用AWS API开通区域
                    $result = $this->awsService->enableRegion(
                        $account->access_key,
                        $account->secret_key,
                        $request->region
                    );

                    \Log::info("AWS API返回结果", [
                        'account_id' => $account->id,
                        'result' => $result
                    ]);

                    if ($result['success']) {
                        $results['success'][] = [
                            'account_id' => $account->id,
                            'message' => '开通成功',
                            'enable_result' => json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                        ];
                        
                        // 更新账户开通结果
                        //$account->enable_status = 'success';
                        $account->enable_result = json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    } else {
                        $results['failed'][] = [
                            'account_id' => $account->id,
                            'message' => '开通失败',
                            'enable_result' => json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                        ];
                        
                        // 更新账户开通结果
                        //$account->enable_status = 'failed';
                        $account->enable_result = json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    }
                    
                    $account->save();

                } catch (\Exception $e) {
                    \Log::error("开通区域出错", [
                        'account_id' => $account->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    // 检查是否是代理异常错误
                    if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                           strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                        \Log::info("检测到代理异常，直接抛出", [
                            'account_id' => $account->id,
                            'error_message' => $e->getMessage()
                        ]);
                        // 直接抛出代理异常，让外层catch处理
                        throw $e;
                    }

                    $error = [
                        'success' => false,
                        'responses' => [
                            'error' => $e->getMessage()
                        ]
                    ];
                    
                    $results['failed'][] = [
                        'account_id' => $account->id,
                        'message' => '开通出错',
                        'enable_result' => json_encode($error, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ];
                    
                    // 更新账户开通结果
                    //$account->enable_status = 'failed';
                    $account->enable_result = json_encode($error, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    $account->save();
                }
            }

            \Log::info("批量开通区域完成", [
                'success_count' => count($results['success']),
                'failed_count' => count($results['failed'])
            ]);

            return response()->json([
                'message' => sprintf('开通完成！成功：%d，失败：%d', 
                    count($results['success']), 
                    count($results['failed'])
                ),
                'results' => $results
            ]);
        } catch (\Exception $e) {
            \Log::error("批量开通区域系统错误", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 检查是否是代理异常错误
            if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                                   strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
                \Log::info("检测到代理异常，返回标准格式", [
                    'error_message' => $e->getMessage()
                ]);
                return response()->json([
                    'success' => false,
                    'error_type' => 'proxy_error',
                    'message' => $e->getMessage()
                ], 400);
            }
            
            return response()->json([
                'message' => '系统错误：' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function batchEnableRegion(Request $request)
    {
        $accountIds = $request->input('account_ids', []);
        $region = $request->input('region');

        \Log::info("开始批量开通区域", [
            'account_ids' => $accountIds,
            'region' => $region
        ]);

        if (empty($accountIds) || empty($region)) {
            return response()->json([
                'message' => '请选择要开通的账户和区域'
            ], 400);
        }

        $accounts = AwsAccount::whereIn('id', $accountIds)->get();
        $results = [
            'success' => [],
            'failed' => []
        ];

        foreach ($accounts as $account) {
            \Log::info("处理账户", [
                'account_id' => $account->id,
                'account_name' => $account->account_name
            ]);

            // 预检查账户状态
            $preCheck = $this->preCheckAccountStatus($account);
            
            if (!$preCheck['success']) {
                \Log::warning("账户预检查失败", [
                    'account_id' => $account->id,
                    'message' => $preCheck['message']
                ]);

                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => $preCheck['message'],
                    'enable_result' => json_encode([
                        'success' => false,
                        'responses' => [
                            'error' => $preCheck['message']
                        ]
                    ])
                ];
                
                // 更新账户开通结果
                //$account->enable_status = 'failed';
                $account->enable_result = json_encode([
                    'success' => false,
                    'responses' => [
                        'error' => $preCheck['message']
                    ]
                ]);
                $account->save();
                
                continue;
            }

            try {
                \Log::info("开始调用AWS API开通区域", [
                    'account_id' => $account->id,
                    'region' => $region
                ]);

                // 调用AWS API开通区域
                $result = app(AwsService::class)->enableRegion(
                    $account->access_key,
                    $account->secret_key,
                    $region
                );

                \Log::info("AWS API返回结果", [
                    'account_id' => $account->id,
                    'result' => $result
                ]);

                if ($result['success']) {
                    $results['success'][] = [
                        'account_id' => $account->id,
                        'message' => '开通成功',
                        'enable_result' => json_encode($result)
                    ];
                    
                    // 更新账户开通结果
                    //$account->enable_status = 'success';
                    $account->enable_result = json_encode($result);
                } else {
                    $results['failed'][] = [
                        'account_id' => $account->id,
                        'message' => '开通失败',
                        'enable_result' => json_encode($result)
                    ];
                    
                    // 更新账户开通结果
                    //$account->enable_status = 'failed';
                    $account->enable_result = json_encode($result);
                }
                
                $account->save();

            } catch (\Exception $e) {
                \Log::error("开通区域出错", [
                    'account_id' => $account->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $error = [
                    'success' => false,
                    'responses' => [
                        'error' => $e->getMessage()
                    ]
                ];
                
                $results['failed'][] = [
                    'account_id' => $account->id,
                    'message' => '开通出错',
                    'enable_result' => json_encode($error)
                ];
                
                // 更新账户开通结果
                //$account->enable_status = 'failed';
                $account->enable_result = json_encode($error);
                $account->save();
            }
        }

        \Log::info("批量开通区域完成", [
            'success_count' => count($results['success']),
            'failed_count' => count($results['failed'])
        ]);

        return response()->json([
            'message' => sprintf('开通完成！成功：%d，失败：%d', 
                count($results['success']), 
                count($results['failed'])
            ),
            'results' => $results
        ]);
    }
} 