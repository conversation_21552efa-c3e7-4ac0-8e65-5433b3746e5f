@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
            <h4 class="mb-0 text-dark">编辑AWS账户</h4>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}" class="text-secondary">首页</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.accounts.index') }}" class="text-secondary">AWS账户管理</a></li>
                <li class="breadcrumb-item active" aria-current="page">编辑账户</li>
            </ol>
        </nav>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.accounts.update', $account) }}" method="POST">
            @csrf
            @method('PUT')
            
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="user_id" class="form-label">所属会员</label>
                <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror" required>
                    <option value="">请选择会员</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ old('user_id', $account->user_id) == $user->id ? 'selected' : '' }}>
                            {{ $user->username }}
                        </option>
                    @endforeach
                </select>
                @error('user_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

                    <div class="col-md-6">
                        <label for="account_name" class="form-label">账户邮箱</label>
                        <input type="text" class="form-control @error('account_name') is-invalid @enderror" 
                               id="account_name" name="account_name" 
                               value="{{ old('account_name', $account->account_name) }}" required>
                        @error('account_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="email_password" class="form-label">邮箱密码</label>
                        <input type="text" class="form-control @error('email_password') is-invalid @enderror" 
                               id="email_password" name="email_password" 
                               value="{{ old('email_password', $account->email_password) }}">
                        @error('email_password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="aws_password" class="form-label">AWS密码</label>
                        <input type="text" class="form-control @error('aws_password') is-invalid @enderror" 
                               id="aws_password" name="aws_password" 
                               value="{{ old('aws_password', $account->aws_password) }}">
                        @error('aws_password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="access_key" class="form-label">访问密钥</label>
                        <input type="text" class="form-control @error('access_key') is-invalid @enderror" 
                               id="access_key" name="access_key" 
                               value="{{ old('access_key', $account->access_key) }}" required>
                        @error('access_key')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

                    <div class="col-md-6">
                        <label for="secret_key" class="form-label">秘密访问密钥</label>
                        <input type="text" class="form-control @error('secret_key') is-invalid @enderror" 
                               id="secret_key" name="secret_key" 
                               value="{{ old('secret_key', $account->secret_key) }}">
                        @error('secret_key')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

                    <div class="col-md-6">
                <label for="status" class="form-label">状态</label>
                <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                            @foreach($statusList as $key => $value)
                                <option value="{{ $key }}" {{ old('status', $account->status) == $key ? 'selected' : '' }}>
                                    {{ $value }}
                                </option>
                            @endforeach
                </select>
                @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

                    <div class="col-md-6">
                        <label for="quota" class="form-label">配额</label>
                        <input type="text" class="form-control @error('quota') is-invalid @enderror" 
                               id="quota" name="quota" 
                               value="{{ old('quota', $account->quota) }}">
                        @error('quota')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

                    <div class="col-md-6">
                        <label for="last_check_at" class="form-label">最后测号时间</label>
                        <input type="datetime-local" class="form-control" id="last_check_at" name="last_check_at" 
                            value="{{ $account->last_check_at ? $account->last_check_at->format('Y-m-d\TH:i') : '' }}">
                    </div>

                    <div class="col-12">
                <label for="remarks" class="form-label">备注</label>
                <textarea class="form-control @error('remarks') is-invalid @enderror" 
                                  id="remarks" name="remarks" rows="3">{{ old('remarks', $account->remarks) }}</textarea>
                @error('remarks')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                    </div>

                    <!-- IAM账户信息 -->
                    <div class="col-12">
                        <h5 class="mb-3 mt-4">IAM账户信息</h5>
                    </div>

                    <div class="col-md-6">
                        <label for="aws_account_id" class="form-label">账户ID</label>
                        <input type="text" class="form-control @error('aws_account_id') is-invalid @enderror" 
                               id="aws_account_id" name="aws_account_id" 
                               value="{{ old('aws_account_id', $account->aws_account_id) }}">
                        @error('aws_account_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="iam_username" class="form-label">IAM用户名</label>
                        <input type="text" class="form-control @error('iam_username') is-invalid @enderror" 
                               id="iam_username" name="iam_username" 
                               value="{{ old('iam_username', $account->iam_username) }}">
                        @error('iam_username')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="iam_password" class="form-label">IAM密码</label>
                        <input type="text" class="form-control @error('iam_password') is-invalid @enderror" 
                               id="iam_password" name="iam_password" 
                               value="{{ old('iam_password', $account->iam_password) }}">
                        @error('iam_password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="iam_access_key" class="form-label">IAM访问密钥</label>
                        <input type="text" class="form-control @error('iam_access_key') is-invalid @enderror" 
                               id="iam_access_key" name="iam_access_key" 
                               value="{{ old('iam_access_key', $account->iam_access_key) }}">
                        @error('iam_access_key')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="iam_secret_key" class="form-label">IAM秘密密钥</label>
                        <input type="text" class="form-control @error('iam_secret_key') is-invalid @enderror" 
                               id="iam_secret_key" name="iam_secret_key" 
                               value="{{ old('iam_secret_key', $account->iam_secret_key) }}">
                        @error('iam_secret_key')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="iam_status" class="form-label">IAM创建状态</label>
                        <select name="iam_status" id="iam_status" class="form-select @error('iam_status') is-invalid @enderror">
                            <option value="success" {{ old('iam_status', $account->iam_status) == 'success' ? 'selected' : '' }}>成功</option>
                            <option value="failed" {{ old('iam_status', $account->iam_status) == 'failed' ? 'selected' : '' }}>失败</option>
                        </select>
                        @error('iam_status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="mt-4 d-flex justify-content-end">
                    <a href="{{ session('admin_accounts_previous_url', route('admin.accounts.index')) }}" class="btn btn-light me-2">返回列表</a>
                    <button type="submit" class="btn btn-primary">保存修改</button>
            </div>
        </form>
        </div>
    </div>
</div>
@endsection 