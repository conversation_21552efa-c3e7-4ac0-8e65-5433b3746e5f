@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 表单控件样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background-color: #f8fafc;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
    background-color: #fff;
}

.form-control:disabled, .form-control[readonly] {
    background-color: #f1f5f9;
    cursor: not-allowed;
}

.form-label {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.btn-primary:hover {
    background-color: #3651d4;
    border-color: #3651d4;
    transform: translateY(-1px);
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* 分隔线 */
.divider {
    height: 1px;
    background: rgba(0,0,0,.05);
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: attr(data-title);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 0 1rem;
    color: #718096;
    font-size: 0.875rem;
}


/* 状态标签 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: #4a5568;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.25rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn:last-child {
        margin-bottom: 0;
    }
}
</style>
@endpush

@section('content')
<div class="container fade-in">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">编辑AWS账户</h4>
                        <p class="text-muted mb-0 mt-1">修改AWS账户信息</p>
                    </div>
                    <a href="{{ route('aws-accounts.index') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="card-body">
                    <form method="POST" action="{{ route('aws-accounts.update', $awsAccount) }}">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="account_name" class="form-label">账户邮箱</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-envelope"></i>
                                </span>
                                <input type="email" class="form-control @error('account_name') is-invalid @enderror" 
                                       id="account_name" name="account_name" value="{{ old('account_name', $awsAccount->account_name) }}">
                            </div>
                            @error('account_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="email_password" class="form-label">邮箱密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-key"></i>
                                </span>
                                <input type="text" class="form-control @error('email_password') is-invalid @enderror" 
                                       id="email_password" name="email_password" value="{{ old('email_password', $awsAccount->email_password) }}">
                            </div>
                            @error('email_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="aws_password" class="form-label">AWS密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input type="text" class="form-control @error('aws_password') is-invalid @enderror" 
                                       id="aws_password" name="aws_password" value="{{ old('aws_password', $awsAccount->aws_password) }}">
                            </div>
                            @error('aws_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="access_key" class="form-label">访问密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-key-fill"></i>
                                </span>
                                <input type="text" class="form-control @error('access_key') is-invalid @enderror" 
                                       id="access_key" name="access_key" value="{{ old('access_key', $awsAccount->access_key) }}">
                            </div>
                            @error('access_key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="secret_key" class="form-label">秘密访问密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-shield-lock"></i>
                                </span>
                                <input type="text" class="form-control @error('secret_key') is-invalid @enderror" 
                                       id="secret_key" name="secret_key" value="{{ old('secret_key', $awsAccount->secret_key) }}">
                            </div>
                            @error('secret_key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="aws_mfa_key" class="form-label">MFA密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-shield-lock"></i>
                                </span>
                                <input type="text" class="form-control @error('aws_mfa_key') is-invalid @enderror" 
                                       id="aws_mfa_key" name="aws_mfa_key" value="{{ old('aws_mfa_key', $awsAccount->aws_mfa_key) }}">
                            </div>
                            @error('aws_mfa_key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="divider" data-title="账户状态"></div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <label class="form-label">状态</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-circle-fill text-{{ $awsAccount->status === 1 ? 'success' : ($awsAccount->status === 2 ? 'danger' : 'warning') }}"></i>
                                        </span>
                                        <input type="text" class="form-control" value="{{ $awsAccount->status_text }}" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-4">
                                    <label class="form-label">操作时间</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-clock"></i>
                                        </span>
                                        <input type="text" class="form-control" value="{{ $awsAccount->last_check_at ? $awsAccount->last_check_at->format('Y-m-d H:i:s') : '未测试' }}" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-4">
                                    <label class="form-label">配额</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-speedometer2"></i>
                                        </span>
                                        <input type="text" class="form-control" value="{{ $awsAccount->quota ?? '未测试' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="divider" data-title="IAM账户信息"></div>
                        <!-- IAM账户信息 -->
                        <div class="mb-4">
                            <label for="aws_account_id" class="form-label">账户ID</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person-badge"></i>
                                </span>
                                <input type="text" class="form-control" id="aws_account_id" value="{{ $awsAccount->aws_account_id }}" readonly>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="iam_username" class="form-label">IAM用户名</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control" id="iam_username" value="{{ $awsAccount->iam_username }}" readonly>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="iam_password" class="form-label">IAM密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-key"></i>
                                </span>
                                <input type="text" class="form-control" id="iam_password" value="{{ $awsAccount->iam_password }}" readonly>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="iam_access_key" class="form-label">IAM访问密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-key-fill"></i>
                                </span>
                                <input type="text" class="form-control" id="iam_access_key" value="{{ $awsAccount->iam_access_key }}" readonly>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="iam_secret_key" class="form-label">IAM秘密密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-shield-lock"></i>
                                </span>
                                <input type="text" class="form-control" id="iam_secret_key" value="{{ $awsAccount->iam_secret_key }}" readonly>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="iam_status" class="form-label">IAM创建状态</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-check-circle"></i>
                                </span>
                                <input type="text" class="form-control" id="iam_status" value="{{ $awsAccount->iam_status == 'success' ? '成功' : ($awsAccount->iam_status == 'failed' ? '失败' : '') }}" readonly>
                            </div>
                        </div>

                        <!--<div class="divider" data-title="MFA设备信息"></div>
                        <div class="mb-4">
                            <label for="aws_mfa_key" class="form-label">MFA密钥</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-shield-lock"></i>
                                </span>
                                <input type="text" class="form-control" id="aws_mfa_key" value="{{ $awsAccount->aws_mfa_key }}" readonly>
                            </div>
                        </div>-->

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check2 me-1"></i>保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 