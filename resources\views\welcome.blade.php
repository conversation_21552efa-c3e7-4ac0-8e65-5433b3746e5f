@extends('layouts.app')

@push('styles')
<style>
/* 移除主容器的默认padding */
main.py-4 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
    margin-top: 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    opacity: 0.8;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    margin-bottom: 3rem;
}

.btn-hero {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    margin: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-hero-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-hero-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-hero-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-2px);
}

/* 功能特性区域 */
.features-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.2rem;
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    height: 100%;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #1e2a78 0%, #ff6a00 100%);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 科技感渐变色彩方案 */
.feature-card:nth-child(1) .feature-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card:nth-child(2) .feature-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-card:nth-child(3) .feature-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.feature-card:nth-child(4) .feature-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.feature-card:nth-child(5) .feature-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.feature-card:nth-child(6) .feature-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.feature-description {
    color: #718096;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    color: #4a5568;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #48bb78;
    font-weight: bold;
}

/* 统计数据区域 */
.stats-section {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 4rem 0;
}

.stat-card {
    text-align: center;
    padding: 2rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: #ff6a00;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ff6a00 0%, #ffa500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease forwards;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s ease forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s ease forwards;
}

.delay-1 { animation-delay: 0.2s; }
.delay-2 { animation-delay: 0.4s; }
.delay-3 { animation-delay: 0.6s; }
.delay-4 { animation-delay: 0.8s; }

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .features-section {
        padding: 3rem 0;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .feature-card {
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .stats-section {
        padding: 3rem 0;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .btn-hero {
        display: block;
        margin: 0.5rem auto;
        text-align: center;
        max-width: 250px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
    }

    .feature-icon i {
        font-size: 1.5rem;
    }
}
</style>
@endpush

@section('content')
<!-- 英雄区域 -->
<div class="hero-section">
    <div class="hero-content">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12 text-center">
                    <h1 class="hero-title animate-fadeInUp">AWS云管理面板</h1>
                    <p class="hero-subtitle animate-fadeInUp delay-1">专业的AWS账户管理解决方案</p>
                    <p class="hero-description animate-fadeInUp delay-2">
                        集成AWS官方API，提供账户管理、区域开通、IAM创建、EC2实例管理等全方位服务，
                        让您的AWS资源管理更加高效、安全、便捷。
                    </p>
                    <div class="cta-buttons animate-fadeInUp delay-3">
                        @guest
                            <a href="{{ route('login') }}" class="btn-hero btn-hero-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>立即登录
                            </a>
                            @if(\App\Models\Setting::getBool('register_enabled', true))
                            <a href="{{ route('register') }}" class="btn-hero btn-hero-secondary">
                                <i class="bi bi-person-plus me-2"></i>免费注册
                            </a>
                            @endif
                        @else
                            <a href="{{ route('dashboard') }}" class="btn-hero btn-hero-primary">
                                <i class="bi bi-speedometer2 me-2"></i>进入控制台
                            </a>
                            <a href="{{ route('aws-accounts.index') }}" class="btn-hero btn-hero-secondary">
                                <i class="bi bi-cloud me-2"></i>管理AWS账户
                            </a>
                        @endguest
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能特性区域 -->
<div class="features-section">
    <div class="container">
        <div class="section-title animate-fadeInUp">
            <h2>强大的功能特性</h2>
            <p>基于AWS官方SDK开发，提供完整的云资源管理解决方案</p>
        </div>

        <div class="row">
            <!-- AWS账户管理 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInUp delay-1">
                    <div class="feature-icon">
                        <i class="bi bi-cloud"></i>
                    </div>
                    <h3 class="feature-title">AWS账户管理</h3>
                    <p class="feature-description">
                        全面的AWS账户生命周期管理，支持批量导入、状态监控、配额检测等功能。
                    </p>
                    <ul class="feature-list">
                        <li>批量账户导入与管理</li>
                        <li>实时账户状态检测</li>
                        <li>配额限制监控</li>
                        <li>账户安全验证</li>
                    </ul>
                </div>
            </div>

            <!-- 区域开通 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInUp delay-2">
                    <div class="feature-icon">
                        <i class="bi bi-globe"></i>
                    </div>
                    <h3 class="feature-title">全球区域开通</h3>
                    <p class="feature-description">
                        一键开通AWS全球区域服务，支持批量操作，快速扩展您的云基础设施。
                    </p>
                    <ul class="feature-list">
                        <li>支持所有AWS区域</li>
                        <li>批量区域开通</li>
                        <li>开通状态跟踪</li>
                        <li>自动错误处理</li>
                    </ul>
                </div>
            </div>

            <!-- IAM管理 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInUp delay-3">
                    <div class="feature-icon">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <h3 class="feature-title">IAM用户创建</h3>
                    <p class="feature-description">
                        快速创建和管理IAM用户，自动配置访问权限，简化账户授权流程。
                    </p>
                    <ul class="feature-list">
                        <li>批量IAM用户创建</li>
                        <li>自动权限配置</li>
                        <li>访问密钥管理</li>
                        <li>账单权限激活</li>
                    </ul>
                </div>
            </div>

            <!-- EC2实例管理 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInLeft delay-1">
                    <div class="feature-icon">
                        <i class="bi bi-server"></i>
                    </div>
                    <h3 class="feature-title">EC2实例管理</h3>
                    <p class="feature-description">
                        完整的EC2实例生命周期管理，支持创建、启动、停止、终止等操作。
                    </p>
                    <ul class="feature-list">
                        <li>批量实例创建</li>
                        <li>多账户管理</li>
                        <li>实例状态监控</li>
                        <li>系统镜像选择</li>
                    </ul>
                </div>
            </div>

            <!-- 安全管理 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInLeft delay-2">
                    <div class="feature-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h3 class="feature-title">安全管理</h3>
                    <p class="feature-description">
                        多层次安全防护，确保您的AWS资源和数据安全。
                    </p>
                    <ul class="feature-list">
                        <li>访问权限控制</li>
                        <li>操作日志记录</li>
                        <li>数据加密传输</li>
                        <li>安全审计功能</li>
                    </ul>
                </div>
            </div>

            <!-- 数据统计 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card animate-fadeInLeft delay-3">
                    <div class="feature-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h3 class="feature-title">数据统计分析</h3>
                    <p class="feature-description">
                        详细的数据统计和可视化分析，帮助您更好地了解资源使用情况。
                    </p>
                    <ul class="feature-list">
                        <li>账户状态统计</li>
                        <li>资源使用分析</li>
                        <li>趋势图表展示</li>
                        <li>自定义报表</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计数据区域 -->
<div class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-fadeInUp delay-1">
                    <div class="stat-icon">
                        <i class="bi bi-cloud"></i>
                    </div>
                    <div class="stat-number">5+</div>
                    <div class="stat-label">核心功能模块</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-fadeInUp delay-2">
                    <div class="stat-icon">
                        <i class="bi bi-globe"></i>
                    </div>
                    <div class="stat-number">20+</div>
                    <div class="stat-label">支持AWS区域</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-fadeInUp delay-3">
                    <div class="stat-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="stat-number">100%</div>
                    <div class="stat-label">安全可靠</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-fadeInUp delay-4">
                    <div class="stat-icon">
                        <i class="bi bi-lightning"></i>
                    </div>
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">服务支持</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
