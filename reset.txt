 AWS Root User MFA重置功能尝试记录
=================================

1. AWS SDK直接调用
请求方式：
```php
$client->enableMFADevice([
    'SerialNumber' => $serialNumber,
    'AuthenticationCode1' => $code1,
    'AuthenticationCode2' => $code2
]);
```
错误信息：`[UserName] is missing and is a required parameter`

2. 添加UserName参数
请求方式：
```php
$client->enableMFADevice([
    'UserName' => 'root',
    'SerialNumber' => $serialNumber,
    'AuthenticationCode1' => $code1,
    'AuthenticationCode2' => $code2
]);
```
错误信息：`The user with name root cannot be found`

3. AWS CLI方式
请求方式：
```php
$process = new Process(['aws', 'iam', 'enable-mfa-device', ...]);
```
错误信息：`'aws' 不是内部或外部命令`

4. 直接HTTP API调用
请求方式：
```php
$client->post('https://iam.amazonaws.com/', [
    'headers' => [...],
    'form_params' => [...]
]);
```
错误信息：`400 Bad Request` 和 `404 Not Found`

5. SDK低级API调用
请求方式：
```php
$command = $client->getCommand('EnableMFADevice', [
    'SerialNumber' => $serialNumber,
    'AuthenticationCode1' => $code1,
    'AuthenticationCode2' => $code2
]);
$result = $client->execute($command);
```
错误信息：`Operation not found: GetCallerIdentity`

6. 根用户专用API调用
请求方式：
```php
$client->__call('EnableMFADevice', [
    'EntityType' => 'Root',
    'SerialNumber' => $serialNumber,
    'AuthenticationCode1' => $code1,
    'AuthenticationCode2' => $code2
]);
```
错误信息：`ValidationError: Value at 'userName' failed to satisfy constraint`

7. AWS STS服务和v4签名
请求方式：
```php
$stsClient = new StsClient([...]);
$callerIdentity = $stsClient->getCallerIdentity([]);
$s3Client = new S3Client([...]);
$request = new \Aws\Command([
    'Action' => 'EnableMFADevice',
    'Version' => '2010-05-08'
]);
```
错误信息：`No handler has been specified`

8. IAM原生API和v4签名
请求方式：
```php
$iamClient = new \Aws\Iam\IamClient([
    'version' => 'latest',
    'region'  => 'us-east-1',
    'signature_version' => 'v4',
    ...
]);
$result = $iamClient->enableMFADevice([
    'SerialNumber' => $serialNumber,
    'AuthenticationCode1' => $code1,
    'AuthenticationCode2' => $code2,
    'EntityType' => 'Root',
    '@region' => 'us-east-1'
]);
```
错误信息：`[UserName] is missing and is a required parameter`