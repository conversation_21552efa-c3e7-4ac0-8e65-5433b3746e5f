<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('aws_accounts')) {
            Schema::create('aws_accounts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('account_name');
                $table->string('email_password');
                $table->string('aws_password');
                $table->string('access_key');
                $table->text('secret_key');
                $table->boolean('status')->default(false);
                $table->text('quota')->nullable();
                $table->timestamp('last_check_at')->nullable();
                $table->text('remarks')->nullable();
                $table->timestamps();
            });
        } else {
            Schema::table('aws_accounts', function (Blueprint $table) {
                if (!Schema::hasColumn('aws_accounts', 'account_name')) {
                    $table->string('account_name')->after('user_id');
                }
                if (!Schema::hasColumn('aws_accounts', 'email_password')) {
                    $table->string('email_password')->after('account_name');
                }
                if (!Schema::hasColumn('aws_accounts', 'aws_password')) {
                    $table->string('aws_password')->after('email_password');
                }
                if (!Schema::hasColumn('aws_accounts', 'access_key')) {
                    $table->string('access_key')->after('aws_password');
                }
                if (!Schema::hasColumn('aws_accounts', 'secret_key')) {
                    $table->text('secret_key')->after('access_key');
                }
                if (!Schema::hasColumn('aws_accounts', 'status')) {
                    $table->boolean('status')->default(false)->after('secret_key');
                }
                if (!Schema::hasColumn('aws_accounts', 'quota')) {
                    $table->text('quota')->nullable()->after('status');
                }
                if (!Schema::hasColumn('aws_accounts', 'last_check_at')) {
                    $table->timestamp('last_check_at')->nullable()->after('quota');
                }
                if (!Schema::hasColumn('aws_accounts', 'remarks')) {
                    $table->text('remarks')->nullable()->after('last_check_at');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aws_accounts');
    }
};
