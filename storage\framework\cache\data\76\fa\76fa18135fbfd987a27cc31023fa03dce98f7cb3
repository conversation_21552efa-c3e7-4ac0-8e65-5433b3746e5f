1754210307a:6:{s:13:"totalAccounts";i:28;s:14:"normalAccounts";i:6;s:14:"bannedAccounts";i:9;s:16:"untestedAccounts";i:13;s:11:"growthStats";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:8:{i:0;a:2:{s:4:"date";s:10:"2025-06-01";s:5:"count";i:5;}i:1;a:2:{s:4:"date";s:10:"2025-06-02";s:5:"count";i:7;}i:2;a:2:{s:4:"date";s:10:"2025-06-05";s:5:"count";i:8;}i:3;a:2:{s:4:"date";s:10:"2025-07-02";s:5:"count";i:9;}i:4;a:2:{s:4:"date";s:10:"2025-07-10";s:5:"count";i:19;}i:5;a:2:{s:4:"date";s:10:"2025-07-13";s:5:"count";i:25;}i:6;a:2:{s:4:"date";s:10:"2025-07-18";s:5:"count";i:26;}i:7;a:2:{s:4:"date";s:10:"2025-07-28";s:5:"count";i:28;}}s:28:" * escapeWhenCastingToString";b:0;}s:14:"recentAccounts";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:21:"App\Models\AwsAccount":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"aws_accounts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:24:{s:2:"id";i:68;s:7:"user_id";i:1;s:12:"account_name";s:28:"<EMAIL>";s:14:"email_password";s:9:"k12BSOD3I";s:12:"aws_password";s:8:"ddD1ANwu";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"xml8/Kk6RGrbQYU5hKW7s0Xqa/kGG4P9/iHTTsAP";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";s:19:"2025-07-28 17:13:57";s:7:"remarks";N;s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-28 16:21:04";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";s:64:"JENTG7B3NRSEJXXGZKQSFRRZ6XUWZZW3BEIHWS72TTZHFJ6NWV3F4R6TWU57I5AO";s:12:"iam_username";s:5:"we123";s:12:"iam_password";s:9:"we123AA98";s:14:"iam_access_key";s:20:"********************";s:14:"iam_secret_key";s:40:"GMo4hx3Tm9jcnrSJOU8JzziIF3sBAPlhdwH8or2d";s:10:"iam_status";s:7:"success";s:14:"iam_created_at";s:19:"2025-07-28 17:14:02";s:14:"aws_account_id";s:12:"************";s:10:"account_id";N;}s:11:" * original";a:24:{s:2:"id";i:68;s:7:"user_id";i:1;s:12:"account_name";s:28:"<EMAIL>";s:14:"email_password";s:9:"k12BSOD3I";s:12:"aws_password";s:8:"ddD1ANwu";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"xml8/Kk6RGrbQYU5hKW7s0Xqa/kGG4P9/iHTTsAP";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";s:19:"2025-07-28 17:13:57";s:7:"remarks";N;s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-28 16:21:04";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";s:64:"JENTG7B3NRSEJXXGZKQSFRRZ6XUWZZW3BEIHWS72TTZHFJ6NWV3F4R6TWU57I5AO";s:12:"iam_username";s:5:"we123";s:12:"iam_password";s:9:"we123AA98";s:14:"iam_access_key";s:20:"********************";s:14:"iam_secret_key";s:40:"GMo4hx3Tm9jcnrSJOU8JzziIF3sBAPlhdwH8or2d";s:10:"iam_status";s:7:"success";s:14:"iam_created_at";s:19:"2025-07-28 17:14:02";s:14:"aws_account_id";s:12:"************";s:10:"account_id";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:7:"integer";s:10:"ec2_status";s:7:"integer";s:13:"last_check_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"account_name";i:1;s:14:"email_password";i:2;s:12:"aws_password";i:3;s:10:"access_key";i:4;s:10:"secret_key";i:5;s:11:"aws_mfa_key";i:6;s:5:"quota";i:7;s:14:"aws_account_id";i:8;s:10:"account_id";i:9;s:12:"iam_username";i:10;s:12:"iam_password";i:11;s:14:"iam_access_key";i:12;s:14:"iam_secret_key";i:13;s:10:"iam_status";i:14;s:14:"iam_created_at";i:15;s:7:"remarks";}s:10:" * guarded";a:7:{i:0;s:2:"id";i:1;s:7:"user_id";i:2;s:6:"status";i:3;s:10:"ec2_status";i:4;s:13:"last_check_at";i:5;s:10:"created_at";i:6;s:10:"updated_at";}}i:1;O:21:"App\Models\AwsAccount":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"aws_accounts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:24:{s:2:"id";i:67;s:7:"user_id";i:1;s:12:"account_name";s:20:"{_currentData.Email}";s:14:"email_password";s:15:"{emailPassword}";s:12:"aws_password";s:23:"{_currentData.Password}";s:10:"access_key";s:11:"{accessKey}";s:10:"secret_key";s:17:"{secretAccessKey}";s:6:"status";i:0;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";N;s:7:"remarks";s:18:"账户已被封禁";s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-28 13:18:55";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";s:22:"{_currentData.MfaInfo}";s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";N;s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:11:" * original";a:24:{s:2:"id";i:67;s:7:"user_id";i:1;s:12:"account_name";s:20:"{_currentData.Email}";s:14:"email_password";s:15:"{emailPassword}";s:12:"aws_password";s:23:"{_currentData.Password}";s:10:"access_key";s:11:"{accessKey}";s:10:"secret_key";s:17:"{secretAccessKey}";s:6:"status";i:0;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";N;s:7:"remarks";s:18:"账户已被封禁";s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-28 13:18:55";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";s:22:"{_currentData.MfaInfo}";s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";N;s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:7:"integer";s:10:"ec2_status";s:7:"integer";s:13:"last_check_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"account_name";i:1;s:14:"email_password";i:2;s:12:"aws_password";i:3;s:10:"access_key";i:4;s:10:"secret_key";i:5;s:11:"aws_mfa_key";i:6;s:5:"quota";i:7;s:14:"aws_account_id";i:8;s:10:"account_id";i:9;s:12:"iam_username";i:10;s:12:"iam_password";i:11;s:14:"iam_access_key";i:12;s:14:"iam_secret_key";i:13;s:10:"iam_status";i:14;s:14:"iam_created_at";i:15;s:7:"remarks";}s:10:" * guarded";a:7:{i:0;s:2:"id";i:1;s:7:"user_id";i:2;s:6:"status";i:3;s:10:"ec2_status";i:4;s:13:"last_check_at";i:5;s:10:"created_at";i:6;s:10:"updated_at";}}i:2;O:21:"App\Models\AwsAccount":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"aws_accounts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:24:{s:2:"id";i:66;s:7:"user_id";i:1;s:12:"account_name";s:26:"<EMAIL>";s:14:"email_password";s:11:"mWLrTgE2RIs";s:12:"aws_password";s:8:"ztKCBBc1";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"Y7aUnu4H0R86V3f4C/rpApx22D4csyg4PVFno2B6";s:6:"status";i:0;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";N;s:7:"remarks";s:18:"账户已被封禁";s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-18 16:46:43";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";N;s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";N;s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:11:" * original";a:24:{s:2:"id";i:66;s:7:"user_id";i:1;s:12:"account_name";s:26:"<EMAIL>";s:14:"email_password";s:11:"mWLrTgE2RIs";s:12:"aws_password";s:8:"ztKCBBc1";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"Y7aUnu4H0R86V3f4C/rpApx22D4csyg4PVFno2B6";s:6:"status";i:0;s:10:"ec2_status";i:0;s:5:"quota";N;s:13:"last_check_at";N;s:7:"remarks";s:18:"账户已被封禁";s:13:"enable_result";s:180:"{
    "success": false,
    "responses": {
        "error": "系统错误: 当前免费代理模式IP异常，请切换其他模式进行操作"
    },
    "account_status": null
}";s:10:"created_at";s:19:"2025-07-18 16:46:43";s:10:"updated_at";s:19:"2025-07-30 01:25:06";s:11:"aws_mfa_key";N;s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";N;s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:7:"integer";s:10:"ec2_status";s:7:"integer";s:13:"last_check_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"account_name";i:1;s:14:"email_password";i:2;s:12:"aws_password";i:3;s:10:"access_key";i:4;s:10:"secret_key";i:5;s:11:"aws_mfa_key";i:6;s:5:"quota";i:7;s:14:"aws_account_id";i:8;s:10:"account_id";i:9;s:12:"iam_username";i:10;s:12:"iam_password";i:11;s:14:"iam_access_key";i:12;s:14:"iam_secret_key";i:13;s:10:"iam_status";i:14;s:14:"iam_created_at";i:15;s:7:"remarks";}s:10:" * guarded";a:7:{i:0;s:2:"id";i:1;s:7:"user_id";i:2;s:6:"status";i:3;s:10:"ec2_status";i:4;s:13:"last_check_at";i:5;s:10:"created_at";i:6;s:10:"updated_at";}}i:3;O:21:"App\Models\AwsAccount":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"aws_accounts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:24:{s:2:"id";i:65;s:7:"user_id";i:1;s:12:"account_name";s:26:"<EMAIL>";s:14:"email_password";s:13:"2cfCIFlWpECvc";s:12:"aws_password";s:8:"3tT2f8xV";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"5UarPaEgEMh4bee9ZnAmhCRXi58uwkH8yqPINHL1";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";s:7:"32 x 16";s:13:"last_check_at";s:19:"2025-07-30 02:02:13";s:7:"remarks";N;s:13:"enable_result";s:5479:"{
    "success": true,
    "responses": {
        "describeRegions": {
            "NextToken": "AAMA-EFRSURBSGhsVUxzbjd1N2t6Z2t1ZEdqYVV2d2J6b0VnR2VRTWx5ZnBTeHdyQzVrRmFRRVNkRGVzcU1TTHYwUmRHOGNXbjJIYkFBQUFmakI4QmdrcWhraUc5dzBCQndhZ2J6QnRBZ0VBTUdnR0NTcUdTSWIzRFFFSEFUQWVCZ2xnaGtnQlpRTUVBUzR3RVFRTWV3VUhvYis0WDMvOEU3TlBBZ0VRZ0R0U25JUHRwbEdSdDhkOUhXTnllYW5Sc1oxWUFwQnM5Z1RjKzRiVTJhNFZVbVZmaXJkcTNPM0x1L1VuVjZkeTE4ajBVYlNKUVc3eGpXa1JQQT091_upSG1zigWbvggnAJBTPcZdNh2F0MeePuw-kuunetRsnnIgemiBe5X4aenbmt6zUEFMgfU6ah3-v7eY-VQV4TgIxZml",
            "Regions": [
                {
                    "RegionName": "af-south-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-northeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-3",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-3",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-4",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-5",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-7",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ca-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ca-west-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-central-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-north-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-south-1",
                    "RegionOptStatus": "DISABLED"
                }
            ],
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/listRegions",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:43 GMT",
                    "content-type": "application\/json",
                    "content-length": "1725",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "d865664c-80bf-4ad9-a825-2dc9c5399823",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b6HkTIAMERcw=",
                    "x-amzn-remapped-content-length": "1725",
                    "x-amz-apigw-id": "Oe7b6HkTIAMERcw=",
                    "x-amzn-trace-id": "Root=1-68890c4b-4a1dfedb0272417875837b57",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:43 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        },
        "error": null,
        "enableRegion": {
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/enableRegion",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:44 GMT",
                    "content-type": "application\/json",
                    "content-length": "0",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "********-af48-4dc1-942f-9bb013163df2",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-remapped-content-length": "0",
                    "x-amz-apigw-id": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-trace-id": "Root=1-68890c4c-32e9ba6b106e10e930e8aedc",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:44 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        }
    },
    "account_status": 1
}";s:10:"created_at";s:19:"2025-07-13 14:30:08";s:10:"updated_at";s:19:"2025-07-30 02:02:16";s:11:"aws_mfa_key";N;s:12:"iam_username";s:11:"Awqeqwe1231";s:12:"iam_password";s:10:"Awqeqwe123";s:14:"iam_access_key";s:20:"********************";s:14:"iam_secret_key";s:40:"gijgqQwT1PTGSFUBUBU8AWd4KQO4/zbEXBxc+tRk";s:10:"iam_status";s:7:"success";s:14:"iam_created_at";s:19:"2025-07-30 02:02:16";s:14:"aws_account_id";s:12:"************";s:10:"account_id";N;}s:11:" * original";a:24:{s:2:"id";i:65;s:7:"user_id";i:1;s:12:"account_name";s:26:"<EMAIL>";s:14:"email_password";s:13:"2cfCIFlWpECvc";s:12:"aws_password";s:8:"3tT2f8xV";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"5UarPaEgEMh4bee9ZnAmhCRXi58uwkH8yqPINHL1";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";s:7:"32 x 16";s:13:"last_check_at";s:19:"2025-07-30 02:02:13";s:7:"remarks";N;s:13:"enable_result";s:5479:"{
    "success": true,
    "responses": {
        "describeRegions": {
            "NextToken": "AAMA-EFRSURBSGhsVUxzbjd1N2t6Z2t1ZEdqYVV2d2J6b0VnR2VRTWx5ZnBTeHdyQzVrRmFRRVNkRGVzcU1TTHYwUmRHOGNXbjJIYkFBQUFmakI4QmdrcWhraUc5dzBCQndhZ2J6QnRBZ0VBTUdnR0NTcUdTSWIzRFFFSEFUQWVCZ2xnaGtnQlpRTUVBUzR3RVFRTWV3VUhvYis0WDMvOEU3TlBBZ0VRZ0R0U25JUHRwbEdSdDhkOUhXTnllYW5Sc1oxWUFwQnM5Z1RjKzRiVTJhNFZVbVZmaXJkcTNPM0x1L1VuVjZkeTE4ajBVYlNKUVc3eGpXa1JQQT091_upSG1zigWbvggnAJBTPcZdNh2F0MeePuw-kuunetRsnnIgemiBe5X4aenbmt6zUEFMgfU6ah3-v7eY-VQV4TgIxZml",
            "Regions": [
                {
                    "RegionName": "af-south-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-northeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-3",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-3",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-4",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-5",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-7",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ca-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ca-west-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-central-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-north-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-south-1",
                    "RegionOptStatus": "DISABLED"
                }
            ],
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/listRegions",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:43 GMT",
                    "content-type": "application\/json",
                    "content-length": "1725",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "d865664c-80bf-4ad9-a825-2dc9c5399823",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b6HkTIAMERcw=",
                    "x-amzn-remapped-content-length": "1725",
                    "x-amz-apigw-id": "Oe7b6HkTIAMERcw=",
                    "x-amzn-trace-id": "Root=1-68890c4b-4a1dfedb0272417875837b57",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:43 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        },
        "error": null,
        "enableRegion": {
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/enableRegion",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:44 GMT",
                    "content-type": "application\/json",
                    "content-length": "0",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "********-af48-4dc1-942f-9bb013163df2",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-remapped-content-length": "0",
                    "x-amz-apigw-id": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-trace-id": "Root=1-68890c4c-32e9ba6b106e10e930e8aedc",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:44 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        }
    },
    "account_status": 1
}";s:10:"created_at";s:19:"2025-07-13 14:30:08";s:10:"updated_at";s:19:"2025-07-30 02:02:16";s:11:"aws_mfa_key";N;s:12:"iam_username";s:11:"Awqeqwe1231";s:12:"iam_password";s:10:"Awqeqwe123";s:14:"iam_access_key";s:20:"********************";s:14:"iam_secret_key";s:40:"gijgqQwT1PTGSFUBUBU8AWd4KQO4/zbEXBxc+tRk";s:10:"iam_status";s:7:"success";s:14:"iam_created_at";s:19:"2025-07-30 02:02:16";s:14:"aws_account_id";s:12:"************";s:10:"account_id";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:7:"integer";s:10:"ec2_status";s:7:"integer";s:13:"last_check_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"account_name";i:1;s:14:"email_password";i:2;s:12:"aws_password";i:3;s:10:"access_key";i:4;s:10:"secret_key";i:5;s:11:"aws_mfa_key";i:6;s:5:"quota";i:7;s:14:"aws_account_id";i:8;s:10:"account_id";i:9;s:12:"iam_username";i:10;s:12:"iam_password";i:11;s:14:"iam_access_key";i:12;s:14:"iam_secret_key";i:13;s:10:"iam_status";i:14;s:14:"iam_created_at";i:15;s:7:"remarks";}s:10:" * guarded";a:7:{i:0;s:2:"id";i:1;s:7:"user_id";i:2;s:6:"status";i:3;s:10:"ec2_status";i:4;s:13:"last_check_at";i:5;s:10:"created_at";i:6;s:10:"updated_at";}}i:4;O:21:"App\Models\AwsAccount":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"aws_accounts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:24:{s:2:"id";i:64;s:7:"user_id";i:1;s:12:"account_name";s:25:"<EMAIL>";s:14:"email_password";s:13:"2cfCIFlWpECvc";s:12:"aws_password";s:8:"3tT2f8xV";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"5UarPaEgEMh4bee9ZnAmhCRXi58uwkH8yqPINHL1";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";s:7:"16 x 32";s:13:"last_check_at";s:19:"2025-07-30 02:00:43";s:7:"remarks";N;s:13:"enable_result";s:5454:"{
    "success": true,
    "responses": {
        "describeRegions": {
            "NextToken": "AAMA-EFRSURBSGhsVUxzbjd1N2t6Z2t1ZEdqYVV2d2J6b0VnR2VRTWx5ZnBTeHdyQzVrRmFRRVNkRGVzcU1TTHYwUmRHOGNXbjJIYkFBQUFmakI4QmdrcWhraUc5dzBCQndhZ2J6QnRBZ0VBTUdnR0NTcUdTSWIzRFFFSEFUQWVCZ2xnaGtnQlpRTUVBUzR3RVFRTWV3VUhvYis0WDMvOEU3TlBBZ0VRZ0R0U25JUHRwbEdSdDhkOUhXTnllYW5Sc1oxWUFwQnM5Z1RjKzRiVTJhNFZVbVZmaXJkcTNPM0x1L1VuVjZkeTE4ajBVYlNKUVc3eGpXa1JQQT091_upSG1zigWbvggnAJBTPcZdNh2F0MeePuw-kuunetRsnnIgemiBe5X4aenbmt6zUEFMgfU6ah3-v7eY-VQV4TgIxZml",
            "Regions": [
                {
                    "RegionName": "af-south-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-northeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-3",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-3",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-4",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-5",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-7",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ca-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ca-west-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-central-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-north-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-south-1",
                    "RegionOptStatus": "DISABLED"
                }
            ],
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/listRegions",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:43 GMT",
                    "content-type": "application\/json",
                    "content-length": "1725",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "d865664c-80bf-4ad9-a825-2dc9c5399823",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b6HkTIAMERcw=",
                    "x-amzn-remapped-content-length": "1725",
                    "x-amz-apigw-id": "Oe7b6HkTIAMERcw=",
                    "x-amzn-trace-id": "Root=1-68890c4b-4a1dfedb0272417875837b57",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:43 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        },
        "error": null,
        "enableRegion": {
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/enableRegion",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:44 GMT",
                    "content-type": "application\/json",
                    "content-length": "0",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "********-af48-4dc1-942f-9bb013163df2",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-remapped-content-length": "0",
                    "x-amz-apigw-id": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-trace-id": "Root=1-68890c4c-32e9ba6b106e10e930e8aedc",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:44 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        }
    }
}";s:10:"created_at";s:19:"2025-07-13 12:22:52";s:10:"updated_at";s:19:"2025-07-30 02:00:44";s:11:"aws_mfa_key";N;s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";s:7:"success";s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:11:" * original";a:24:{s:2:"id";i:64;s:7:"user_id";i:1;s:12:"account_name";s:25:"<EMAIL>";s:14:"email_password";s:13:"2cfCIFlWpECvc";s:12:"aws_password";s:8:"3tT2f8xV";s:10:"access_key";s:20:"********************";s:10:"secret_key";s:40:"5UarPaEgEMh4bee9ZnAmhCRXi58uwkH8yqPINHL1";s:6:"status";i:1;s:10:"ec2_status";i:0;s:5:"quota";s:7:"16 x 32";s:13:"last_check_at";s:19:"2025-07-30 02:00:43";s:7:"remarks";N;s:13:"enable_result";s:5454:"{
    "success": true,
    "responses": {
        "describeRegions": {
            "NextToken": "AAMA-EFRSURBSGhsVUxzbjd1N2t6Z2t1ZEdqYVV2d2J6b0VnR2VRTWx5ZnBTeHdyQzVrRmFRRVNkRGVzcU1TTHYwUmRHOGNXbjJIYkFBQUFmakI4QmdrcWhraUc5dzBCQndhZ2J6QnRBZ0VBTUdnR0NTcUdTSWIzRFFFSEFUQWVCZ2xnaGtnQlpRTUVBUzR3RVFRTWV3VUhvYis0WDMvOEU3TlBBZ0VRZ0R0U25JUHRwbEdSdDhkOUhXTnllYW5Sc1oxWUFwQnM5Z1RjKzRiVTJhNFZVbVZmaXJkcTNPM0x1L1VuVjZkeTE4ajBVYlNKUVc3eGpXa1JQQT091_upSG1zigWbvggnAJBTPcZdNh2F0MeePuw-kuunetRsnnIgemiBe5X4aenbmt6zUEFMgfU6ah3-v7eY-VQV4TgIxZml",
            "Regions": [
                {
                    "RegionName": "af-south-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-east-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-northeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-northeast-3",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-south-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-2",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ap-southeast-3",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-4",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-5",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ap-southeast-7",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "ca-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "ca-west-1",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-central-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-central-2",
                    "RegionOptStatus": "DISABLED"
                },
                {
                    "RegionName": "eu-north-1",
                    "RegionOptStatus": "ENABLED_BY_DEFAULT"
                },
                {
                    "RegionName": "eu-south-1",
                    "RegionOptStatus": "DISABLED"
                }
            ],
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/listRegions",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:43 GMT",
                    "content-type": "application\/json",
                    "content-length": "1725",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "d865664c-80bf-4ad9-a825-2dc9c5399823",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b6HkTIAMERcw=",
                    "x-amzn-remapped-content-length": "1725",
                    "x-amz-apigw-id": "Oe7b6HkTIAMERcw=",
                    "x-amzn-trace-id": "Root=1-68890c4b-4a1dfedb0272417875837b57",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:43 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        },
        "error": null,
        "enableRegion": {
            "@metadata": {
                "statusCode": 200,
                "effectiveUri": "https:\/\/account.us-east-1.amazonaws.com\/enableRegion",
                "headers": {
                    "date": "Tue, 29 Jul 2025 18:00:44 GMT",
                    "content-type": "application\/json",
                    "content-length": "0",
                    "connection": "keep-alive",
                    "x-amzn-requestid": "********-af48-4dc1-942f-9bb013163df2",
                    "x-amzn-remapped-x-amzn-requestid": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-remapped-content-length": "0",
                    "x-amz-apigw-id": "Oe7b9Eu6oAMErrQ=",
                    "x-amzn-trace-id": "Root=1-68890c4c-32e9ba6b106e10e930e8aedc",
                    "x-amzn-remapped-date": "Tue, 29 Jul 2025 18:00:44 GMT"
                },
                "transferStats": {
                    "http": [
                        []
                    ]
                }
            }
        }
    }
}";s:10:"created_at";s:19:"2025-07-13 12:22:52";s:10:"updated_at";s:19:"2025-07-30 02:00:44";s:11:"aws_mfa_key";N;s:12:"iam_username";N;s:12:"iam_password";N;s:14:"iam_access_key";N;s:14:"iam_secret_key";N;s:10:"iam_status";s:7:"success";s:14:"iam_created_at";N;s:14:"aws_account_id";N;s:10:"account_id";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:7:"integer";s:10:"ec2_status";s:7:"integer";s:13:"last_check_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:16:{i:0;s:12:"account_name";i:1;s:14:"email_password";i:2;s:12:"aws_password";i:3;s:10:"access_key";i:4;s:10:"secret_key";i:5;s:11:"aws_mfa_key";i:6;s:5:"quota";i:7;s:14:"aws_account_id";i:8;s:10:"account_id";i:9;s:12:"iam_username";i:10;s:12:"iam_password";i:11;s:14:"iam_access_key";i:12;s:14:"iam_secret_key";i:13;s:10:"iam_status";i:14;s:14:"iam_created_at";i:15;s:7:"remarks";}s:10:" * guarded";a:7:{i:0;s:2:"id";i:1;s:7:"user_id";i:2;s:6:"status";i:3;s:10:"ec2_status";i:4;s:13:"last_check_at";i:5;s:10:"created_at";i:6;s:10:"updated_at";}}}s:28:" * escapeWhenCastingToString";b:0;}}