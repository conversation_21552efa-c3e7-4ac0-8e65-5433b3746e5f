@extends('layouts.app')

@section('title', 'AMI数据管理')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        AWS AMI数据管理
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-cloud"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">数据源状态</span>
                                    <span class="info-box-number" id="dataSourceStatus">检查中...</span>
                                    <div class="progress">
                                        <div class="progress-bar" id="dataSourceProgress" style="width: 0%"></div>
                                    </div>
                                    <span class="progress-description" id="dataSourceDesc">正在检查数据源...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">最后更新时间</span>
                                    <span class="info-box-number" id="lastUpdateTime">--</span>
                                    <span class="progress-description" id="nextUpdateTime">下次更新: 每天凌晨5点</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>操作面板</h4>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-primary" id="checkDataBtn">
                                            <i class="fas fa-search"></i>
                                            检查数据状态
                                        </button>
                                        <button type="button" class="btn btn-warning" id="clearCacheBtn">
                                            <i class="fas fa-trash"></i>
                                            清除缓存
                                        </button>
                                        <button type="button" class="btn btn-success" id="refreshDataBtn">
                                            <i class="fas fa-sync"></i>
                                            刷新数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>操作日志</h4>
                                </div>
                                <div class="card-body">
                                    <div id="operationLog" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                                        <div class="log-entry">
                                            <span class="text-muted">[{{ now()->format('Y-m-d H:i:s') }}]</span>
                                            <span class="text-info">系统启动</span>
                                            <span>AMI数据管理界面已加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>数据统计</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="dataStats">
                                        <div class="col-md-3">
                                            <div class="small-box bg-info">
                                                <div class="inner">
                                                    <h3 id="regionCount">--</h3>
                                                    <p>支持地区</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="fas fa-globe"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="small-box bg-success">
                                                <div class="inner">
                                                    <h3 id="osCount">--</h3>
                                                    <p>操作系统</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="fas fa-desktop"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="small-box bg-warning">
                                                <div class="inner">
                                                    <h3 id="amiCount">--</h3>
                                                    <p>AMI总数</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="fas fa-hdd"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="small-box bg-danger">
                                                <div class="inner">
                                                    <h3 id="cacheSize">--</h3>
                                                    <p>缓存大小</p>
                                                </div>
                                                <div class="icon">
                                                    <i class="fas fa-memory"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // 页面加载时检查数据状态
    checkDataStatus();

    // 检查数据状态按钮
    $('#checkDataBtn').click(function() {
        checkDataStatus();
    });

    // 清除缓存按钮
    $('#clearCacheBtn').click(function() {
        if (confirm('确定要清除AMI数据缓存吗？这将强制下次请求重新加载数据。')) {
            clearCache();
        }
    });

    // 刷新数据按钮
    $('#refreshDataBtn').click(function() {
        if (confirm('确定要刷新AMI数据吗？这将清除缓存并重新加载固化数据。')) {
            refreshData();
        }
    });

    // 检查数据状态
    function checkDataStatus() {
        addLog('info', '开始检查数据状态...');
        $('#dataSourceStatus').text('检查中...');
        $('#dataSourceProgress').css('width', '50%');

        $.get('{{ route("aws-ec2.ami-data") }}')
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#dataSourceStatus').text(data.data_source === 'real_api' ? '真实API数据' : '硬编码数据');
                    $('#dataSourceProgress').css('width', '100%');
                    $('#dataSourceDesc').text('数据加载成功');
                    $('#lastUpdateTime').text(new Date(data.last_updated).toLocaleString());

                    // 更新统计信息
                    updateStats(data);
                    addLog('success', '数据状态检查完成');
                } else {
                    $('#dataSourceStatus').text('数据加载失败');
                    $('#dataSourceProgress').css('width', '0%');
                    addLog('error', '数据状态检查失败: ' + response.message);
                }
            })
            .fail(function() {
                $('#dataSourceStatus').text('检查失败');
                $('#dataSourceProgress').css('width', '0%');
                addLog('error', '无法连接到服务器');
            });
    }

    // 清除缓存
    function clearCache() {
        addLog('info', '开始清除缓存...');
        
        $.post('{{ route("aws-ec2.ami-clear-cache") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                addLog('success', '缓存清除成功');
                // 重新检查数据状态
                setTimeout(checkDataStatus, 1000);
            } else {
                addLog('error', '缓存清除失败: ' + response.message);
            }
        })
        .fail(function() {
            addLog('error', '缓存清除请求失败');
        });
    }

    // 刷新数据
    function refreshData() {
        addLog('info', '开始刷新数据...');
        $('#refreshDataBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 刷新中...');

        $.post('{{ route("aws-ec2.ami-refresh-data") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                addLog('success', '数据刷新成功');
                // 2秒后重新检查数据状态
                setTimeout(checkDataStatus, 2000);
            } else {
                addLog('error', '数据刷新失败: ' + response.message);
            }
        })
        .fail(function() {
            addLog('error', '数据刷新请求失败');
        })
        .always(function() {
            $('#refreshDataBtn').prop('disabled', false).html('<i class="fas fa-sync"></i> 刷新数据');
        });
    }

    // 更新统计信息
    function updateStats(data) {
        const regionCount = Object.keys(data.regions || {}).length;
        const osTypes = new Set();
        let amiCount = 0;

        Object.values(data.regions || {}).forEach(region => {
            Object.keys(region.systems || {}).forEach(os => {
                osTypes.add(os);
                const versions = region.systems[os].versions || [];
                amiCount += versions.length;
            });
        });

        $('#regionCount').text(regionCount);
        $('#osCount').text(osTypes.size);
        $('#amiCount').text(amiCount);
        $('#cacheSize').text(Math.round(JSON.stringify(data).length / 1024) + ' KB');
    }

    // 添加日志
    function addLog(type, message) {
        const timestamp = new Date().toLocaleString();
        const typeClass = {
            'info': 'text-info',
            'success': 'text-success',
            'error': 'text-danger',
            'warning': 'text-warning'
        }[type] || 'text-muted';

        const logEntry = `
            <div class="log-entry">
                <span class="text-muted">[${timestamp}]</span>
                <span class="${typeClass}">${type.toUpperCase()}</span>
                <span>${message}</span>
            </div>
        `;

        $('#operationLog').append(logEntry);
        $('#operationLog').scrollTop($('#operationLog')[0].scrollHeight);
    }
});
</script>
@endsection
