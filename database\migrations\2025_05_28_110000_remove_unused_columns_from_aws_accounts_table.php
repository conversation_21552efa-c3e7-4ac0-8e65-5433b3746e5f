<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 这些列已经在创建表时就不存在了，所以不需要删除
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('aws_accounts', function (Blueprint $table) {
            $table->text('enabled_regions')->nullable();
            $table->text('error_message')->nullable();
        });
    }
}; 