# AWS云Panel开发文档

## 项目概述

本项目是一个基于AWS的集成管理操作面板工具，使用PHP+MySQL+Laravel开发，前端采用Minible模板风格（非直接引用），后端通过AWS-SDK-PHP与AWS官方API交互。

## 技术栈
- **后端**: PHP 8.0+, Laravel 9.x
- **前端**: HTML5, CSS3, JavaScript, Bootstrap (Minible模板风格)
- **数据库**: MySQL
- **AWS集成**: AWS-SDK-PHP
- **运行环境**: Laravel运行目录为`public`

## 功能模块详细说明

### 前端功能
1. **未登录状态页面**
   - 自适应介绍页面，展示面板功能
   - 登录和注册入口（按照登录页面.png风格实现）
   - 页面需完全自适应，确保在各种设备上正常显示

2. **注册与登录系统**
   - **注册功能**：
     - 必填字段：会员名、密码、激活码
     - 激活码验证（需后台管理员生成）
     - 表单验证和错误提示
   - **登录功能**：
     - 必填字段：会员名、密码
     - 登录状态维护
     - 安全验证机制

3. **会员控制台**
   - **仪表盘统计**：
     - 已添加账户总数
     - 封禁账户数量
     - 正常账户数量
     - 未检测账户数量
     - 今日添加账户数量
     - 昨日添加账户数量

   - **AWS账户管理模块**：
     - **添加账户功能**：
       - 文本域输入框（支持多行输入）
       - 输入格式示例：
         ```
         ①微软账号：<EMAIL> ②微软密码：62deGrCK0TM6 ③AWS密码：TnjnD30W ④访问密钥：******************** ⑤秘密访问密钥：w00p589WwR4+5zYrrCuD9IG0AaUNO/3tQautJ5CP
         ```
       - 账户列表显示示例：
         | 账户邮箱                  | 邮箱密码      | AWS密码  | 访问密钥                     | 秘密访问密钥                          | 添加时间       | 配额 | 状态 | 最后测号时间 | 操作       |
         |---------------------------|---------------|----------|------------------------------|---------------------------------------|----------------|------|------|--------------|------------|
         | <EMAIL> | 62deGrCK0TM6 | TnjnD30W | ********************         | w00p589WwR4+5zYrrCuD9IG0AaUNO/3tQautJ5CP | 当前添加时间   | 未测 | 未测 | 未测         | 编辑、删除 |
       - 状态颜色规则：
         - 未测：黑色（#000000）
         - 正常：蓝色（#007BFF）
         - 封禁：红色（#DC3545）
       - 其他说明：
         - 添加时间自动记录为当前时间
         - 配额、状态、最后测号时间初始为"未测"
         - 操作栏提供编辑和删除功能
      - 验证逻辑：
         - 检查1-3行中是否至少有1行包含全部5个标识符
         - 不符合则提示"数据不符合"并停止执行
         - 符合则从标识符后提取对应值
       - 数据存储：
         - 账户邮箱：从"①微软账号："后提取
         - 邮箱密码：从"②微软密码："后提取
         - AWS登录密码：从"③AWS密码："后提取
         - 访问密钥：从"④访问密钥："后提取
         - 秘密访问密钥：从"⑤秘密访问密钥："后提取
         - 添加时间：当前时间
         - 配额：留空
         - 状态：留空
         - 最后测号时间：留空
         - 操作：编辑、删除
       - 安全措施：
         - 仅允许会员查看自己的账户
         - 防止越权查看漏洞

   - **AWS一键测号功能**：
     - 账户选择功能：
       - 全选功能
       - 单选功能
       - 多选功能
       - 状态筛选
       - 添加时间筛选
     - 测号流程：
       - 使用AWS-SDK-PHP（参考111/api/index.php）
       - 提取选中账户的访问密钥和秘密访问密钥
       - 请求AWS官方验证密钥有效性
       - 显示进度条指示检测进度
     - 结果显示：
       - 异常账户：
         - 显示无效密钥对应的账户邮箱
         - 状态更新为【封禁】
         - 更新账户表中状态
       - 正常账户：
         - 显示有效密钥对应的账户邮箱
         - 状态更新为【正常】
         - 更新账户表中状态
       - 更新最后测号时间为当前时间
     - 统计显示（正方形显示）：
       - 全部：提交的账户总数量
       - 正常：正常的账户数量
       - 异常：异常封禁的账户数量

   - **AWS区域开通功能**：
     - 账户选择（与一键测号相同的选择功能）
     - 区域选择列表（严格按照111/enable/目录下的代码定义）
     - 开通流程：
       - 选择区域
       - 点击一键开通按钮
       - 提取选中账户的密钥
       - 请求AWS官方API
     - 结果显示：
       - 异常账户：
         - 显示无效账户邮箱
         - 状态更新为【封禁】
         - 更新账户表状态
         - 更新最后测号时间
       - 开通成功账户：
         - 按照enable目录代码逻辑归类
         - 显示对应账户和状态
     - 统计显示（正方形）：
       - 全部：提交的账户总数量
       - 正常：开通成功的账户数量
       - 异常：异常封禁的账户数量

   - **AWS配额检测功能**：
     - 账户选择（与一键测号相同的选择功能）
     - 配额检测流程：
       - 参考111/api/quota.php
       - 提取选中账户的密钥
       - 请求AWS官方API
     - 结果显示：
       - 异常账户：
         - 显示无效账户邮箱
         - 状态更新为【封禁】
         - 更新账户表状态
         - 更新最后测号时间
       - 正常账户：
         - 显示账户邮箱
         - 显示配额数据（支持升序降序排序）
     - 统计显示（正方形）：
       - 全部：提交的账户总数量
       - 正常：正常的账户数量
       - 异常：异常封禁的账户数量

   - **会员资料管理**：
     - 头像显示（默认AWS头像）
     - 会员信息显示
     - 密码修改功能（需验证旧密码）

### 后台管理系统
1. **系统配置**
   - 后台访问地址：通过`.env`文件配置（如`ADMIN_URL=admin`）
   - 数据库分离：管理员表(`admins`)与会员表(`users`)完全独立

2. **管理员仪表盘**
   - **数据统计**：
     - 实时显示全部会员账户数量
     - 已绑定AWS账户总数
     - 今日/昨日绑定账户数量对比
     - 今日测号数量（通过AWS一键测号功能请求官方的验证次数）
     - 今日开通区域数量（通过AWS区域开通功能请求官方的开通次数）
     - 今日配额检测数量（通过AWS配额检测功能请求官方的检测次数）
   - **数据可视化**：使用Minible模板的图表组件展示趋势

3. **管理功能**
   - **会员管理**：
     - 列表显示所有会员（分页）
     - 支持按用户名、注册时间筛选
     - 增删改查操作（禁用直接删除，需二次确认）
   - **AWS账户管理**：
     - 可筛选查看任意会员的AWS账户
     - 显示字段与前端会员视图完全一致（邮箱、密码、密钥等）
     - 支持编辑/封禁操作（禁止删除以防数据丢失）
   - **激活码生成**：
     - 生成规则：
       - 10位大小写数字混合（如`A1b2C3d4E5`）
       - 可指定生成数量和有效期（按天计算）
       - 自动记录生成时间和创建者
     - 列表管理：
       - 显示未使用/已使用状态
       - 支持按有效期筛选

4. **安全措施**
   - 管理员操作日志记录（关键操作需二次验证）
   - 密码策略：强制复杂度+定期更换
   - 防暴力破解：登录失败次数限制

5. **模板使用规范**
   - 从`Minible_CI_v2.3.0`复制所需文件（禁止直接引用）
   - 上线前需彻底删除`Minible_CI_v2.3.0`文件夹
   - 界面风格需与Minible保持一致但避免完全一致

## 数据库设计

### 表结构
1. **会员表** (`users`)
   ```sql
   CREATE TABLE `users` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
     `username` varchar(255) NOT NULL,
     `password` varchar(255) NOT NULL,
     `activation_code` varchar(255) NOT NULL,
     `avatar` varchar(255) DEFAULT 'aws-default.png',
     `created_at` timestamp NULL DEFAULT NULL,
     `updated_at` timestamp NULL DEFAULT NULL,
     PRIMARY KEY (`id`),
     UNIQUE KEY `users_username_unique` (`username`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

2. **AWS账户表** (`aws_accounts`)
   ```sql
   CREATE TABLE `aws_accounts` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
     `user_id` bigint(20) unsigned NOT NULL,
     `email` varchar(255) NOT NULL,
     `microsoft_password` varchar(255) NOT NULL,
     `aws_password` varchar(255) NOT NULL,
     `access_key` varchar(255) NOT NULL,
     `secret_key` varchar(255) NOT NULL,
     `status` enum('normal','banned','') DEFAULT '',
     `quota` text DEFAULT NULL,
     `last_check_time` timestamp NULL DEFAULT NULL,
     `created_at` timestamp NULL DEFAULT NULL,
     PRIMARY KEY (`id`),
     KEY `aws_accounts_user_id_foreign` (`user_id`),
     CONSTRAINT `aws_accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

3. **管理员表** (`admins`)
   ```sql
   CREATE TABLE `admins` (
     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
     `username` varchar(255) NOT NULL,
     `password` varchar(255) NOT NULL,
     `created_at` timestamp NULL DEFAULT NULL,
     PRIMARY KEY (`id`),
     UNIQUE KEY `admins_username_unique` (`username`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

## 安全要求
1. **AWS-SDK-PHP集成要求**：
   - 必须通过真实请求AWS官方API
   - 禁止任何模拟测试
   - SDK安装位置：确保前后端都能调用

2. **安全防护措施**：
   - 实现完整的权限验证系统
   - 防止SQL注入攻击
   - 防止XSS攻击
   - 防止CSRF攻击
   - 实现请求频率限制
   - 数据加密传输

3. **数据安全**：
   - 密码使用bcrypt加密
   - AWS密钥安全存储
   - 敏感数据加密

## 开发规范
1. **目录结构规范**：
   - `111`：参考代码目录（禁止直接引用）
   - `Minible_CI_v2.3.0`：模板代码（仅供参考）
   -  根目录下已有Laravel框架

2. **代码规范**：
   - 遵循PSR-4自动加载规范
   - 遵循Laravel最佳实践
   - 使用统一的代码风格

## 部署要求
1. **环境配置**：
   - 域名：`awspanel.localhost`
   - MySQL配置：
     - 用户名：`panel`
     - 数据库名：`panel`
     - 密码：`panel`

2. **运行目录**：
   - Laravel运行目录：`public`

3. **依赖安装**：
   - AWS-SDK-PHP安装位置：确保全局可访问

## 测试要求
1. **功能测试**：
   - AWS API调用测试
   - 账户管理功能测试
   - 权限验证测试

2. **安全测试**：
   - 漏洞扫描
   - 渗透测试
   - 权限测试

## 维护计划
1. **定期更新**：
   - AWS-SDK-PHP版本更新
   - 安全补丁更新
   - 功能优化更新

2. **监控机制**：
   - 系统日志监控
   - 性能监控
   - 安全监控 