<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加免费代理相关设置
        $freeProxySettings = [
            [
                'key' => 'free_proxy_enabled',
                'value' => '0',
                'description' => '是否启用免费代理功能',
            ],
            [
                'key' => 'free_proxy_type',
                'value' => 'http',
                'description' => '免费代理协议类型',
            ],
            [
                'key' => 'free_proxy_host',
                'value' => '',
                'description' => '免费代理服务器地址',
            ],
            [
                'key' => 'free_proxy_port',
                'value' => '',
                'description' => '免费代理服务器端口',
            ],
            [
                'key' => 'free_proxy_username',
                'value' => '',
                'description' => '免费代理用户名',
            ],
            [
                'key' => 'free_proxy_password',
                'value' => '',
                'description' => '免费代理密码',
            ],
            [
                'key' => 'free_proxy_status',
                'value' => 'inactive',
                'description' => '免费代理状态',
            ],
            [
                'key' => 'free_proxy_last_test',
                'value' => '',
                'description' => '免费代理最后测试时间',
            ],
            [
                'key' => 'free_proxy_error_message',
                'value' => '',
                'description' => '免费代理错误信息',
            ],
        ];

        foreach ($freeProxySettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                [
                    'value' => $setting['value'],
                    'description' => $setting['description']
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除免费代理相关设置
        $keys = [
            'free_proxy_enabled',
            'free_proxy_type',
            'free_proxy_host',
            'free_proxy_port',
            'free_proxy_username',
            'free_proxy_password',
            'free_proxy_status',
            'free_proxy_last_test',
            'free_proxy_error_message',
        ];

        Setting::whereIn('key', $keys)->delete();
    }
};
