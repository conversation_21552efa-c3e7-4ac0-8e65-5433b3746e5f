<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 后台配置
    |--------------------------------------------------------------------------
    |
    | 这里配置后台相关的设置
    |
    */

    // 后台路由前缀
    'url' => env('ADMIN_URL', 'admin'),

    // 后台名称
    'name' => env('ADMIN_NAME', 'AWS账户管理系统'),

    // 后台标题
    'title' => env('ADMIN_TITLE', 'AWS账户管理系统'),

    // 登录页面标题
    'login_title' => env('ADMIN_LOGIN_TITLE', '管理员登录'),

    /*
    |--------------------------------------------------------------------------
    | 每页显示记录数
    |--------------------------------------------------------------------------
    |
    | 这个配置决定了后台列表页面每页显示的记录数
    |
    */
    'per_page' => 10,

    /*
    |--------------------------------------------------------------------------
    | 登录尝试次数限制
    |--------------------------------------------------------------------------
    |
    | 这个配置决定了管理员在被锁定之前可以尝试登录的次数
    |
    */
    'max_attempts' => 5,

    /*
    |--------------------------------------------------------------------------
    | 锁定时间（分钟）
    |--------------------------------------------------------------------------
    |
    | 这个配置决定了当管理员被锁定后，需要等待多长时间才能再次尝试登录
    |
    */
    'lockout_time' => 10,
]; 