<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\ActivationCode;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class RegisterController extends Controller
{
    public function showRegistrationForm()
    {
        // 检查注册功能是否开启
        if (Setting::get('register_enabled') !== '1') {
            return redirect()->route('login')
                ->with('error', '注册功能已关闭');
        }

        return view('auth.register');
    }

    public function register(Request $request)
    {
        // 检查注册功能是否开启
        if (Setting::get('register_enabled') !== '1') {
            return redirect()->route('login')
                ->with('error', '注册功能已关闭');
        }

        $request->validate([
            'username' => ['required', 'string', 'max:255', 'unique:users'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'activation_code' => ['required', 'string'],
        ]);

        $code = ActivationCode::where('code', $request->activation_code)
            ->where('is_used', false)
            ->first();

        if (!$code) {
            return back()->withErrors(['activation_code' => '激活码无效或已使用']);
        }

        // 开始事务
        \DB::beginTransaction();
        
        try {
            $user = User::create([
                'username' => $request->username,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'expires_at' => now()->addDays($code->valid_days), // 设置用户到期时间
            ]);

            // 更新激活码状态
            $code->update([
                'is_used' => true,
                'user_id' => $user->id,
            ]);

            \DB::commit();
            
            auth()->login($user);

            return redirect()->route('dashboard');
        } catch (\Exception $e) {
            \DB::rollback();
            return back()->withErrors(['error' => '注册失败，请重试']);
        }
    }
} 