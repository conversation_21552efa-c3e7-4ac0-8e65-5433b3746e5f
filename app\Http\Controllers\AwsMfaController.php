<?php

namespace App\Http\Controllers;

use App\Models\AwsAccount;
use Illuminate\Http\Request;
use Aws\Iam\IamClient;
use Carbon\Carbon;
use App\Services\AwsService;
use Aws\Sts\StsClient;
use GuzzleHttp\Client;
use Symfony\Component\Process\Process;
use GuzzleHttp\Exception\RequestException;
use Aws\Credentials\Credentials;
use Aws\S3\S3Client;
use Illuminate\Support\Facades\Auth;

class AwsMfaController extends Controller
{
    /**
     * 应用用户代理配置到AWS SDK配置
     */
    private function applyProxyConfig(&$config, $user = null)
    {
        if (!$user) {
            \Log::info("MFA AWS SDK请求模式: 本地模式 (无用户认证)");
            return;
        }

        // 如果用户选择了代理模式，必须检查代理状态
        if ($user->proxy_mode === 'proxy') {
            // 检查代理状态是否异常
            if ($user->proxy_status !== 'active') {
                \Log::error("MFA代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'proxy_status' => $user->proxy_status,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('当前代理模式IP异常，请切换其他模式进行操作');
            }

            // 检查代理配置是否完整
            if (!$user->proxy_host || !$user->proxy_port) {
                \Log::error("MFA代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_host' => $user->proxy_host,
                    'proxy_port' => $user->proxy_port
                ]);

                throw new \Exception('代理配置不完整，请重新配置代理或切换到本地模式');
            }

            // 构建代理URL
            $proxyUrl = $this->buildProxyUrl(
                $user->proxy_type,
                $user->proxy_host,
                $user->proxy_port,
                $user->proxy_username,
                $user->proxy_password
            );

            // 应用代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("MFA AWS SDK请求模式: 代理模式", [
                'user_id' => $user->id,
                'proxy_type' => $user->proxy_type,
                'proxy_host' => $user->proxy_host,
                'proxy_port' => $user->proxy_port,
                'proxy_username' => $user->proxy_username,
                'proxy_status' => $user->proxy_status,
                'actual_proxy_ip' => $user->proxy_host, // 实际使用的代理IP
                'proxy_url_format' => $user->proxy_type . '://' . $user->proxy_host . ':' . $user->proxy_port
            ]);
        } elseif ($user->proxy_mode === 'free_proxy') {
            // 免费代理模式
            $freeProxyStatus = \App\Models\Setting::get('free_proxy_status', 'inactive');

            if ($freeProxyStatus !== 'active') {
                \Log::error("MFA免费代理模式下代理IP异常，阻止AWS请求", [
                    'user_id' => $user->id,
                    'proxy_mode' => $user->proxy_mode,
                    'free_proxy_status' => $freeProxyStatus
                ]);

                throw new \Exception('当前免费代理模式IP异常，请切换其他模式进行操作');
            }

            // 获取免费代理配置
            $freeProxyType = \App\Models\Setting::get('free_proxy_type', 'http');
            $freeProxyHost = \App\Models\Setting::get('free_proxy_host', '');
            $freeProxyPort = \App\Models\Setting::get('free_proxy_port', '');
            $freeProxyUsername = \App\Models\Setting::get('free_proxy_username', '');
            $freeProxyPassword = \App\Models\Setting::get('free_proxy_password', '');

            if (!$freeProxyHost || !$freeProxyPort) {
                \Log::error("MFA免费代理配置不完整，阻止AWS请求", [
                    'user_id' => $user->id
                    // 敏感信息已移除：不记录具体的配置信息
                ]);

                throw new \Exception('免费代理配置不完整，请联系管理员');
            }

            // 构建免费代理URL
            $proxyUrl = $this->buildProxyUrl(
                $freeProxyType,
                $freeProxyHost,
                $freeProxyPort,
                $freeProxyUsername,
                $freeProxyPassword
            );

            // 应用免费代理配置
            $config['http']['proxy'] = $proxyUrl;

            \Log::info("MFA AWS SDK请求模式: 免费代理模式", [
                'user_id' => $user->id,
                'free_proxy_status' => $freeProxyStatus
                // 敏感信息已移除：不记录IP、端口、用户名等
            ]);
        } else {
            \Log::info("MFA AWS SDK请求模式: 本地模式", [
                'user_id' => $user->id,
                'proxy_mode' => $user->proxy_mode,
                'proxy_status' => $user->proxy_status ?? 'inactive'
            ]);
        }
    }

    /**
     * 构建代理URL
     */
    private function buildProxyUrl($type, $host, $port, $username = null, $password = null)
    {
        $proxyUrl = $type . '://';

        if ($username && $password) {
            $proxyUrl .= urlencode($username) . ':' . urlencode($password) . '@';
        }

        $proxyUrl .= $host . ':' . $port;

        return $proxyUrl;
    }

    /**
     * 检查错误是否为代理相关错误
     */
    private function isProxyRelatedError($errorMessage)
    {
        $proxyErrorIndicators = [
            '407', 'Proxy Authentication Required',
            '502', 'Bad Gateway',
            '503', 'Service Unavailable',
            '504', 'Gateway Timeout',
            'CONNECT tunnel failed',
            'Proxy CONNECT aborted',
            'cURL error 7', 'cURL error 28', 'cURL error 35',
            'cURL error 52', 'cURL error 56',
            'Empty reply from server',
            'Connection refused',
            'Operation timed out'
        ];

        foreach ($proxyErrorIndicators as $indicator) {
            if (strpos($errorMessage, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }
    public function index()
    {
        $accounts = AwsAccount::query()
            ->where('user_id', Auth::id())
            ->when(request('search'), function ($query, $search) {
                return $query->where('account_name', 'like', "%{$search}%");
            })
            ->when(request('status') !== null && request('status') !== 'all', function ($query) {
                return $query->where('status', request('status'));
            })
            ->when(request('time_range'), function ($query, $timeRange) {
                if ($timeRange === 'today') {
                    return $query->whereDate('created_at', Carbon::today());
                } elseif ($timeRange === 'yesterday') {
                    return $query->whereDate('created_at', Carbon::yesterday());
                } elseif ($timeRange === 'custom') {
                    $startDate = request('start_date');
                    $endDate = request('end_date');
                    if ($startDate && $endDate) {
                        return $query->whereBetween('created_at', [
                            Carbon::parse($startDate)->startOfDay(),
                            Carbon::parse($endDate)->endOfDay()
                        ]);
                    }
                }
            })
            ->orderByDesc('id')
            ->paginate(request('per_page', 10));

        return view('aws-mfa.index', compact('accounts'));
    }

    public function getCode(Request $request)
    {
        $request->validate([
            'account_id' => 'required|integer|exists:aws_accounts,id'
        ]);
        $account = AwsAccount::find($request->account_id);
        if (!$account || empty($account->aws_mfa_key)) {
            return response()->json(['success' => false, 'message' => '未找到MFA密钥'], 404);
        }
        try {
            $code = $this->generateTOTPCode($account->aws_mfa_key);
            return response()->json(['success' => true, 'code' => $code]);
                } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    // 在enableMFA方法后添加异常处理包装
    private function handleMfaException(\Exception $e)
    {
        // 检查是否是代理异常错误
        if ($e->getMessage() && (strpos($e->getMessage(), '当前免费代理模式IP异常，请切换其他模式进行操作') !== false ||
                               strpos($e->getMessage(), '当前代理模式IP异常，请切换其他模式进行操作') !== false)) {
            return response()->json([
                'success' => false,
                'error_type' => 'proxy_error',
                'message' => $e->getMessage()
            ], 400);
        }

        return response()->json([
            'success' => false,
            'message' => 'MFA操作失败: ' . $e->getMessage()
        ], 500);
    }

    private function generateTOTPCode($base32StringSeed)
    {
            $base32StringSeed = trim($base32StringSeed);
            $totp = \OTPHP\TOTP::create($base32StringSeed);
            $code = $totp->now();
            if (!preg_match('/^\d{6}$/', $code)) {
                throw new \Exception('生成的TOTP码格式无效');
            }
            return $code;
    }

    private function base32_encode($data)
    {
        if (empty($data)) {
            return '';
        }

        // RFC 4648 Base32字母表
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $bits = 0;
        $value = 0;
        $output = '';
        
        foreach (str_split($data) as $char) {
            $value = ($value << 8) | ord($char);
            $bits += 8;
            
            while ($bits >= 5) {
                $output .= $alphabet[($value >> ($bits - 5)) & 31];
                $bits -= 5;
            }
        }
        
        if ($bits > 0) {
            $output .= $alphabet[($value << (5 - $bits)) & 31];
        }
        
        // 添加填充
        while (strlen($output) % 8 !== 0) {
            $output .= '=';
        }
        
        return $output;
    }

    private function base32_decode($base32String)
    {
        if (empty($base32String)) {
            return '';
        }

        $base32String = strtoupper(trim($base32String, '='));
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $value = 0;
        $bits = 0;
        
        foreach (str_split($base32String) as $char) {
            $pos = strpos($alphabet, $char);
            if ($pos === false) {
                continue;
            }
            
            $value = ($value << 5) | $pos;
            $bits += 5;
            
            if ($bits >= 8) {
                $output .= chr(($value >> ($bits - 8)) & 0xFF);
                $bits -= 8;
            }
        }
        
        return $output;
    }

    private function deactivateMFADeviceWithRetry($client, $params, $maxRetries = 3)
    {
        $retryCount = 0;
        $lastException = null;

        while ($retryCount < $maxRetries) {
            try {
                $client->deactivateMFADevice($params);
                return true;
            } catch (\Exception $e) {
                $lastException = $e;
                $retryCount++;
                \Log::warning('停用 MFA 设备失败，尝试重试', [
                    'retry_count' => $retryCount,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);
                if ($retryCount < $maxRetries) {
                    sleep(pow(2, $retryCount)); // 指数退避
                }
            }
        }

        throw $lastException;
    }

    private function getSTSCredentials($account, $mfaCode, $serialNumber)
    {
        try {
            $sdkConfig = [
                'version' => 'latest',
                'region'  => 'us-east-1',
                'credentials' => [
                    'key'    => $account->access_key,
                    'secret' => $account->secret_key,
                ]
            ];

            // 根据配置决定是否禁用SSL验证
            if (config('aws.disable_ssl_verification', false)) {
                $sdkConfig['http']['verify'] = false;
            }

            $stsClient = new StsClient($sdkConfig);

            $result = $stsClient->getSessionToken([
                'SerialNumber' => $serialNumber,
                'TokenCode' => $mfaCode,
                'DurationSeconds' => 3600 // 1小时有效期
            ]);

            \Log::info('成功获取 STS 临时凭证', [
                'account_id' => $account->id,
                'expires' => $result['Credentials']['Expiration'] ?? null,
                'session_duration' => 3600
            ]);

            // 创建新的 IAM 客户端
            $sdkConfig['credentials'] = [
                'key'    => $result['Credentials']['AccessKeyId'],
                'secret' => $result['Credentials']['SecretAccessKey'],
                'token'  => $result['Credentials']['SessionToken']
            ];

            $client = new IamClient($sdkConfig);

            return $client;
        } catch (\Exception $e) {
            \Log::error('获取 STS 临时凭证失败', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('获取临时凭证失败: ' . $e->getMessage());
        }
    }

    private function getUserName($client)
    {
        try {
            // 首先尝试直接获取用户信息
            $result = $client->getUser();
            if (isset($result['User']['UserName'])) {
                return $result['User']['UserName'];
            }
            
            // 如果没有UserName，尝试从ARN中提取
            if (isset($result['User']['Arn'])) {
                if (preg_match('/user\/([^\/]+)$/', $result['User']['Arn'], $matches)) {
                    return $matches[1];
                }
            }
            
            // 尝试列出用户
            $users = $client->listUsers();
            if (!empty($users['Users'])) {
                foreach ($users['Users'] as $user) {
                    // 检查是否是当前用户
                    if (isset($user['Arn']) && strpos($user['Arn'], $result['User']['Arn']) !== false) {
                        return $user['UserName'];
                    }
                }
            }
            
            throw new \Exception('无法确定用户名');
        } catch (\Exception $e) {
            \Log::error('获取用户名失败', ['error' => $e->getMessage()]);
            throw new \Exception('获取用户名失败: ' . $e->getMessage());
        }
    }

    private function handleAwsException(\Aws\Exception\AwsException $e, $accountId, $context = [])
    {
        $errorCode = $e->getAwsErrorCode();
        $errorMessage = $e->getMessage();
        
        $logContext = array_merge([
            'account_id' => $accountId,
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'request_id' => $e->getAwsRequestId(),
            'status_code' => $e->getStatusCode()
        ], $context);

        \Log::error('AWS 操作失败', $logContext);

        // 根据错误代码返回用户友好的错误消息
        $userMessage = match($errorCode) {
            'AccessDenied' => '访问被拒绝，请检查账户权限',
            'InvalidClientTokenId' => '无效的访问密钥，请检查凭证是否正确',
            'SignatureDoesNotMatch' => '签名不匹配，请检查密钥是否正确',
            'ValidationError' => '验证错误：' . $errorMessage,
            'EntityTemporarilyUnmodifiable' => 'MFA 设备当前无法修改，请稍后重试',
            'LimitExceeded' => '已达到 AWS 限制，请稍后重试',
            default => 'AWS 操作失败: ' . $errorMessage
        };

        throw new \Exception($userMessage);
    }

    private function validateAccount($account)
    {
        if (!$account) {
            throw new \Exception('账户不存在');
        }

        if ($account->status != 1) {
            throw new \Exception('账户状态不正常，无法执行操作');
        }

        if (empty($account->access_key) || empty($account->secret_key)) {
            throw new \Exception('账户凭证不完整');
        }

        return true;
    }

    private function getAwsErrorMessage(\Aws\Exception\AwsException $e)
    {
        $errorCode = $e->getAwsErrorCode();
        return match($errorCode) {
            'AccessDenied' => '访问被拒绝，请检查账户权限',
            'InvalidClientTokenId' => '无效的访问密钥，请检查凭证是否正确',
            'SignatureDoesNotMatch' => '签名不匹配，请检查密钥是否正确',
            'ValidationError' => '验证错误：' . $e->getMessage(),
            'EntityTemporarilyUnmodifiable' => 'MFA设备当前无法修改，请稍后重试',
            'LimitExceeded' => '已达到AWS限制，请稍后重试',
            default => 'AWS操作失败: ' . $e->getMessage()
        };
    }

    private function checkAccountType($client)
    {
        try {
            // 尝试获取用户信息
            $result = $client->getUser();
            
            // 检查是否是根用户
            if (isset($result['User']['Arn'])) {
                $arn = $result['User']['Arn'];
                if (strpos($arn, ':root') !== false) {
                    return ['isRootUser' => true, 'userName' => null];
                }
                
                // 如果不是根用户，尝试获取用户名
                if (preg_match('/user\/([^\/]+)$/', $arn, $matches)) {
                    return ['isRootUser' => false, 'userName' => $matches[1]];
                }
            }
            
            // 如果无法从ARN判断，尝试其他方法
            try {
                $users = $client->listUsers();
                if (!empty($users['Users'])) {
                    foreach ($users['Users'] as $user) {
                        if (isset($user['Arn']) && $user['Arn'] === $result['User']['Arn']) {
                            return ['isRootUser' => false, 'userName' => $user['UserName']];
                        }
                    }
                }
            } catch (\Exception $e) {
                \Log::warning('列出用户失败，可能是根用户', [
                    'error' => $e->getMessage()
                ]);
                return ['isRootUser' => true, 'userName' => null];
            }
            
            // 如果还是无法确定，假设是根用户
            return ['isRootUser' => true, 'userName' => null];
        } catch (\Exception $e) {
            \Log::error('检查账户类型失败', ['error' => $e->getMessage()]);
            throw new \Exception('检查账户类型失败: ' . $e->getMessage());
        }
    }
} 