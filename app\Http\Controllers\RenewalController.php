<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ActivationCode;
use Illuminate\Http\Request;
use Carbon\Carbon;

class RenewalController extends Controller
{
    public function index()
    {
        return view('renewal.index');
    }

    public function renew(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'activation_code' => 'required|string'
        ]);

        // 1. 查找用户
        $user = User::where('username', $request->username)->first();
        if (!$user) {
            return back()->withErrors(['username' => '会员账号不存在']);
        }

        // 2. 查找激活码
        $code = ActivationCode::where('code', $request->activation_code)
            ->where('is_used', false)
            ->first();

        if (!$code) {
            return back()->withErrors(['activation_code' => '激活码无效或已被使用']);
        }

        // 3. 处理续费逻辑
        try {
            \DB::beginTransaction();

            $now = Carbon::now();
            $validDays = $code->valid_days;

            // 如果用户已过期或未设置过期时间，从当前时间开始计算
            if (!$user->expires_at || $user->expires_at->lt($now)) {
                $user->expires_at = $now->copy()->addDays($validDays);
            } else {
                // 如果未过期，从当前过期时间开始增加
                $user->expires_at = $user->expires_at->copy()->addDays($validDays);
            }

            // 更新用户过期时间
            $user->save();

            // 标记激活码为已使用
            $code->update([
                'is_used' => true,
                'used_at' => $now,
                'user_id' => $user->id
            ]);

            \DB::commit();

            return back()->with('success', sprintf(
                '续费成功！账号 %s 的会员有效期已延长 %d 天，新的到期时间为：%s',
                $user->username,
                $validDays,
                $user->expires_at->format('Y-m-d H:i:s')
            ));

        } catch (\Exception $e) {
            \DB::rollBack();
            return back()->withErrors(['error' => '续费失败：' . $e->getMessage()]);
        }
    }
} 