<?php

use App\Http\Controllers\Admin\ActivationCodeController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\AwsAccountController;
use App\Http\Controllers\DashboardController as AppDashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\AccountController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\AwsRegionController;
use App\Http\Controllers\AwsIamController;
use App\Http\Controllers\AwsMfaController;
use App\Http\Controllers\AwsEc2Controller;
use App\Http\Controllers\ProxyController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

// 设置欢迎页面为默认首页
Route::get('/', function () {
    return view('welcome');
})->name('home');



Route::middleware('guest')->group(function () {
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login']);
    Route::get('register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('register', [RegisterController::class, 'register']);
});

// 用户相关路由，添加user前缀
Route::middleware('auth')->prefix('user')->group(function () {
    Route::post('logout', [LoginController::class, 'logout'])->name('logout');
    Route::get('dashboard', [AppDashboardController::class, 'index'])->name('dashboard');
    Route::post('dashboard/clear-cache', [AppDashboardController::class, 'clearCache'])->name('dashboard.clear-cache');

    // 个人资料
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/renew', [ProfileController::class, 'renew'])->name('profile.renew');

    // AWS账户管理
    Route::prefix('aws-accounts')->name('aws-accounts.')->group(function () {
        Route::get('/', [AwsAccountController::class, 'index'])->name('index');
        Route::get('/create', [AwsAccountController::class, 'create'])->name('create');
        Route::post('/', [AwsAccountController::class, 'store'])->name('store');
        Route::get('/{awsAccount}/edit', [AwsAccountController::class, 'edit'])->name('edit')->middleware('aws.owner');
        Route::put('/{awsAccount}', [AwsAccountController::class, 'update'])->name('update')->middleware('aws.owner');
        Route::delete('/{awsAccount}', [AwsAccountController::class, 'destroy'])->name('destroy')->middleware('aws.owner');
        Route::post('check', [AwsAccountController::class, 'check'])->name('check');
        Route::post('check-quotas', [AwsAccountController::class, 'checkQuotas'])->name('check-quotas');
        Route::post('enable-region', [AwsAccountController::class, 'enableRegion'])->name('enable-region');
        Route::post('batch-delete', [AwsAccountController::class, 'batchDelete'])->name('batch-delete');
        Route::get('export', [AwsAccountController::class, 'export'])->name('export');
        Route::post('get-by-ids', [AwsAccountController::class, 'getByIds'])->name('get-by-ids');
    });

    // AWS区域开通
    Route::prefix('aws-regions')->name('aws-regions.')->group(function () {
        Route::get('/', [AwsRegionController::class, 'index'])->name('index');
        Route::post('/enable', [AwsRegionController::class, 'enable'])->name('enable');
    });

    // AWS IAM 账户开通
    Route::get('/aws-iam', [AwsIamController::class, 'index'])->name('aws-iam.index');
    Route::post('/aws-iam/create', [AwsIamController::class, 'create'])->name('aws-iam.create');

    // AWS MFA管理
    Route::get('/aws-mfa', [AwsMfaController::class, 'index'])->name('aws-mfa.index');
    Route::post('/aws-mfa/get-code', [AwsMfaController::class, 'getCode'])->name('aws-mfa.getCode');

    // AWS EC2实例管理
    Route::prefix('aws-ec2')->name('aws-ec2.')->group(function () {
        Route::get('/', [AwsEc2Controller::class, 'index'])->name('index');
        Route::get('/create', [AwsEc2Controller::class, 'create'])->name('create');
        Route::get('/batch-create', [AwsEc2Controller::class, 'batchCreate'])->name('batch-create');
        Route::post('/batch-store', [AwsEc2Controller::class, 'batchStore'])->name('batch-store');
        Route::post('/instances', [AwsEc2Controller::class, 'getInstances'])->name('instances');
        Route::post('/create-instance', [AwsEc2Controller::class, 'createInstance'])->name('create-instance');
        Route::post('/start-instances', [AwsEc2Controller::class, 'startInstances'])->name('start-instances');
        Route::post('/stop-instances', [AwsEc2Controller::class, 'stopInstances'])->name('stop-instances');
        Route::post('/reboot-instances', [AwsEc2Controller::class, 'rebootInstances'])->name('reboot-instances');
        Route::post('/terminate-instances', [AwsEc2Controller::class, 'terminateInstances'])->name('terminate-instances');
        Route::post('/create-snapshot', [AwsEc2Controller::class, 'createSnapshot'])->name('create-snapshot');
        Route::post('/manage-tags', [AwsEc2Controller::class, 'manageTags'])->name('manage-tags');
        Route::post('/manage-security-groups', [AwsEc2Controller::class, 'manageSecurityGroups'])->name('manage-security-groups');
        Route::get('/regions', [AwsEc2Controller::class, 'getRegions'])->name('regions');
        Route::get('/amis', [AwsEc2Controller::class, 'getAmis'])->name('amis');
        Route::get('/ami-data', [App\Http\Controllers\AwsAmiController::class, 'getAllAmiData'])->name('ami-data');
        Route::post('/ami-clear-cache', [App\Http\Controllers\AwsAmiController::class, 'clearCache'])->name('ami-clear-cache');
        Route::post('/ami-refresh-data', [App\Http\Controllers\AwsAmiController::class, 'refreshData'])->name('ami-refresh-data');
        Route::get('/ami-management', function() {
            return view('aws-ec2.ami-management');
        })->name('ami-management');


        Route::get('/operating-systems', [AwsEc2Controller::class, 'getSupportedOperatingSystems'])->name('operating-systems');
        Route::get('/instance-types', [AwsEc2Controller::class, 'getInstanceTypes'])->name('instance-types');
        Route::get('/user-data-templates', [AwsEc2Controller::class, 'getUserDataTemplates'])->name('user-data-templates');

        // 这三个接口
        Route::match(['GET', 'POST'], '/key-pairs', [AwsEc2Controller::class, 'getKeyPairs'])->name('key-pairs');
        Route::match(['GET', 'POST'], '/subnets', [AwsEc2Controller::class, 'getSubnets'])->name('subnets');
        Route::match(['GET', 'POST'], '/security-groups', [AwsEc2Controller::class, 'getSecurityGroups'])->name('security-groups');


        Route::post('/batch-create-instances', [AwsEc2Controller::class, 'batchCreateInstances'])->name('batch-create-instances');
        Route::get('/account-stats', [AwsEc2Controller::class, 'getAccountStats'])->name('account-stats');
        Route::post('/smart-select', [AwsEc2Controller::class, 'smartSelectAccounts'])->name('smart-select');
        Route::post('/store-batch-selection', [AwsEc2Controller::class, 'storeBatchSelection'])->name('store-batch-selection');
        Route::post('/clear-batch-selection', [AwsEc2Controller::class, 'clearBatchSelection'])->name('clear-batch-selection');
    });

    // 代理设置路由
    Route::prefix('proxy')->name('proxy.')->group(function () {
        Route::post('/update', [ProxyController::class, 'update'])->name('update');
        Route::post('/test', [ProxyController::class, 'test'])->name('test');
        Route::post('/quick-switch', [ProxyController::class, 'quickSwitch'])->name('quick-switch');
        Route::post('/quick-test', [ProxyController::class, 'quickTest'])->name('quick-test');
    });
});

// 会员续费路由（无需登录即可访问）
Route::get('/renewal', [App\Http\Controllers\RenewalController::class, 'index'])->name('renewal.index');
Route::post('/renewal', [App\Http\Controllers\RenewalController::class, 'renew'])->name('renewal.renew');

// 管理后台路由
Route::prefix(config('admin.url'))->name('admin.')->group(function () {
    // 后台首页 - 根据登录状态重定向
    Route::get('/', function () {
        if (Auth::guard('admin')->check()) {
            return redirect()->route('admin.dashboard');
        }
        return redirect()->route('admin.login');
    });

    // 登录相关路由
    Route::middleware('guest:admin')->group(function () {
        Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AuthController::class, 'login']);
        Route::get('register', [AuthController::class, 'showRegistrationForm'])->name('register');
        Route::post('register', [AuthController::class, 'register']);
    });

    // 需要登录的路由
    Route::middleware('auth:admin')->group(function () {
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
        
        // 个人资料
        Route::get('profile', [\App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('profile');
        Route::patch('profile', [\App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');
        
        // 激活码管理
        Route::resource('activation-codes', ActivationCodeController::class)->except(['edit', 'update', 'show']);
        Route::post('activation-codes/batch-destroy', [ActivationCodeController::class, 'batchDestroy'])->name('activation-codes.batch-destroy');
        Route::post('activation-codes/export', [ActivationCodeController::class, 'export'])->name('activation-codes.export');
        
        // 账户管理路由
        Route::resource('accounts', AccountController::class);
        Route::post('accounts/batch-destroy', [AccountController::class, 'batchDestroy'])->name('accounts.batch-destroy');
        Route::get('accounts/get-password/{id}', [AccountController::class, 'getPassword'])->name('accounts.get-password');
        
        // 会员管理路由
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        
        // 系统设置
        Route::get('settings', [App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
        Route::post('settings', [App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');
        Route::post('settings/test-free-proxy', [App\Http\Controllers\Admin\SettingController::class, 'testFreeProxy'])->name('settings.test-free-proxy');
    });
});

// 登出后重定向到首页
// Route::post('/logout', 'Auth\LoginController@logout')->name('logout');
