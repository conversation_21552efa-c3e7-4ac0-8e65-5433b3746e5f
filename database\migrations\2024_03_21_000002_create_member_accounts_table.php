<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('member_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('member_id')->constrained()->onDelete('cascade');
            $table->string('account_name');
            $table->string('account_number');
            $table->string('account_password');
            $table->text('remarks')->nullable();
            $table->tinyInteger('status')->default(1)->comment('1:正常 0:禁用');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('member_accounts');
    }
}; 