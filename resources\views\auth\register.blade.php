@extends('layouts.guest')

@section('content')
<h1>{{ \App\Models\Setting::get('login_title', config('app.name')) }}</h1>
<p>会员注册</p>

<form method="POST" action="{{ route('register') }}" class="space-y-4" id="registerForm">
    @csrf
    
    <div class="mb-3">
        <label for="username" class="form-label">用户名</label>
        <input id="username" type="text" name="username" value="{{ old('username') }}" 
            class="form-control @error('username') is-invalid @enderror" required autofocus>
        @error('username')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="email" class="form-label">邮箱</label>
        <input id="email" type="email" name="email" value="{{ old('email') }}" 
            class="form-control @error('email') is-invalid @enderror" required>
        @error('email')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="password" class="form-label">密码</label>
        <input id="password" type="password" name="password" 
            class="form-control @error('password') is-invalid @enderror" required>
        @error('password')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="password_confirmation" class="form-label">确认密码</label>
        <input id="password_confirmation" type="password" name="password_confirmation" 
            class="form-control" required>
    </div>

    <div class="mb-3">
        <label for="activation_code" class="form-label">激活码</label>
        <input id="activation_code" type="text" name="activation_code" value="{{ old('activation_code') }}" 
            class="form-control @error('activation_code') is-invalid @enderror" required>
        @error('activation_code')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <button type="submit" class="btn btn-primary w-100">
        注册
    </button>

    <div class="text-center mt-3">
        <span class="text-muted">已有账号？</span>
        <a href="{{ route('login') }}" class="text-decoration-none">立即登录</a>
    </div>
</form>

@push('scripts')
<script>
$(document).ready(function() {
    // 表单提交处理
    $('#registerForm').on('submit', function() {
        // 如果表单验证失败，只清空密码字段
        if (!this.checkValidity()) {
            $('#password, #password_confirmation').val('');
            return false;
        }
    });

    // 密码强度检查
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = checkPasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });

    function checkPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        return strength;
    }

    function updatePasswordStrengthIndicator(strength) {
        const strengthText = ['很弱', '弱', '一般', '强', '很强'];
        const strengthClass = ['danger', 'warning', 'info', 'primary', 'success'];
        const index = strength - 1;
        
        if (strength > 0) {
            $('#password').removeClass().addClass(`form-control border-${strengthClass[index]}`);
            $('#password').siblings('.invalid-feedback').text(`密码强度: ${strengthText[index]}`).show();
        } else {
            $('#password').removeClass().addClass('form-control');
            $('#password').siblings('.invalid-feedback').hide();
        }
    }
});
</script>
@endpush
@endsection 