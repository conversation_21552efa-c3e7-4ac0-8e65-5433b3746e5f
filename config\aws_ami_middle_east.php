<?php

/**
 * AWS AMI数据配置 - 中东地区
 * 包含中东各国地区
 */

return [
    'me-south-1' => [
        'name' => '中东（巴林）',
        'code' => 'me-south-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0e456789f0123456', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'me-central-1' => [
        'name' => '中东（阿联酋）',
        'code' => 'me-central-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-0f567890123456789', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ],
    'il-central-1' => [
        'name' => '以色列（特拉维夫）',
        'code' => 'il-central-1',
        'systems' => [
            'Amazon Linux' => [
                'name' => 'Amazon Linux',
                'versions' => [
                    ['name' => 'Amazon Linux 2023', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Amazon Linux 2023'],
                    ['name' => 'Amazon Linux 2', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Amazon Linux 2']
                ]
            ],
            'Ubuntu' => [
                'name' => 'Ubuntu',
                'versions' => [
                    ['name' => 'Ubuntu 24.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 24.04 LTS'],
                    ['name' => 'Ubuntu 22.04 LTS', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Ubuntu 22.04 LTS'],
                    ['name' => 'Ubuntu 20.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 20.04 LTS'],
                    ['name' => 'Ubuntu 18.04 LTS', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Ubuntu 18.04 LTS']
                ]
            ],
            'Windows Server' => [
                'name' => 'Windows Server',
                'versions' => [
                    ['name' => 'Windows Server 2025', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2025'],
                    ['name' => 'Windows Server 2022', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2022'],
                    ['name' => 'Windows Server 2019', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2019'],
                    ['name' => 'Windows Server 2016', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Windows Server 2016']
                ]
            ],
            'Red Hat' => [
                'name' => 'Red Hat',
                'versions' => [
                    ['name' => 'RHEL 9.4', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'RHEL 9.4'],
                    ['name' => 'RHEL 8.10', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 8.10'],
                    ['name' => 'RHEL 7.9', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'RHEL 7.9']
                ]
            ],
            'SUSE Linux' => [
                'name' => 'SUSE Linux',
                'versions' => [
                    ['name' => 'SUSE Linux Enterprise Server 15 SP6', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'SUSE Linux Enterprise Server 15 SP6'],
                    ['name' => 'SUSE Linux Enterprise Server 15 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 15 SP5'],
                    ['name' => 'SUSE Linux Enterprise Server 12 SP5', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'SUSE Linux Enterprise Server 12 SP5']
                ]
            ],
            'Debian' => [
                'name' => 'Debian',
                'versions' => [
                    ['name' => 'Debian 12', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Debian 12'],
                    ['name' => 'Debian 11', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Debian 11']
                ]
            ],
            'CentOS' => [
                'name' => 'CentOS',
                'versions' => [
                    ['name' => 'CentOS Stream 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'CentOS Stream 9'],
                    ['name' => 'CentOS Stream 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'CentOS Stream 8']
                ]
            ],
            'Oracle Linux' => [
                'name' => 'Oracle Linux',
                'versions' => [
                    ['name' => 'Oracle Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Oracle Linux 9'],
                    ['name' => 'Oracle Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Oracle Linux 8']
                ]
            ],
            'Rocky Linux' => [
                'name' => 'Rocky Linux',
                'versions' => [
                    ['name' => 'Rocky Linux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'Rocky Linux 9'],
                    ['name' => 'Rocky Linux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'Rocky Linux 8']
                ]
            ],
            'AlmaLinux' => [
                'name' => 'AlmaLinux',
                'versions' => [
                    ['name' => 'AlmaLinux 9', 'ami_id' => 'ami-0c02fb55956c7d317', 'display_name' => 'AlmaLinux 9'],
                    ['name' => 'AlmaLinux 8', 'ami_id' => 'ami-080e1f13689e07408', 'display_name' => 'AlmaLinux 8']
                ]
            ]
        ]
    ]
];
