@extends('layouts.app')

@push('styles')
<style>
/* 现代化卡片样式 */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,.05);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0 30px rgba(0,0,0,.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 1.5rem;
}

/* 现代化按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid rgba(67, 97, 238, 0.2);
}

.btn-soft-primary:hover,
.btn-soft-primary:focus,
.btn-soft-primary:active {
    background-color: #4361ee;
    color: #fff;
    border-color: #4361ee;
    box-shadow: none;
}

.btn-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    border: 1px solid rgba(46, 212, 122, 0.2);
}

.btn-soft-success:hover,
.btn-soft-success:focus,
.btn-soft-success:active {
    background-color: #2ed47a;
    color: #fff;
    border-color: #2ed47a;
    box-shadow: none;
}

.btn-soft-info {
    color: #37b9f1;
    background-color: rgba(55, 185, 241, 0.1);
    border: 1px solid rgba(55, 185, 241, 0.2);
}

.btn-soft-info:hover,
.btn-soft-info:focus,
.btn-soft-info:active {
    background-color: #37b9f1;
    color: #fff;
    border-color: #37b9f1;
    box-shadow: none;
}

.btn-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    border: 1px solid rgba(242, 87, 103, 0.2);
}

.btn-soft-danger:hover,
.btn-soft-danger:focus,
.btn-soft-danger:active {
    background-color: #f25767;
    color: #fff;
    border-color: #f25767;
    box-shadow: none;
}

/* 表格样式优化 */
.table {
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

.table > :not(caption) > * > * {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
    text-align: center;
}

.table > thead {
    background-color: #f8f9fa;
}

.table > thead th {
    font-weight: 600;
    color: #495057;
    border-bottom: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* 所有列居中对齐 */
.table th, .table td {
    text-align: center;
}

/* 表单控件样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* 徽章样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 6px;
    font-size: 0.9rem;
}

.badge-soft-success {
    color: #2ed47a;
    background-color: rgba(46, 212, 122, 0.1);
    font-size: 0.9rem;
}

.badge-soft-danger {
    color: #f25767;
    background-color: rgba(242, 87, 103, 0.1);
    font-size: 0.9rem;
}

.badge-soft-warning {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    font-size: 0.9rem;
}

/* 加载遮罩层样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: #fff;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0,0,0,.1);
    text-align: center;
    min-width: 300px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(67, 97, 238, 0.1);
    margin: 1rem auto;
    max-width: 200px;
}

.progress-bar {
    background-color: #4361ee;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

@media (max-width: 768px) {
    .loading-content {
        margin: 1rem;
        min-width: auto;
        width: calc(100% - 2rem);
    }
}

/* 添加截断文本的样式 */
.truncate-text {
    display: inline-block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
}

.email-truncate {
    max-width: 250px;
}

.password-truncate {
    max-width: 100px;
}

.key-truncate {
    max-width: 120px;
}

.secret-truncate {
    max-width: 180px;
}
</style>
@endpush

@section('content')
<div class="container-fluid fade-in">
    @if(session('import_result'))
        <div class="mb-4">
            <x-aws-import-result
                :totalLines="session('import_result.total_lines')"
                :successCount="session('import_result.success_count')"
                :failedCount="session('import_result.failed_count')"
                :failedAccounts="session('import_result.failed_accounts')"
            />
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">AWS账户管理</h4>
                            <p class="text-muted mb-0 mt-1">管理您的所有AWS账户</p>
                        </div>

                        <!-- 代理状态栏 - 居中 -->
                        <div class="flex-grow-1 d-flex justify-content-center mx-4">
                            @include('components.proxy-status-bar')
                        </div>

                        <a href="{{ route('aws-accounts.create') }}" class="btn btn-soft-primary">
                            <i class="bi bi-plus-lg me-1"></i>添加账户
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 使用筛选组件 -->
                    <x-filter-panel 
                        :status-options="App\Models\AwsAccount::getStatusList()" 
                        :show-per-page="false" 
                    />

                    <div class="table-responsive">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="btn-group">
                                <button class="btn btn-soft-success" id="checkSelected">
                                    <i class="bi bi-check2-circle me-1"></i>一键测号
                            </button>
                                <button class="btn btn-soft-info" id="checkQuotasSelected">
                                    <i class="bi bi-speedometer2 me-1"></i>检测配额
                            </button>
                                <button class="btn btn-soft-danger" id="deleteSelected">
                                    <i class="bi bi-trash me-1"></i>删除选中
                            </button>
                                <button class="btn btn-soft-primary" id="exportSelected">
                                    <i class="bi bi-download me-1"></i>导出选中
                            </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <label class="me-2 mb-0">每页显示：</label>
                                <select class="form-select form-select-sm" style="width: auto" onchange="window.location.href='{{ route('aws-accounts.index') }}?per_page=' + this.value">
                                    @foreach([10, 20, 50, 100] as $size)
                                        <option value="{{ $size }}" {{ $perPage == $size ? 'selected' : '' }}>{{ $size }}条</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </div>
                                    </th>
                                    <th>账户邮箱</th>
                                    <th>邮箱密码</th>
                                    <th>AWS密码</th>
                                    <th>访问密钥</th>
                                    <th>秘密访问密钥</th>
                                    <th>状态</th>
                                    <th>最后操作时间</th>
                                    <th>配额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($accounts as $account)
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input account-select" value="{{ $account->id }}">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="truncate-text email-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->account_name }}">
                                            {{ $account->account_name }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text password-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->email_password }}">
                                            {{ $account->email_password }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text password-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->aws_password }}">
                                            {{ $account->aws_password }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text key-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->access_key }}">
                                            {{ $account->access_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="truncate-text secret-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->secret_key }}">
                                            {{ $account->secret_key }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $account->status === 1 ? 'badge-soft-success' : ($account->status === 2 ? 'badge-soft-danger' : 'badge-soft-warning') }}">
                                            {{ $account->status_text }}
                                        </span>
                                    </td>
                                    <td>{{ $account->last_check_at ? $account->last_check_at->format('Y-m-d H:i:s') : '未测试' }}</td>
                                    <td>
                                        <span class="truncate-text key-truncate copy-cell" style="cursor:pointer;user-select:all;" title="{{ $account->quota }}">
                                            {{ $account->quota ?? '未测试' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('aws-accounts.edit', $account) }}" class="btn btn-soft-primary btn-sm">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-soft-danger btn-sm delete-account" data-id="{{ $account->id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="mt-4 d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                总共 {{ $accounts->total() }} 条记录
                            </div>
                            <div>
                                {{ $accounts->withQueryString()->links('components.pagination') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <p class="mt-3">确定要删除这个账户吗？此操作无法撤销。</p>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确定删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载进度条 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content text-center">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 class="mb-3" id="loadingText">正在处理中...</h5>
        <div class="progress mb-2">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <p class="mb-0" id="progressText">0%</p>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选功能
    document.getElementById('selectAll').addEventListener('change', function() {
        document.querySelectorAll('.account-select').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // 获取选中的ID
    function getSelectedIds() {
        return Array.from(document.querySelectorAll('.account-select:checked')).map(cb => cb.value);
    }

    // 一键测号功能
    document.getElementById('checkSelected').addEventListener('click', function() {
        const ids = getSelectedIds();
        if (ids.length === 0) {
            alert('请选择要测试的账户');
            return;
        }

        if (!confirm(`确定要测试选中的 ${ids.length} 个账户吗？`)) {
            return;
        }

        // 显示加载动画
        document.getElementById('loadingOverlay').style.display = 'flex';
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        let progress = 1;
        let lastUpdate = Date.now();

        // 模拟进度
        const progressInterval = setInterval(() => {
            const now = Date.now();
            if (progress < 90 && (now - lastUpdate) >= 2000) {
                progress += Math.floor(Math.random() * 15) + 5;
                if (progress > 89) progress = 89;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '%';
                lastUpdate = now;
            }
        }, 100);

        // 发送请求
        fetch('{{ route("aws-accounts.check") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            progressText.textContent = '100%';
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';

                // 检查是否是代理异常错误
                if (data.error_type === 'proxy_error') {
                    // 显示代理异常提示
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', data.message);
                    } else {
                        alert(data.message);
                    }
                    return; // 不刷新页面，让用户切换代理模式
                }

                alert(data.message);
                location.reload();
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            document.getElementById('loadingOverlay').style.display = 'none';

            // 检查是否是代理异常错误（通过响应状态码判断）
            if (xhr && xhr.responseJSON && xhr.responseJSON.error_type === 'proxy_error') {
                // 显示代理异常提示
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', xhr.responseJSON.message);
                } else {
                    alert(xhr.responseJSON.message);
                }
                return; // 不刷新页面，让用户切换代理模式
            }

            alert('操作失败：' + error.message);
        });
    });

    // 检测配额功能
    document.getElementById('checkQuotasSelected').addEventListener('click', function() {
        const ids = getSelectedIds();
        if (ids.length === 0) {
            alert('请选择要检测配额的账户');
            return;
        }

        if (!confirm(`确定要检测选中的 ${ids.length} 个账户的配额吗？`)) {
            return;
        }

        // 显示加载动画
        document.getElementById('loadingOverlay').style.display = 'flex';
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        let progress = 1;
        let lastUpdate = Date.now();

        // 模拟进度
        const progressInterval = setInterval(() => {
            const now = Date.now();
            if (progress < 90 && (now - lastUpdate) >= 2000) {
                progress += Math.floor(Math.random() * 15) + 5;
                if (progress > 89) progress = 89;
                progressBar.style.width = progress + '%';
                progressText.textContent = progress + '%';
                lastUpdate = now;
            }
        }, 100);

        // 发送请求
        fetch('{{ route("aws-accounts.check-quotas") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(text || '网络请求失败');
                });
            }
            return response.json();
        })
        .then(data => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            progressText.textContent = '100%';
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';

                // 检查是否是代理异常错误
                if (data.error_type === 'proxy_error') {
                    // 显示代理异常提示
                    if (typeof showProxyToast === 'function') {
                        showProxyToast('error', data.message);
                    } else {
                        alert(data.message);
                    }
                    return; // 不刷新页面，让用户切换代理模式
                }

                alert(data.message);
                location.reload();
            }, 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            document.getElementById('loadingOverlay').style.display = 'none';

            // 检查是否是代理异常错误（通过响应状态码判断）
            if (xhr && xhr.responseJSON && xhr.responseJSON.error_type === 'proxy_error') {
                // 显示代理异常提示
                if (typeof showProxyToast === 'function') {
                    showProxyToast('error', xhr.responseJSON.message);
                } else {
                    alert(xhr.responseJSON.message);
                }
                return; // 不刷新页面，让用户切换代理模式
            }

            alert('操作失败：' + error.message);
        });
    });

    // 删除选中功能
    document.getElementById('deleteSelected').addEventListener('click', function() {
        const ids = getSelectedIds();
        if (ids.length === 0) {
            alert('请选择要删除的账户');
            return;
        }

        if (!confirm(`确定要删除选中的 ${ids.length} 个账户吗？此操作无法撤销。`)) {
            return;
        }

        // 发送请求
        fetch('{{ route("aws-accounts.batch-delete") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ ids: ids.join(',') })
        })
        .then(response => response.json())
        .then(data => {
            alert('删除成功！');
            location.reload();
        })
        .catch(error => {
            alert('删除失败：' + error.message);
        });
    });

    // 导出选中功能
    document.getElementById('exportSelected').addEventListener('click', function() {
        const ids = getSelectedIds();
        if (ids.length === 0) {
            alert('请选择要导出的账户');
            return;
        }

        window.location.href = '{{ route("aws-accounts.export") }}?ids=' + ids.join(',');
    });

    let accountIdToDelete = null;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));

    // 删除按钮点击事件
    document.querySelectorAll('.delete-account').forEach(button => {
        button.addEventListener('click', function() {
            accountIdToDelete = this.dataset.id;
            deleteModal.show();
        });
    });

    // 确认删除按钮点击事件
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (!accountIdToDelete) return;

        // 创建一个临时表单来提交删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url('user/aws-accounts') }}/${accountIdToDelete}`;
        
        // 添加 CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').content;
        form.appendChild(csrfToken);
        
        // 添加 _method 字段来模拟 DELETE 请求
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);
        
        // 添加表单到文档并提交
        document.body.appendChild(form);
        deleteModal.hide();
        form.submit();
    });
});

// 复制提示浮窗
function showCopyToast(text) {
    let toast = document.createElement('div');
    toast.textContent = text;
    toast.style.position = 'fixed';
    toast.style.zIndex = 999999;
    toast.style.background = '#222';
    toast.style.color = '#fff';
    toast.style.padding = '10px 30px';
    toast.style.borderRadius = '8px';
    toast.style.fontSize = '20px';
    toast.style.left = '50vw';
    toast.style.top = '30vh';
    toast.style.opacity = 1;
    toast.style.pointerEvents = 'none';
    document.body.appendChild(toast);
    setTimeout(() => { toast.style.opacity = 0; }, 1800);
    setTimeout(() => { toast.remove(); }, 2200);
}
document.querySelector('tbody').addEventListener('click', function(e) {
    if (e.target.classList.contains('copy-cell')) {
                    const code = e.target.textContent.trim();
        navigator.clipboard.writeText(code).then(function() {
            showCopyToast('复制成功');
        });
    }
});
</script>
@endpush