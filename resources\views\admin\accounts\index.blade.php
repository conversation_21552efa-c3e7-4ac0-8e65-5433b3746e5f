@extends('admin.layouts.app')

@push('styles')
<style>
/* 页面容器样式 */
.content-wrapper {
    padding: 1.5rem;
    background-color: #f8f9fa;
    min-height: calc(100vh - 170px);
}

/* 页面头部样式 */
.page-header {
    background: #fff;
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
}

.page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 导航样式 */
.breadcrumb {
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
    background: transparent;
    list-style: none;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: color 0.15s ease-in-out;
}

.breadcrumb-item a:hover {
    color: #5156be;
}

.breadcrumb-item.active {
    color: #5156be;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    color: #6c757d;
    margin: 0 0.5rem;
}

/* 搜索工具栏样式 */
.table-tools {
    margin-bottom: 1rem;
}

.search-box {
    position: relative;
    flex: 4;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 4;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.form-select {
    min-width: 100px;
    max-width: 200px;
}

/* 表格样式保持不变 */
.table th {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}
.table td {
    color: #495057;
    vertical-align: middle;
}
.table-centered {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}
.table-centered tr {
    background-color: #fff;
    transition: all 0.3s ease;
}
.table-centered tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,.08);
}
.table-centered td, .table-centered th {
    border: none;
    padding: 1rem;
}
.table-centered td:first-child, .table-centered th:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}
.table-centered td:last-child, .table-centered th:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    letter-spacing: 0.3px;
}
.badge-soft-success {
    color: #fff;
    background-color: #0ab39c;
    border: 1px solid #0ab39c;
}
.badge-soft-danger {
    color: #f06548;
    background-color: rgba(240, 101, 72, 0.18);
    font-weight: 500;
}
.badge-soft-warning {
    color: #fff;
    background-color: #f7b84b;
    border: 1px solid #f7b84b;
}
.badge-soft-secondary {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.18);
    border: 1px solid rgba(108, 117, 125, 0.3);
    font-weight: 500;
}
.btn-soft-primary {
    color: #5156be;
    background-color: rgba(81,86,190,.1);
    border-color: transparent;
}
.btn-soft-primary:hover {
    color: #fff;
    background-color: #5156be;
}
.btn-soft-danger {
    color: #f06548;
    background-color: rgba(240,101,72,.1);
    border-color: transparent;
}
.btn-soft-danger:hover {
    color: #fff;
    background-color: #f06548;
}
.pagination {
    margin-bottom: 0;
}
.page-link {
    color: #5156be;
    border-radius: 0.25rem;
    margin: 0 0.2rem;
    border: none;
    min-width: 32px;
    text-align: center;
    transition: all 0.3s ease;
}
.page-link:hover {
    color: #4347a5;
    background-color: rgba(81,86,190,.1);
}
.page-item.active .page-link {
    background-color: #5156be;
    border-color: #5156be;
}
.empty-state {
    text-align: center;
    padding: 2rem;
}
.empty-state i {
    font-size: 3rem;
    color: #74788d;
    margin-bottom: 1rem;
}
.empty-state p {
    color: #74788d;
    margin-bottom: 0;
}
.checkbox-column {
    width: 40px;
}
.table-centered td.checkbox-column, 
.table-centered th.checkbox-column {
    padding: 1rem 0.5rem;
}
.text-truncate-cell {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
}
.text-truncate-cell:hover {
    overflow: visible;
    white-space: normal;
    background-color: #fff;
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    border-radius: 4px;
    padding: 0.5rem;
}
.password-cell {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
}
.password-cell:hover {
    overflow: visible;
    white-space: normal;
    background-color: #fff;
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    border-radius: 4px;
    padding: 0.5rem;
}
</style>
@endpush

@section('content')
<!-- 页面标题和导航 -->
<div class="page-header-modern">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title-modern mb-2">AWS账户管理</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="text-secondary">
                            <i class="bi bi-house-door me-1"></i>首页
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="bi bi-cloud me-1"></i>AWS账户管理
                    </li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.accounts.create') }}" class="btn btn-soft-primary">
                <i class="bi bi-plus-lg me-2"></i>添加AWS账户
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <!-- 搜索和筛选工具栏 -->
        <div class="table-tools d-flex flex-wrap gap-2 mb-3">
            <div class="d-flex gap-2 flex-grow-1">
                <div class="search-box flex-grow-1">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索账户..." value="{{ request('search') }}">
                </div>
                <select class="form-select" id="userFilter">
                    <option value="">全部会员</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                            {{ $user->username }}
                        </option>
                    @endforeach
                </select>
                <select class="form-select" id="statusFilter" name="status" data-testid="status-filter">
                    <option value="">全部状态</option>
                    @foreach(\App\Models\AwsAccount::getStatusList() as $key => $value)
                        <option value="{{ $key }}" {{ request('status') !== null && (int)request('status') === $key ? 'selected' : '' }}>
                            {{ $value }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="d-flex align-items-center">
                <select class="form-select form-select-sm" style="width: auto" name="per_page">
                    @foreach([10, 20, 50, 100] as $size)
                        <option value="{{ $size }}" {{ $perPage == $size ? 'selected' : '' }}>
                            {{ $size }}条/页
                        </option>
                    @endforeach
                </select>
            </div>
        </div>

        @if($accounts->isEmpty())
            <div class="empty-state">
                <i class="bi bi-inbox"></i>
                <p>暂无账户数据</p>
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-centered">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>
                                <a href="{{ route('admin.accounts.index', array_merge(request()->query(), ['sort' => 'id', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc'])) }}" class="text-decoration-none text-dark">
                                    #
                                    @if(request('sort') === 'id')
                                        <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th>所属会员</th>
                            <th>账户邮箱</th>
                            <th>邮箱密码</th>
                            <th>AWS密码</th>
                            <th>访问密钥</th>
                            <th>秘密访问密钥</th>
                            <th>状态</th>
                            <th>测号时间</th>
                            <th>配额</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($accounts as $account)
                            <tr>
                                <td class="checkbox-column">
                                    <input type="checkbox" name="selected[]" value="{{ $account->id }}" class="form-check-input row-checkbox">
                                </td>
                                <td>{{ $account->id }}</td>
                                <td>{{ $account->user->username ?? '-' }}</td>
                                <td class="text-truncate-cell">{{ $account->account_name ?: '-' }}</td>
                                <td class="text-truncate-cell">{{ $account->email_password ?: '-' }}</td>
                                <td class="text-truncate-cell">{{ $account->aws_password ?: '-' }}</td>
                                <td class="text-truncate-cell">{{ $account->access_key ?: '-' }}</td>
                                <td class="text-truncate-cell">{{ $account->secret_key ?: '-' }}</td>
                                <td>
                                    <span class="badge badge-soft-{{ $account->status == 1 ? 'success' : ($account->status == 2 ? 'danger' : ($account->status == 3 ? 'warning' : 'secondary')) }}">
                                        {{ $account->status_text }}
                                    </span>
                                </td>
                                <td>{{ $account->last_check_at ? $account->last_check_at->format('Y-m-d H:i:s') : '未测试' }}</td>
                                <td>{{ $account->quota ?: '未知' }}</td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.accounts.edit', $account) }}" class="btn-action btn-action-primary" title="编辑账户">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form action="{{ route('admin.accounts.destroy', $account) }}"
                                              method="POST"
                                              onsubmit="return confirm('确定要删除这个AWS账户吗？');"
                                              class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn-action btn-action-danger" title="删除账户">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- 底部操作栏 -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <button type="button" class="btn btn-soft-danger" id="batchDeleteBtn" disabled>
                        <i class="bi bi-trash me-1"></i> 批量删除
                    </button>
                </div>
                <div>
                    {{ $accounts->appends(request()->query())->links('components.pagination') }}
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有筛选元素
    const searchInput = document.getElementById('searchInput');
    const userFilter = document.getElementById('userFilter');
    const statusFilter = document.getElementById('statusFilter');
    const perPageSelect = document.querySelector('select[name="per_page"]');

    // 统一的更新URL函数
    function updateURL(params = {}) {
        const url = new URL(window.location.href);
        
        // 清除分页参数
        url.searchParams.delete('page');
        
        // 更新所有参数
        Object.entries(params).forEach(([key, value]) => {
            if (value) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
        });
        
        // 跳转到新URL
        window.location.href = url.toString();
    }

    // 添加事件监听器
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateURL({ search: this.value.trim() });
        }, 500);
    });

    userFilter.addEventListener('change', function() {
        updateURL({ user_id: this.value });
    });

    statusFilter.addEventListener('change', function() {
        updateURL({ status: this.value });
    });

    perPageSelect.addEventListener('change', function() {
        updateURL({ per_page: this.value });
    });

    // 全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('input[name="selected[]"]');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');

    function updateBatchDeleteButton() {
        const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
        batchDeleteBtn.disabled = checkedBoxes.length === 0;
    }

    selectAllCheckbox?.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        updateBatchDeleteButton();
    });

    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBatchDeleteButton();
            // 更新全选状态
            const checkedCount = document.querySelectorAll('input[name="selected[]"]:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        });
    });

    // 批量删除功能
    batchDeleteBtn?.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('input[name="selected[]"]:checked');
        if (checkedBoxes.length === 0) {
            alert('请选择要删除的账户');
            return;
        }

        if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个账户吗？此操作不可恢复！`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("admin.accounts.batch-destroy") }}';

            // 添加CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // 添加选中的ID
            checkedBoxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'ids[]';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // 密码现在以明文显示，无需点击功能
});

// 单个删除功能
function deleteAccount(id) {
    if (confirm('确定要删除这个AWS账户吗？此操作不可恢复！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/keyadmin/accounts/${id}`;

        // 添加CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // 添加DELETE方法
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection