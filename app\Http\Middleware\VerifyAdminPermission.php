<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class VerifyAdminPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission = null): Response
    {
        // 确保用户已通过管理员认证
        if (!Auth::guard('admin')->check()) {
            abort(403, '需要管理员权限');
        }

        $admin = Auth::guard('admin')->user();
        
        // 如果指定了特定权限，可以在这里添加更细粒度的权限检查
        // 目前所有管理员都有完整权限，后续可以扩展角色系统
        if ($permission) {
            // 预留权限检查逻辑
            // 例如：if (!$admin->hasPermission($permission)) { abort(403); }
        }

        return $next($request);
    }
}
